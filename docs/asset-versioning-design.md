# Asset Versioning System Design

## Overview

The Asset Versioning System implements a **copy-on-write** pattern that preserves historical integrity while enabling content modifications across multiple content cycles. Assets are shared across cycles until modification is attempted, at which point a cycle-specific version is created.

## Core Concept: Copy-on-Write Versioning

Assets exist in a shared state across multiple cycles until they need to be modified. When an edit is attempted on a shared asset, the system automatically creates a new version specific to the target cycle, preserving the original asset's state in its source cycle.

## Database Schema

### Asset Versioning Fields

```sql
-- assets table
id                  BIGINT PRIMARY KEY
origin_asset_id     BIGINT NULL REFERENCES assets(id)  -- Points to the original asset
version_number      INT DEFAULT 1                      -- Incremental version counter
origin_cycle_id     BIGINT NULL REFERENCES cycles(id)  -- Cycle where this version was created
title               VARCHAR(255)
start_date          DATETIME
end_date            DATETIME
asset_type          VARCHAR(255)
parent_asset_id     BIGINT NULL REFERENCES assets(id)  -- For hierarchical assets
uuid                UUID
created_at          TIMESTAMP
updated_at          TIMESTAMP
deleted_at          TIMESTAMP NULL
```

### Asset-Cycle Relationship

```sql
-- asset_cycle pivot table
asset_id    BIGINT REFERENCES assets(id)
cycle_id    BIGINT REFERENCES cycles(id)
status      ENUM('new', 'holdover', 'expiring', 'recalled')
```

## Asset Status Types

| Status | Description |
|--------|-------------|
| **New** | Newly created asset in current cycle - no versioning needed |
| **Holdover** | Asset carried over from previous cycle - triggers versioning on edit |
| **Expiring** | Asset ending within current cycle - triggers versioning on edit |
| **Recalled** | Asset brought back from previous cycle - triggers versioning on recall |

## Versioning Triggers

### 1. Field Value Changes
- **Trigger**: Modifying any field value on a non-New asset
- **Behavior**: Creates new version with updated field value
- **Exception**: New fields added after cycle start don't trigger versioning

### 2. Asset Date Modifications
- **Trigger**: Changing `start_date` or `end_date` on a non-New asset
- **Behavior**: Creates new version with updated dates

### 3. Schema Changes
- **Trigger**: Removing fields that have values on existing assets
- **Behavior**: Creates new versions for all affected assets across all cycles

### 4. Asset Recall
- **Trigger**: Recalling an asset from a previous cycle
- **Behavior**: Immediately creates new version in target cycle

## Versioning Rules

### One Version Per Cycle
- Only **one version** of an asset can exist per cycle
- First edit creates the version; subsequent edits modify the existing version
- No cascading versions within the same cycle

### Version Isolation
- Each version is isolated to its specific cycle
- Original asset remains frozen in its source cycle
- Versions don't affect each other across cycles

### Preservation of History
- Original assets are never modified after versioning
- Historical data remains intact for auditing and rollback

## Implementation Details

### Core Manager: `CycleAssetManager`

```php
class CycleAssetManager
{
    /**
     * Ensures an editable version exists for the asset in the target cycle
     * Returns existing version if already created, or creates new one
     */
    public function ensureEditableAssetVersion(
        int $assetID, 
        int $cycleID, 
        FieldValue|AssetVideo|AssetAudio|null $fieldValue = null
    ): Asset
}
```

### Version Creation Process

1. **Check for Existing Version**
   ```php
   // Look for existing version in target cycle
   $asset = Asset::where(fn($q) => $q->where('id', $assetID)->orWhere('origin_asset_id', $assetID))
       ->where('origin_cycle_id', $cycleID)
       ->first();
   ```

2. **Validate Versioning Need**
   ```php
   // Skip versioning for New assets
   if ($currentAssetStatus === AssetStatus::New->value) {
       return $asset;
   }
   
   // Skip versioning for new fields added after cycle start
   if ($fieldValue && $fieldValue->field->created_at > $cycle->start_date) {
       return $asset;
   }
   ```

3. **Create New Version**
   ```php
   $duplicateAsset = $asset->replicate()->fill([
       'version_number' => $asset->version_number + 1,
       'origin_asset_id' => $asset->origin_asset_id ?? $assetID,
       'origin_cycle_id' => $cycleID,
   ]);
   ```

4. **Carry Over Related Data**
   - Field values (excluding the modified one)
   - Category relationships
   - Media files (videos, audios, images)
   - Asset hierarchy relationships

5. **Update Cycle Relationships**
   ```php
   $cycle->assets()->attach($duplicateAsset->id, ['status' => $currentAssetStatus]);
   $cycle->assets()->detach($assetID);
   ```

## Data Carryover Process

### Field Values
- All existing field values are copied to the new version
- Modified field value is excluded from carryover
- New UUIDs generated for copied values

### Media Files
- **Videos**: Copied with new UUIDs, maintaining file references
- **Audio**: Copied with new UUIDs, maintaining file references  
- **Images**: Copied with new UUIDs, maintaining file references

### Category Relationships
- Category assignments are preserved in the new version
- Only categories belonging to the target cycle are carried over

### Validation
- New version undergoes fresh validation
- Original asset's validation issues are cleaned up

## Navigation & History

### Historical View
- Previous cycles show assets as they existed at that time
- No modifications visible from future cycles
- Maintains audit trail of changes

### Current View
- Active cycle shows the latest version
- All modifications are visible
- Editable state for authorized users

### Version Tracking
```php
// Find all versions of an asset
$versions = Asset::where('origin_asset_id', $originalAssetId)
    ->orWhere('id', $originalAssetId)
    ->orderBy('version_number')
    ->get();
```

## Asset Recall System

### Recall Eligibility
- Assets within the airline's recall period
- Assets not already present in target cycle
- Assets not expired beyond recall window

### Recall Process
1. **Immediate Versioning**: Creates new version upon recall
2. **Date Adjustment**: Updates start/end dates to match target cycle
3. **Status Assignment**: Sets status to 'recalled'

```php
public function recallAsset($asset, $cycle)
{
    $recalledAsset = $this->ensureEditableAssetVersion($asset->id, $cycle->id);
    $recalledAsset->start_date = $cycle->start_date;
    $recalledAsset->end_date = $cycle->end_date;
    $recalledAsset->save();
    
    return $recalledAsset;
}
```

## Performance Considerations

### Batch Operations
- Uses batch inserts for large data carryover operations
- Configurable batch size (default: 500 items)
- Chunked processing for memory efficiency

### Query Optimization
- Indexed queries on `origin_asset_id` and `origin_cycle_id`
- Efficient lookups for existing versions
- Optimized relationship loading

## Testing Coverage

The system includes comprehensive test coverage:

- **Version Creation**: Validates proper version creation on edits
- **One Version Rule**: Ensures only one version per cycle
- **Data Carryover**: Verifies all related data is properly copied
- **Status Handling**: Tests different asset status scenarios
- **Schema Changes**: Validates versioning on field removal
- **Recall Process**: Tests asset recall functionality

## Benefits

1. **Historical Integrity**: Complete preservation of past states
2. **Flexible Editing**: Safe modifications without data loss
3. **Audit Trail**: Full history of changes across cycles
4. **Performance**: Shared assets until modification needed
5. **Isolation**: Changes don't affect other cycles
6. **Rollback Capability**: Can reference previous versions

## Future Considerations

- **Version Cleanup**: Automated cleanup of old versions
- **Compression**: Archive old versions to reduce storage
- **Diff Tracking**: Track specific changes between versions
- **Merge Capabilities**: Merge changes across versions
