<?php

namespace Database\Factories;

use App\Models\Enums\FieldEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Field>
 */
class FieldFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => uniqid('test field'),
            'field_type' => FieldEnum::String->value, 
        ];
    }
}
