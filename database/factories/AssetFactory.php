<?php

namespace Database\Factories;

use App\Models\Enums\AssetTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Asset>
 */
class AssetFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $parentTypes = array_values(array_filter(
            AssetTypeEnum::cases(),
            fn(AssetTypeEnum $type) => $type->isTopLevel()
        ));

        return [
            'title' => ucfirst($this->faker->words(2, true)),
            'asset_type' => $this->faker->randomElement($parentTypes)->value,
            'start_date' => now(),
            'end_date' => $this->faker->dateTimeBetween('+1 month', '+3 month'),
        ];
    }
}
