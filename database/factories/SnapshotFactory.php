<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Snapshot>
 */
class SnapshotFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
            'cycle_id' => 1,
            'user_id' => 1,
            'is_running' => false,
            'is_completed' => false,
            'is_failed' => false,
            'progress' => 0,
        ];
    }
}
