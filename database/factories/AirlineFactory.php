<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\CycleFrequency;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Airline>
 */
class AirlineFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $word = $this->faker->firstName();

        return [
            'name' => 'Air ' . ucfirst($word),
            'organization_id' => 1,
            'logo_url'  => '',
            'icao_code' => strtoupper(substr($word, 0, 3)),
            'cycle_frequency' =>  CycleFrequency::Annually,
            'asset_types' => [AssetTypeEnum::Movie, AssetTypeEnum::Series, AssetTypeEnum::Music, AssetTypeEnum::Audiobook, AssetTypeEnum::Podcast],
            'max_total_snapshot_size' => 1024 ** 4, // 1 TB
            'max_delta_snapshot_size' => 100 * (1024 ** 3), // 100 GB
            'max_for_eis_size' => 30 * (1024 ** 3), // 30 GB
        ];
    }
}
