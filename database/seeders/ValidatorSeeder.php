<?php

namespace Database\Seeders;

use App\Models\Enums\ValidatorGroupEnum;
use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Validator;
use App\Models\ValidatorFieldType;
use Illuminate\Database\Seeder;

class ValidatorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all valid validator names from the enum
        $validNames = array_column(ValidatorRulesEnum::cases(), 'value');
        // Delete any validators from the database that are no longer in the enum
        $notExistingValidators = Validator::whereNotIn('name', $validNames)->pluck('id');
        Validator::whereIn('id', $notExistingValidators)->delete();
        ValidatorFieldType::whereIn('validator_id', $notExistingValidators)->delete();

        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::Required->value, // 'required',
            ],
            [
                'description' => 'The field is required.',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::String->value, // 'string',
            ],
            [
                'description' => 'The field must be a string.',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::Min->value, // 'min',
            ],
            [
                'description' => 'The field value must be at least :n characters long.',
                'parameters' => json_encode([
                    'value' => 3,
                ])
            ]
        );

        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::CaseStyle->value,
            ],
            [
                'description' => 'Text should follow Title or Sentence case',
                'parameters' => json_encode([
                    'options' => [
                        ['value' => 'title', 'label' => 'Title Case'],
                        ['value' => 'sentence', 'label' => 'Sentence Case'],
                    ],
                    'value' => 'sentence',
                ]),
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::Formatting->value,
            ],
            [
                'description' => 'Checks for proper text formatting',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::DateOrder->value,
            ],
            [
                'description' => 'End date must be after start date',
                'group' => ValidatorGroupEnum::DateOrder->value,
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::ValueList->value,
            ],
            [
                'description' => 'Value must be from a predefined list',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::MaxStringLength->value,
            ],
            [
                'description' => 'String must meet length requirements',
                'parameters' => json_encode([
                    'value' => 255,
                ]),
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::Enumeration->value,
            ],
            [
                'description' => 'Value must be from an enumeration',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::FileNamePattern->value,
            ],
            [
                'description' => 'Must match a specific filename pattern',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::DuplicateCheck->value,
            ],
            [
                'description' => 'Checks for duplicate entries',
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::MaxCount->value,
            ],
            [
                'description' => 'Collection must not exceed maximum allowed count',
                'parameters' => json_encode([
                    'value' => 6
                ])
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::AtLeastOneCategory->value,
            ],
            [
                'description' => 'At least one category must be selected',
                'group' => ValidatorGroupEnum::Category->value
            ]
        );
        Validator::updateOrCreate(
            // category validators
            [
                'name' => ValidatorRulesEnum::MaxAssignedCategories->value,
            ],
            [
                'description' => 'Maximum assigned categories to the asset',
                'group' => ValidatorGroupEnum::Category->value,
                'parameters' => json_encode([
                    'value' => 3
                ])
            ]
        );
        Validator::updateOrCreate(
            [
                'name' => ValidatorRulesEnum::CategoryContentTypeMatch->value,
            ],
            [
                'description' => 'Content type must be matching types allowed in the selected category.',
                'group' => ValidatorGroupEnum::Category->value,
            ]
        );
    }
}
