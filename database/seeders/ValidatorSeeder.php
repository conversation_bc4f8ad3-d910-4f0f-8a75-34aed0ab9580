<?php

namespace Database\Seeders;

use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Validator; 
use Illuminate\Database\Seeder;

class ValidatorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Validator::factory()->createMany([
            [
                'name' => ValidatorRulesEnum::Required->value, // 'required',
                'description' => 'The field is required.',
            ], 
            [
                'name' => ValidatorRulesEnum::String->value, // 'string',
                'description' => 'The field must be a string.',
            ],
            [
                'name' => ValidatorRulesEnum::Min->value, // 'min',
                'description' => 'The field value must be at least :n characters long.',
                'parameters' => json_encode([
                    'value' => 3,
                ])
            ],
        ]);
    }
}
