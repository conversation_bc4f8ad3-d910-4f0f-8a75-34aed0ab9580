<?php

namespace Database\Seeders;


use App\Models\Language;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LanguageTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            ['ife_code' => 'ARA', 'bcp_47_code' => 'ar', 'iso_639_2_t_code' => 'ara', 'eng_description' => 'Arabic', 'local_description' => 'عربى'],
            ['ife_code' => 'ARW', 'bcp_47_code' => 'arw', 'iso_639_2_t_code' => 'arw', 'eng_description' => 'Arawak', 'local_description' => 'Arawák'],
            ['ife_code' => 'BAM', 'bcp_47_code' => 'bm', 'iso_639_2_t_code' => 'bam', 'eng_description' => 'Bambara', 'local_description' => 'ߓߊߡߊߣߊߣߞߊߣ'],
            ['ife_code' => 'BEN', 'bcp_47_code' => 'bn', 'iso_639_2_t_code' => 'ben', 'eng_description' => 'Bengali', 'local_description' => 'বাংলা'],
            ['ife_code' => 'BPR', 'bcp_47_code' => 'pt-BR', 'iso_639_2_t_code' => 'por', 'eng_description' => 'Brazilian Portuguese', 'local_description' => 'Português do Brasil'],
            ['ife_code' => 'CAT', 'bcp_47_code' => 'ca', 'iso_639_2_t_code' => 'cat', 'eng_description' => 'Catalan', 'local_description' => 'català'],
            ['ife_code' => 'CFR', 'bcp_47_code' => 'fr-CA', 'iso_639_2_t_code' => 'fra', 'eng_description' => 'Canadian French', 'local_description' => 'Français Canadien'],
            ['ife_code' => 'CHI', 'bcp_47_code' => 'zh', 'iso_639_2_t_code' => 'zho', 'eng_description' => 'Chinese', 'local_description' => '中文'],
            ['ife_code' => 'CSP', 'bcp_47_code' => 'es', 'iso_639_2_t_code' => 'spa', 'eng_description' => 'Castillian Spanish', 'local_description' => 'Español'],
            ['ife_code' => 'CZE', 'bcp_47_code' => 'cs', 'iso_639_2_t_code' => 'ces', 'eng_description' => 'Czech', 'local_description' => 'český'],
            ['ife_code' => 'DAN', 'bcp_47_code' => 'da', 'iso_639_2_t_code' => 'dan', 'eng_description' => 'Danish', 'local_description' => 'Dansk'],
            ['ife_code' => 'DUT', 'bcp_47_code' => 'nl', 'iso_639_2_t_code' => 'nld', 'eng_description' => 'Dutch', 'local_description' => 'Nederlands'],
            ['ife_code' => 'ENG', 'bcp_47_code' => 'en', 'iso_639_2_t_code' => 'eng', 'eng_description' => 'English', 'local_description' => 'English'],
            ['ife_code' => 'EWE', 'bcp_47_code' => 'ee', 'iso_639_2_t_code' => 'ewe', 'eng_description' => 'Ewe', 'local_description' => 'Èʋegbe'],
            ['ife_code' => 'FAS', 'bcp_47_code' => 'fa', 'iso_639_2_t_code' => 'fas', 'eng_description' => 'Persian', 'local_description' => 'فارسی'],
            ['ife_code' => 'FIN', 'bcp_47_code' => 'fi', 'iso_639_2_t_code' => 'fin', 'eng_description' => 'Finnish', 'local_description' => 'Suomi'],
            ['ife_code' => 'FRE', 'bcp_47_code' => 'fr', 'iso_639_2_t_code' => 'fra', 'eng_description' => 'French', 'local_description' => 'Français'],
            ['ife_code' => 'GAE', 'bcp_47_code' => 'ga-IE', 'iso_639_2_t_code' => 'gle', 'eng_description' => 'Irish', 'local_description' => 'Gaeilge'],
            ['ife_code' => 'GER', 'bcp_47_code' => 'de', 'iso_639_2_t_code' => 'deu', 'eng_description' => 'German', 'local_description' => 'Deutsch'],
            ['ife_code' => 'GRE', 'bcp_47_code' => 'el', 'iso_639_2_t_code' => 'ell', 'eng_description' => 'Greek', 'local_description' => 'Ελληνικά'],
            ['ife_code' => 'GUJ', 'bcp_47_code' => 'gu', 'iso_639_2_t_code' => 'guj', 'eng_description' => 'Gujarati', 'local_description' => 'ગુજરાતી'],
            ['ife_code' => 'HAC', 'bcp_47_code' => 'ht', 'iso_639_2_t_code' => 'hat', 'eng_description' => 'Haitian Creole', 'local_description' => 'kreyòl ayisyen'],
            ['ife_code' => 'HEB', 'bcp_47_code' => 'he', 'iso_639_2_t_code' => 'heb', 'eng_description' => 'Hebrew', 'local_description' => 'עברית'],
            ['ife_code' => 'HIN', 'bcp_47_code' => 'hi', 'iso_639_2_t_code' => 'hin', 'eng_description' => 'Hindi', 'local_description' => 'हिंदी'],
            ['ife_code' => 'HUN', 'bcp_47_code' => 'hu', 'iso_639_2_t_code' => 'hun', 'eng_description' => 'Hungarian', 'local_description' => 'Magyar'],
            ['ife_code' => 'IND', 'bcp_47_code' => 'id', 'iso_639_2_t_code' => 'ind', 'eng_description' => 'Indonesian', 'local_description' => 'Bahasa Indonesia'],
            ['ife_code' => 'ISL', 'bcp_47_code' => 'is', 'iso_639_2_t_code' => 'isl', 'eng_description' => 'Icelandic', 'local_description' => 'Íslensku'],
            ['ife_code' => 'ITA', 'bcp_47_code' => 'it-IT', 'iso_639_2_t_code' => 'ita', 'eng_description' => 'Italian', 'local_description' => 'Italiano'],
            ['ife_code' => 'JPN', 'bcp_47_code' => 'ja', 'iso_639_2_t_code' => 'jpn', 'eng_description' => 'Japanese', 'local_description' => '日本語'],
            ['ife_code' => 'KAN', 'bcp_47_code' => 'kn', 'iso_639_2_t_code' => 'kan', 'eng_description' => 'Kannada', 'local_description' => 'kannađa'],
            ['ife_code' => 'KAZ', 'bcp_47_code' => 'kk', 'iso_639_2_t_code' => 'kaz', 'eng_description' => 'Kazakh', 'local_description' => 'Қазақ'],
            ['ife_code' => 'KOR', 'bcp_47_code' => 'ko', 'iso_639_2_t_code' => 'kor', 'eng_description' => 'Korean', 'local_description' => '한국어'],
            ['ife_code' => 'LSP', 'bcp_47_code' => 'es-419', 'iso_639_2_t_code' => 'spa', 'eng_description' => 'Latin Spanish', 'local_description' => 'Español'],
            ['ife_code' => 'MAC', 'bcp_47_code' => 'mk', 'iso_639_2_t_code' => 'mkd', 'eng_description' => 'Macedonian', 'local_description' => 'македонски'],
            ['ife_code' => 'MAL', 'bcp_47_code' => 'ml', 'iso_639_2_t_code' => 'mal', 'eng_description' => 'Malayalam', 'local_description' => 'മലയാളം'],
            ['ife_code' => 'MAO', 'bcp_47_code' => 'mi', 'iso_639_2_t_code' => 'mri', 'eng_description' => 'Māori', 'local_description' => 'te reo Māori'],
            ['ife_code' => 'MSA', 'bcp_47_code' => 'ms', 'iso_639_2_t_code' => 'msa', 'eng_description' => 'Malay', 'local_description' => 'Melayu'],
            ['ife_code' => 'NOR', 'bcp_47_code' => 'no', 'iso_639_2_t_code' => 'nor', 'eng_description' => 'Norwegian', 'local_description' => 'Norsk'],
            ['ife_code' => 'PFR', 'bcp_47_code' => '', 'iso_639_2_t_code' => 'fra', 'eng_description' => 'Parisian French', 'local_description' => 'Français'],
            ['ife_code' => 'PUN', 'bcp_47_code' => 'pa', 'iso_639_2_t_code' => 'pan', 'eng_description' => 'Punjabi', 'local_description' => 'ਪੰਜਾਬੀ'],
            ['ife_code' => 'POL', 'bcp_47_code' => 'pl', 'iso_639_2_t_code' => 'pol', 'eng_description' => 'Polish', 'local_description' => 'Polskie'],
            ['ife_code' => 'POR', 'bcp_47_code' => 'pt', 'iso_639_2_t_code' => 'por', 'eng_description' => 'European Portuguese', 'local_description' => 'Português'],
            ['ife_code' => 'RON', 'bcp_47_code' => 'ro', 'iso_639_2_t_code' => 'ron', 'eng_description' => 'Romanian', 'local_description' => 'Română'],
            ['ife_code' => 'RUS', 'bcp_47_code' => 'ru', 'iso_639_2_t_code' => 'rus', 'eng_description' => 'Russian', 'local_description' => 'Pусский'],
            ['ife_code' => 'SOM', 'bcp_47_code' => 'so', 'iso_639_2_t_code' => 'som', 'eng_description' => 'Somali', 'local_description' => '𐒖𐒍 𐒈𐒝𐒑𐒛𐒐𐒘'],
            ['ife_code' => 'SER', 'bcp_47_code' => 'sr', 'iso_639_2_t_code' => 'srp', 'eng_description' => 'Serbian', 'local_description' => 'Српски'],
            ['ife_code' => 'SWA', 'bcp_47_code' => 'sw', 'iso_639_2_t_code' => 'swa', 'eng_description' => 'Swahili', 'local_description' => 'Kiswahili'],
            ['ife_code' => 'SWE', 'bcp_47_code' => 'sv', 'iso_639_2_t_code' => 'swe', 'eng_description' => 'Swedish', 'local_description' => 'Svenska'],
            ['ife_code' => 'TAM', 'bcp_47_code' => 'ta', 'iso_639_2_t_code' => 'tam', 'eng_description' => 'Tamil', 'local_description' => 'தமிழ்'],
            ['ife_code' => 'TAG', 'bcp_47_code' => 'tl', 'iso_639_2_t_code' => 'tgl', 'eng_description' => 'Tagalog', 'local_description' => 'Tagalog'],
            ['ife_code' => 'TEL', 'bcp_47_code' => 'te', 'iso_639_2_t_code' => 'tel', 'eng_description' => 'Telugu', 'local_description' => 'తెలుగు'],
            ['ife_code' => 'THA', 'bcp_47_code' => 'th', 'iso_639_2_t_code' => 'tha', 'eng_description' => 'Thai', 'local_description' => 'ไทย'],
            ['ife_code' => 'TUR', 'bcp_47_code' => 'tr', 'iso_639_2_t_code' => 'tur', 'eng_description' => 'Turkish', 'local_description' => 'Türk'],
            ['ife_code' => 'UKR', 'bcp_47_code' => 'uk', 'iso_639_2_t_code' => 'ukr', 'eng_description' => 'Ukrainian', 'local_description' => 'Українська'],
            ['ife_code' => 'URD', 'bcp_47_code' => 'ur', 'iso_639_2_t_code' => 'urd', 'eng_description' => 'Urdu', 'local_description' => 'اُردُو'],
            ['ife_code' => 'VIE', 'bcp_47_code' => 'vi', 'iso_639_2_t_code' => 'vie', 'eng_description' => 'Vietnamese', 'local_description' => 'Tiếng Việt'],
            ['ife_code' => 'YID', 'bcp_47_code' => 'yi', 'iso_639_2_t_code' => 'yid', 'eng_description' => 'Yiddish', 'local_description' => 'אידיש']
        ];

        foreach ($languages as $language) {
            Language::updateOrCreate(
                ['ife_code' => $language['ife_code']],
                $language
            );
        }
    }
}
