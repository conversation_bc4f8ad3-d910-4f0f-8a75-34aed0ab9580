<?php

namespace Database\Seeders;

use App\Models\Enums\ValidatorGroupEnum;
use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Validator;
use Illuminate\Database\Seeder;

class AdditionalValidatorsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Validator::factory()->createMany([
            [
                'name' => ValidatorRulesEnum::CaseStyle->value,
                'description' => 'Text should follow Title or Sentence case',
                'parameters' => json_encode([
                    'options' => [
                        ['value' => 'title', 'label' => 'Title Case'],
                        ['value' => 'sentence', 'label' => 'Sentence Case'],
                    ],
                    'value' => 'sentence',
                ]),
            ],
            [
                'name' => ValidatorRulesEnum::Formatting->value,
                'description' => 'Checks for proper text formatting',
            ],
            [
                'name' => ValidatorRulesEnum::DateOrder->value,
                'description' => 'End date must be after start date',
                'group' => ValidatorGroupEnum::DateOrder->value,
            ],
            [
                'name' => ValidatorRulesEnum::ValueList->value,
                'description' => 'Value must be from a predefined list',
            ],
            [
                'name' => ValidatorRulesEnum::MaxStringLength->value,
                'description' => 'String must meet length requirements',
                'parameters' => json_encode([
                    'value' => 255,
                ]),
            ],
            [
                'name' => ValidatorRulesEnum::Enumeration->value,
                'description' => 'Value must be from an enumeration',
            ],
            [
                'name' => ValidatorRulesEnum::FileNamePattern->value,
                'description' => 'Must match a specific filename pattern',
            ],
            [
                'name' => ValidatorRulesEnum::DuplicateCheck->value,
                'description' => 'Checks for duplicate entries',
            ],
            [
                'name' => ValidatorRulesEnum::MaxCount->value,
                'description' => 'Collection must not exceed maximum allowed count',
                'parameters' => json_encode([
                    'value' => 6
                ])
            ],
            [
                'name' => ValidatorRulesEnum::AtLeastOneCategory->value,
                'description' => 'At least one category must be selected',
                'group' => ValidatorGroupEnum::Category->value
            ],
            // category validators
            [
                'name' => ValidatorRulesEnum::MaxAssignedCategories->value,
                'description' => 'Maximum assigned categories to the asset',
                'group' => ValidatorGroupEnum::Category->value,
                'parameters' => json_encode([
                    'value' => 3
                ])
            ],
            [
                'name' => ValidatorRulesEnum::CategoryContentTypeMatch->value,
                'description' => 'Content type must be matching types allowed in the selected category',
                'group' => ValidatorGroupEnum::Category->value,
            ]
        ]);
    }
}
