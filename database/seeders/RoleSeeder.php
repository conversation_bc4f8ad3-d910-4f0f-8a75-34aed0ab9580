<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Enums\RoleEnum;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // for each role from the enum, create a new role 
        foreach (RoleEnum::cases() as $role) {
            try {
                Role::create([
                    'name' => $role->value,
                ]);
            } catch (\Exception $e) {
                // if the role already exists, skip it
                continue;
            }
        }
       
    }
}
