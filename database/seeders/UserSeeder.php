<?php

namespace Database\Seeders;

use App\Models\{User, Role};
use App\Models\Enums\RoleEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            // Here we create a root admin organization and a root admin user.
            // Make the default Aero Admin organization account
            $adminOrganization = \App\Models\Organization::factory()->create([
                'name' => 'Root Organization'
            ]);

            // Set an admin user set as owner on the account
            $adminUser = User::factory()->create([
                'name' => 'Root Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'organization_id' => $adminOrganization->id,
                'role_id' =>  Role::where('name', RoleEnum::SuperAdmin->value)->first()->id, // super admin role
            ]);

            // Set the admin user as the owner_id on the organization
            $adminOrganization->owner_id = $adminUser->id;
            $adminOrganization->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        try {
            // viasat content ops user
            User::factory()->create([
                'name' => 'Viasat Content Ops',
                'email' => '<EMAIL>',
                'role_id' => Role::where('name', RoleEnum::ViasatContentOps->value)->first()->id,  // viasat content ops role
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        try {
            // csp edit
            User::factory()->create([
                'name' => 'CSP Edit User',
                'email' => '<EMAIL>',
                'role_id' => Role::where('name', RoleEnum::CspEdit->value)->first()->id, // csp edit role
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }


        try {
            // csp review
            User::factory()->create([
                'name' => 'CSP Review User',
                'email' => '<EMAIL>',
                'role_id' => Role::where('name', RoleEnum::CspReadOnly->value)->first()->id, // csp review role
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        try {
            // airline read only
            User::factory()->create([
                'name' => 'Airline Read Only',
                'email' => '<EMAIL>',
                'role_id' => Role::where('name', RoleEnum::AirlineReadOnly->value)->first()->id, // airline read only role
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
