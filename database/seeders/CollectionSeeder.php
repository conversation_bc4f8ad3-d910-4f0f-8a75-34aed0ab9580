<?php

namespace Database\Seeders;

use App\Models\Schema;
use Illuminate\Database\Seeder;

class CollectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $schema = Schema::first();

        // Genres is easy to add to the root schema.
        $collection = $schema->collections()->updateOrCreate(
            ['name' => 'Genres'],
            [
                'name' => 'Genres',
                'is_localizable' => true,
            ]
        );

        $typicalGenres = ['Action', 'Adventure', 'Comedy', 'Drama', 'Fantasy', 'Horror', 'Mystery', 'Romance', 'Sci-Fi', 'Thriller'];
        foreach ($typicalGenres as $genre) {
            $collection->collectionValues()->updateOrCreate(
                ['label' => $genre],
                ['label' => $genre],
            );
        }

        // Let's also add Rating, that would be single-select
        $collection = $schema->collections()->updateOrCreate(
            ['name' => 'Ratings',],
            [
                'name' => 'Ratings',
                'schema_id' => $schema->id,
                'output_key' => 'ageRatings',
            ]
        );

        $typicalRatings = ['G', 'PG', 'PG-13', 'R', 'NC-17'];
        foreach ($typicalRatings as $rating) {
            $collection->collectionValues()->updateOrCreate(
                ['label' => $rating],
                ['label' => $rating],
            );
        }
    }
}
