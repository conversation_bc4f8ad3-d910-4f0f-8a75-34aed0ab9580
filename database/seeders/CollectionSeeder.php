<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CollectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Genres is easy to add to the root schema.
        $collection = \App\Models\Collection::factory()->create([
            'name' => 'Genres',
            'schema_id' => \App\Models\Schema::first()->id,
        ]);
        $typicalGenres = ['Action', 'Adventure', 'Comedy', 'Drama', 'Fantasy', 'Horror', 'Mystery', 'Romance', 'Sci-Fi', 'Thriller'];
        foreach ($typicalGenres as $genre) {
            $collection->collectionValues()->create([
                'label' => $genre
            ]);
        }

        // Let's also add Rating, that would be single-select
        $collection = \App\Models\Collection::factory()->create([
            'name' => 'Ratings',
            'schema_id' => \App\Models\Schema::first()->id,
        ]);
        $typicalRatings = ['G', 'PG', 'PG-13', 'R', 'NC-17'];
        foreach ($typicalRatings as $rating) {
            $collection->collectionValues()->create([
                'label' => $rating
            ]);
        }
    }
}
