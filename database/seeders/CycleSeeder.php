<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CycleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a cycle for each airline.
        foreach (\App\Models\Airline::all() as $airline) {
            \App\Models\Cycle::factory()->create([
                'airline_id' => $airline->id,
                'schema_id' => \App\Models\Schema::first()->id,
                'start_date' => now(),
                'end_date' => now()->addMonth(1)
            ]);
        }
    }
}
