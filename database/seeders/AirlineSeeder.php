<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class AirlineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make airlines
        $a1 = \App\Models\Airline::factory()->create();
        $a2 = \App\Models\Airline::factory()->create();
        \App\Models\Airline::factory()->create();

        // attach 2 of the 3 airlines to the org (to assist in testing user roles)
        $default_org = \App\Models\Organization::first();
        $default_org->airlines()->attach($a1);
        $default_org->airlines()->attach($a2);
    }
}
