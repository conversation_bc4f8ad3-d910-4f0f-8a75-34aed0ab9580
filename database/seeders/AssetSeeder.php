<?php

namespace Database\Seeders;

use App\Models\{Asset, Cycle};
use Illuminate\Database\Seeder;
use App\Models\Enums\AssetStatus;

class AssetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 10 assets for the first cycle:
        $cycle = Cycle::first();
        $assets = Asset::factory(100)->create();
    
        $cycle->assets()->attach(
            $assets->pluck('id')->mapWithKeys(function ($id) {
                return [$id => ['status' => AssetStatus::New]];
            })
        );
    }
}
