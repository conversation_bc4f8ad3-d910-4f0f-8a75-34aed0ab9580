<?php

namespace Database\Seeders;

use App\Models\Schema;
use App\Models\Collection;
use App\Models\Enums\FieldEnum;
use App\Models\Enums\AssetTypeEnum;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class FieldSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // What kinds of fields are obvious?
        $rootSchema = Schema::first();

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Title'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Subtitle'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Synopsis'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Datetime,
            'name' => 'Year of Release'
        ]);

        // Use the collections created in CollectionSeeder
        $rootSchema->fields()->create([
            'field_type' =>  FieldEnum::Dropdown,
            'name' => 'Genres',
            'is_multi_select' => true,
            'collection_id' => Collection::where('name', 'Genres')->first()->id
        ]);

        // Auto populated fields from the API (Get true value via Viasat API)
        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Dropdown,
            'name' => 'Rating',
            'collection_id' => Collection::where('name', 'Ratings')->first()->id
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Video,
            'name' => 'Feature File Name (.mp4)',
            'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie],
            'is_external_api_value_source' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Audio,
            'name' => 'Feature File Name (Song Files Name)',
            'asset_types' => [AssetTypeEnum::Music],
            'is_external_api_value_source' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Video,
            'name' => 'Trailer File Name',
            'asset_types' => [AssetTypeEnum::Movie],
            'is_external_api_value_source' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Image,
            'name' => 'Poster'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Image,
            'name' => 'Image File Name (.jpg)'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Image,
            'name' => 'Episode Image File Name (.jpg)'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Projected Size',
            'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie],
            'is_external_api_value_source' => true,
            'is_size_field' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Lab',
            'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie, AssetTypeEnum::Music],
            'is_external_api_value_source' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Length',
            'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie, AssetTypeEnum::Music],
            'is_external_api_value_source' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'File Size',
            'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie],
            'is_external_api_value_source' => true,
            'is_size_field' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Projected Size/Predicted Size',
            'asset_types' => [AssetTypeEnum::Music],
            'is_external_api_value_source' => true,
            'is_size_field' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Ratio',
            'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie],
            'is_external_api_value_source' => true
        ]);

        foreach (range(1, 6) as $i) {
            $rootSchema->fields()->create([
                'field_type' => FieldEnum::Dropdown,
                'name' => 'Lang ' . $i,
                'use_languages' => true,
                'is_external_api_value_source' => true
            ]);
        }

        foreach (range(1, 2) as $i) {
            $rootSchema->fields()->create([
                'field_type' => FieldEnum::Dropdown,
                'name' => 'Sub Embed ' . $i,
                'use_languages' => true,
                'is_external_api_value_source' => true
            ]);
            $rootSchema->fields()->create([
                'field_type' => FieldEnum::Dropdown,
                'name' => 'Sub Dynamic ' . $i,
                'use_languages' => true,
                'is_external_api_value_source' => true
            ]);
        }

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::Dropdown,
            'name' => 'Closed Caps',
            'use_languages' => true,
            'is_external_api_value_source' => true
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Cast'
        ]);

        $rootSchema->fields()->create([
            'field_type' => FieldEnum::String,
            'name' => 'Director'
        ]);
    }
}
