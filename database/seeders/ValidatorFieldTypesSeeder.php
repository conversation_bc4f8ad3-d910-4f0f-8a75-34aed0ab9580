<?php

namespace Database\Seeders;

use App\Models\Enums\FieldEnum;
use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Validator;
use App\Models\ValidatorFieldType;
use Illuminate\Database\Seeder;

class ValidatorFieldTypesSeeder extends Seeder
{
    private array $data = [];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // get all existing validators
        $validators = Validator::all();

        // map validators to the field types
        foreach ($validators as $validator) {
            switch ($validator->name) {
                case ValidatorRulesEnum::Required->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Datetime->value);
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Dropdown->value);
                    break;
                case ValidatorRulesEnum::Min->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    break;
                case ValidatorRulesEnum::CaseStyle->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Dropdown->value);
                    break;
                case ValidatorRulesEnum::DateOrder->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Datetime->value);
                    break;
                case ValidatorRulesEnum::AtLeastOneCategory->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Dropdown->value);
                    break;
                case ValidatorRulesEnum::MaxStringLength->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    break;
                case ValidatorRulesEnum::Formatting->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    break;
                case ValidatorRulesEnum::MaxCount->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Dropdown->value);
                    break;
                case ValidatorRulesEnum::UniqueInCycle->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    break;
                case ValidatorRulesEnum::UniqueInTVSeries->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    break;
                case ValidatorRulesEnum::UniqueInSeason->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::String->value);
                    break;
                // END    
                case ValidatorRulesEnum::ValueList->value:
                    $this->enableValidatoFieldType($validator->id, FieldEnum::Dropdown->value);
                    break;
                default:
                    break;
            }
        }

        // prepopulate validators field types relations
        ValidatorFieldType::factory()->createMany($this->data);
    }

    private function enableValidatoFieldType($validatorID, $fieldType): void
    {
        $this->data[] = [
            'validator_id' => $validatorID,
            'field_type' => $fieldType,
            'enabled' => true,
        ];
    }
}
