<?php

namespace Database\Seeders;

use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Validator;
use Illuminate\Database\Seeder;

class ComplexValidatorsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Validator::factory()->createMany([
            [
                'name' => ValidatorRulesEnum::UniqueInCycle->value,
                'description' => 'Value must be unique within cycle.',
            ],
            [
                'name' => ValidatorRulesEnum::UniqueInTVSeries->value,
                'description' => 'Value must be unique within TV Series.',
            ],
            [
                'name' => ValidatorRulesEnum::UniqueInSeason->value,
                'description' => 'Must be unique within parent TV Series.',
            ]
        ]);
    }
}
