<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('collection_values', function (Blueprint $table) {
            $table->string('label')
                ->nullable()
                ->after('value')
                ->comment('Label for the collection value');
            $table->dropColumn('value');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('collection_values', function (Blueprint $table) {
            $table->string('value')
                ->nullable()
                ->after('label')
                ->comment('Value for the collection value');
            $table->dropColumn('label');
        });
    }
};
