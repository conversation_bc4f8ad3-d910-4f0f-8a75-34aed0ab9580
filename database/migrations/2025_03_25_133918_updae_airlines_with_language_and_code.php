<?php

use App\Models\Enums\CycleFrequency;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('airlines', function (Blueprint $table) {
            $table->string('logo_url')->nullable();
            $table->string('icao_code', 4)->default('');
            $table->string('cycle_frequency')->default(CycleFrequency::Quarterly);
        });
        
        Schema::create('airline_language', function (Blueprint $table) {
            $table->foreignId('airline_id')->constrained()->onDelete('cascade');
            $table->foreignId('language_id')->constrained()->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('airlines', function (Blueprint $table) {
            $table->dropColumn('logo_url');
            $table->dropColumn('icao_code');
            $table->dropColumn('cycle_frequency');
        });
        Schema::dropIfExists('airline_language');
    }
};
