<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('validator_airline_params', function (Blueprint $table) {
            $table->id();
            $table->foreignId('validator_id')->constrained()->onDelete('cascade');
            $table->foreignId('airline_id')->constrained()->onDelete('cascade');
            $table->json('parameters');
            $table->timestamps();
        });

        Schema::table('issues', function (Blueprint $table) {
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('validator_airline_params');
        Schema::table('issues', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn('category_id');
        });
    }
};
