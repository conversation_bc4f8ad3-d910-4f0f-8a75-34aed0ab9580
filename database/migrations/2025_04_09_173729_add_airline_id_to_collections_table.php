<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('collections', function (Blueprint $table) {
            $table->foreignId('airline_id')
                ->nullable()
                ->constrained('airlines')
                ->onDelete('cascade')
                ->after('organization_id')
                ->comment('Foreign key to the airlines table');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('collections', function (Blueprint $table) {
            $table->dropForeign(['airline_id']);
            $table->dropColumn('airline_id');
        });
    }
};
