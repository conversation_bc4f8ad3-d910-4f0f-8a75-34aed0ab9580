<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->string('input_key')->nullable();

            $table->unique(['schema_id', 'input_key']);
        });

        $updates = [
            'Projected Size' => 'fake_projectedFileSize',
            'Lab' => 'fake_supplier',
            'Length' => 'fake_durationMs',
            'File Size' => 'fileSize',
            'Projected Size/Predicted Size' => 'fake_fileSize',
            'Ratio' => 'fake_videos.0.contentAspectRatio',
            'Lang 1' => 'fake_audio.0.language',
            'Lang 2' => 'fake_audio.1.language',
            'Lang 3' => 'fake_audio.2.language',
        ];

        foreach ($updates as $name => $inputKey) {
            DB::statement('UPDATE fields SET input_key = ? WHERE name = ?', [$inputKey, $name]);
        }

        DB::statement('UPDATE fields SET is_external_api_value_source = false WHERE name in ("Feature File Name (.mp4)", "Feature File Name (Song Files Name)")');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->dropColumn('input_key');
        });

        DB::statement('UPDATE fields SET is_external_api_value_source = true WHERE name in ("Feature File Name (.mp4)", "Feature File Name (Song Files Name)")');
    }
};
