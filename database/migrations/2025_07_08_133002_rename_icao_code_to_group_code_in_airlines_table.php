<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('airlines', function (Blueprint $table) {
            // First, add the new group_code column with 10 character limit
            $table->string('group_code', 10)->nullable()->after('logo_url');
        });

        // Migrate existing icao_code data to group_code
        DB::statement('UPDATE airlines SET group_code = icao_code WHERE icao_code IS NOT NULL AND icao_code != ""');

        Schema::table('airlines', function (Blueprint $table) {
            // Drop the old icao_code column
            $table->dropColumn('icao_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('airlines', function (Blueprint $table) {
            // Re-add the icao_code column with original 4 character limit
            $table->string('icao_code', 4)->default('')->after('logo_url');
        });

        // Migrate group_code data back to icao_code (truncate if necessary)
        DB::statement('UPDATE airlines SET icao_code = LEFT(COALESCE(group_code, ""), 4) WHERE group_code IS NOT NULL');

        Schema::table('airlines', function (Blueprint $table) {
            // Drop the group_code column
            $table->dropColumn('group_code');
        });
    }
};
