<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->string('field_type');
            $table->string('output_key')->nullable();
            $table->foreignId('schema_id')->constrained('schemas')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('collection_id')->nullable();
            $table->boolean('is_multi_select')->default(false);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
