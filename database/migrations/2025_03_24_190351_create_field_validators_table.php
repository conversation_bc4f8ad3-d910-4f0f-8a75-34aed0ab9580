<?php

use App\Models\Enums\ValidatorSeverityEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('validators', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('description')->nullable();
            $table->json('parameters')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('validator_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('field_id')->constrained('fields')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('validator_id')->constrained('validators')->onDelete('cascade')->onUpdate('cascade');
            $table->unique(['field_id', 'validator_id']);
            $table->json('parameters')->nullable();
            $table->string('severity')->default(ValidatorSeverityEnum::Warning->value);
            $table->boolean('enabled')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('validator_field_types', function (Blueprint $table) {
            $table->id();
            $table->foreignId('validator_id')->constrained('validators')->onDelete('cascade')->onUpdate('cascade'); 
            $table->string('field_type'); // from Models/Enums/FieldEnum values
            $table->boolean('enabled')->default(false);
            $table->timestamps();
            $table->softDeletes();
            $table->comment('Available validators for certain field types by default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('validators');
        Schema::dropIfExists('validator_fields');
        Schema::dropIfExists('validator_field_types');
    }
};
