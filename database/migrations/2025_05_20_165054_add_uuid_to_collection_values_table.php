<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('collection_values', function (Blueprint $table) {
            $table->uuid()->after('id')->nullable();
        });


        if (config('database.default') === 'mysql') {
            DB::table('collection_values')->update(['uuid' => DB::raw('(UUID())')]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('collection_values', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });
    }
};
