<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Schema as MetadataSchema;
use App\Models\Collection;
use App\Models\CollectionValue;
use App\Models\Field;
use App\Models\Enums\FieldEnum;
use App\Models\Enums\AssetTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the root schema (assuming there's only one or we want the first one)
        $rootSchema = MetadataSchema::first();

        if (!$rootSchema) {
            throw new \Exception('No schema found. Please run the schema seeder first.');
        }

        // Create the "Ad Types" collection
        $adTypesCollection = $rootSchema->collections()->create([
            'name' => 'Ad Types',
            'viasat_only' => true,
        ]);

        // Create the collection values for Ad Types
        $adTypes = [
            'Warning Slate',
            'Preroll Ad',
            'Interstitial Ad'
        ];

        foreach ($adTypes as $adType) {
            $adTypesCollection->collectionValues()->create([
                'label' => $adType,
            ]);
        }

        // Create the "Ad Type" dropdown field that applies only to ads
        $rootSchema->fields()->create([
            'name' => 'Ad Type',
            'field_type' => FieldEnum::Dropdown,
            'collection_id' => $adTypesCollection->id,
            'asset_types' => [AssetTypeEnum::Ad],
            'is_multi_select' => false,
        ]);

        // Create the "Name" field for ads
        $rootSchema->fields()->create([
            'name' => 'Name',
            'field_type' => FieldEnum::String,
            'asset_types' => [AssetTypeEnum::Ad],
            'is_localizable' => false,
        ]);

        // Create the "Ad File" field for ads
        $rootSchema->fields()->create([
            'name' => 'Ad File',
            'field_type' => FieldEnum::Video,
            'asset_types' => [AssetTypeEnum::Ad],
            'is_external_api_value_source' => false,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the root schema
        $rootSchema = MetadataSchema::first();

        if ($rootSchema) {
            // Delete the ad-specific fields
            $rootSchema->fields()->where('name', 'Ad Type')->delete();
            $rootSchema->fields()->where('name', 'Name')->where('asset_types', json_encode([AssetTypeEnum::Ad]))->delete();
            $rootSchema->fields()->where('name', 'Ad File')->delete();

            // Delete the "Ad Types" collection (this will cascade delete collection values)
            $rootSchema->collections()->where('name', 'Ad Types')->delete();
        }
    }
};
