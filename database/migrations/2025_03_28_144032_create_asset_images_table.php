<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_images', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('field_id')->constrained('fields')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('asset_id')->constrained('assets')->onDelete('cascade')->onUpdate('cascade');
            $table->string('mime')->nullable()->index();
            $table->string('extension')->nullable()->index();
            $table->unsignedBigInteger('size')->nullable()->default(0);
            $table->string('path')->notNullable(); 
            $table->unsignedInteger('width')->nullable()->default(0);
            $table->unsignedInteger('height')->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_images');
    }
};
