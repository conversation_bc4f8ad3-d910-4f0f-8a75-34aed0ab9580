<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_videos', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('cycle_id')->constrained('cycles')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('airline_id')->constrained('airlines')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('field_id')->constrained('fields')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('asset_id')->constrained('assets')->onDelete('cascade')->onUpdate('cascade');
            $table->string('mime')->nullable();
            $table->string('extension')->nullable();
            $table->unsignedBigInteger('size')->nullable()->default(0);
            $table->string('file_name')->notNullable();
            $table->unsignedInteger('duration')->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('asset_audio', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('cycle_id')->constrained('cycles')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('airline_id')->constrained('airlines')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('field_id')->constrained('fields')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('asset_id')->constrained('assets')->onDelete('cascade')->onUpdate('cascade');
            $table->string('mime')->nullable();
            $table->string('extension')->nullable();
            $table->unsignedBigInteger('size')->nullable()->default(0);
            $table->string('file_name')->notNullable();
            $table->unsignedInteger('duration')->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('issues', function (Blueprint $table) {
            $table->foreignId('asset_video_id')->nullable()->constrained('asset_videos')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('asset_audio_id')->nullable()->constrained('asset_audio')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_videos');
        Schema::dropIfExists('asset_audio');

        Schema::table('issues', function (Blueprint $table) {
            $table->dropConstrainedForeignId('asset_video_id');
            $table->dropConstrainedForeignId('asset_audio_id');
        });
    }
};
