<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('collection_values', function (Blueprint $table) {
            $table->foreignId('collection_value_id')
                ->nullable()
                ->constrained('collection_values')
                ->onDelete('cascade')
                ->after('collection_id')
                ->comment('Foreign key to the collection_values table');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('collection_values', function (Blueprint $table) {
            $table->dropForeign(['collection_value_id']);
            $table->dropColumn('collection_value_id');
        });
    }
};
