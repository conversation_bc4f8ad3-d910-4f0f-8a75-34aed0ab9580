<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('issues', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();  // use as get identifier
            $table->foreignId('field_value_id')->constrained('field_values')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('asset_id')->constrained('assets')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('validator_field_id')->constrained('validator_fields')->onDelete('cascade')->onUpdate('cascade');
            $table->string('type'); // Metadata, Images, Content
            $table->boolean('is_custom')->default(false); // Custom (from user) or system generated
            $table->text('description');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    { 
        Schema::dropIfExists('issues');
    }
};
