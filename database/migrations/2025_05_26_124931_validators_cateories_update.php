<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('validators', function (Blueprint $table) {
            $table->string('group', length: 32)->nullable();
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->unsignedBigInteger('origin_id')->nullable();
            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();
            $table->dropColumn('title');
        });

        Schema::create('category_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('categories', 'id')->onDelete('cascade')->onUpdate('cascade');
            $table->string('value', length: 255);
            $table->foreignId('language_id')->constrained('languages', 'id')->onDelete('cascade')->onUpdate('cascade');
            $table->timestamps();
            $table->unique(['category_id', 'language_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('validators', function (Blueprint $table) {
            $table->dropColumn('group');
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->dropColumn('origin_id');
            $table->dropColumn('start_date');
            $table->dropColumn('end_date');
            $table->dropUnique(['origin_id', 'language_id']);
            $table->string('title', length: 255);
        });

        Schema::drop('category_values');
    }
};
