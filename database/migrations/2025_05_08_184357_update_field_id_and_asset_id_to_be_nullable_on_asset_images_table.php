<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('asset_images', function (Blueprint $table) {
            $table->foreignId('field_id')->nullable(true)->change();
            $table->foreignId('asset_id')->nullable(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asset_images', function (Blueprint $table) {
            $table->foreignId('field_id')->nullable(false)->change();
            $table->foreignId('asset_id')->nullable(false)->change();
        });
    }
};
