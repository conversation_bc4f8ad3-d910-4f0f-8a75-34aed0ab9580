<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->json('asset_types')->nullable()->after('cycle_id');
        });

        Schema::table('issues', function (Blueprint $table) {
            $table->string('validator_group')->nullable()->after('cycle_id');
        });

        // item_id remains as a subcategory id
        Schema::table('category_items', function (Blueprint $table) {
            $table->foreignId('asset_id')->nullable()->constrained('assets')->onDelete('cascade')->after('category_id');
            $table->dropColumn('item_id');
            $table->dropColumn('item_type');
        });

        Schema::create('category_closures', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ancestor_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->foreignId('descendant_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->unsignedInteger('order')->nullable()->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropColumn('asset_types');
        });
        Schema::table('issues', function (Blueprint $table) {
            $table->dropColumn('validator_group');
        });
        Schema::table('category_items', function (Blueprint $table) {
            $table->dropColumn('asset_id');
            $table->unsignedBigInteger('item_id')->change();
        });
        Schema::drop('category_closure');
    }
};
