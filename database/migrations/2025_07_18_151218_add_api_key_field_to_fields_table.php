<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->boolean('is_external_api_key_field')->default(false);
        });

        DB::statement('UPDATE fields SET is_external_api_key_field = true WHERE name in ("Feature File Name (.mp4)", "Feature File Name (Song Files Name)")');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->dropColumn('is_external_api_key_field');
        });
    }
};
