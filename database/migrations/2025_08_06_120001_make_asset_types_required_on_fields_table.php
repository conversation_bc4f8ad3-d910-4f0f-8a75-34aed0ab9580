    <?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Field;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, ensure no fields have null asset_types (should already be done by previous migration)
        $fieldsWithNullAssetTypes = Field::whereNull('asset_types')->count();

        if ($fieldsWithNullAssetTypes > 0) {
            throw new \Exception("Found {$fieldsWithNullAssetTypes} fields with null asset_types. Please run the previous migration first to set explicit asset types for all fields.");
        }

        // Make the asset_types column non-nullable
        Schema::table('fields', function (Blueprint $table) {
            $table->json('asset_types')->nullable(false)->change();
        });

        // Add a check constraint to ensure asset_types is not empty
        // Note: This is MySQL-specific syntax
        if (config('database.default') === 'mysql') {
            try {
                DB::statement('ALTER TABLE fields ADD CONSTRAINT chk_asset_types_not_empty CHECK (JSON_LENGTH(asset_types) > 0)');
            } catch (\Exception $e) {
                // Constraint may already exist, ignore if it's a duplicate constraint error
                if (!str_contains($e->getMessage(), 'Duplicate check constraint name')) {
                    throw $e;
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the check constraint first
        if (config('database.default') === 'mysql') {
            DB::statement('ALTER TABLE fields DROP CHECK chk_asset_types_not_empty');
        }

        // Make the asset_types column nullable again
        Schema::table('fields', function (Blueprint $table) {
            $table->json('asset_types')->nullable()->change();
        });
    }
};
