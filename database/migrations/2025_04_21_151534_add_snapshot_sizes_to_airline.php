<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('airlines', function (Blueprint $table) {
            $table->unsignedBigInteger('max_total_snapshot_size')->nullable();
            $table->unsignedBigInteger('max_delta_snapshot_size')->nullable();
            $table->unsignedBigInteger('max_for_eis_size')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('airlines', function (Blueprint $table) {
            $table->dropColumn('max_total_snapshot_size');
            $table->dropColumn('max_delta_snapshot_size');
            $table->dropColumn('max_for_eis_size');
        });
    }
};
