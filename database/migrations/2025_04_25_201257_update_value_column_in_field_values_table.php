<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('field_values', function (Blueprint $table) {
            $table->string('value', 2048)->after('id')->nullable()->change();
        });
    }

    /**
     * Run the migrations.
     */
    public function down(): void
    {
        Schema::table('field_values', function (Blueprint $table) {
            $table->string('value', 255)->change(); // or the original size
        });
    }
};