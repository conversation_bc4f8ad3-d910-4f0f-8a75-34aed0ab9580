<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    { 
        Schema::table('issues', function (Blueprint $table) {
            $table->foreignId('asset_image_id')
                ->nullable()
                ->after('field_value_id')
                ->constrained('asset_images')->onDelete('cascade')->onUpdate('cascade');

            $table->unsignedBigInteger('field_value_id')->nullable()->change();
            $table->unsignedBigInteger('asset_id')->nullable()->change();
            $table->unsignedBigInteger('validator_field_id')->nullable()->change(); 
        });

        Schema::table('asset_images', function (Blueprint $table) {
            $table->string('path')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('issues', function (Blueprint $table) {
            $table->dropConstrainedForeignId('asset_image_id');
            $table->unsignedBigInteger('field_value_id')->nullable(false)->change();
            $table->unsignedBigInteger('asset_id')->nullable(false)->change();
            $table->unsignedBigInteger('validator_field_id')->nullable(false)->change();
        });

        Schema::table('asset_images', function (Blueprint $table) {
            $table->string('path')->nullable(false)->change();
        });
    }
};
