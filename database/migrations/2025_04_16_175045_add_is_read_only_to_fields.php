<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->boolean('is_external_api_value_source')->default(false)->after('is_localizable');
            $table->boolean('is_size_field')->default(false)->after('is_external_api_value_source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->dropColumn('is_external_api_value_source');
            $table->dropColumn('is_size_field');
        });
    }
};
