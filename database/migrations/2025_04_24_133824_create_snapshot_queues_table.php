<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('snapshots', function (Blueprint $table) {
            $table->id();

            $table->foreignId('cycle_id')->references('id')->on('cycles');

            // who triggered it, null if system-level (command line, or some other operation TBD)
            $table->foreignId('user_id')->nullable()->references('id')->on('users');

            $table->boolean('is_completed')->default(false);
            $table->boolean('is_running')->default(false);
            $table->boolean('is_failed')->default(false);

            // rough percentage, generated somehow...
            $table->integer('progress')->default(0);

            // will end up being dumped into S3 and signed for the UI downloads.
            $table->string('filename')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('snapshots');
    }
};
