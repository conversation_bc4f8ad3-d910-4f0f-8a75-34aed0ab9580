<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_labels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('field_id')
                ->constrained('fields')
                ->onDelete('cascade')
                ->comment('Foreign key to the fields table');
            $table->foreignId('language_id')
                ->constrained('languages')
                ->onDelete('cascade')
                ->comment('Foreign key to the languages table');
            $table->string('value');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_labels');
    }
};
