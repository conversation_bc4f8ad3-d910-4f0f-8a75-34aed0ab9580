
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCyclesTable extends Migration
{
    public function up()
    {
        Schema::create('cycles', function (Blueprint $table) {
            $table->id();
            $table->text('description')->nullable();
            $table->boolean('is_locked')->default(false);
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->foreignId('airline_id')->constrained('airlines')->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('schema_id')->constrained('schemas')->onDelete('cascade')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('cycles');
    }
}