<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Field;
use App\Models\Enums\AssetTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all asset types for explicit configuration
        $allAssetTypes = collect(AssetTypeEnum::cases())
            ->values()
            ->toArray();

        // Update all fields that currently apply to all asset types (asset_types is null)
        // to have explicit asset type configuration
        Field::whereNull('asset_types')
            ->update(['asset_types' => json_encode($allAssetTypes)]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert fields that have all asset types back to null (restore null-means-all pattern)
        $allAssetTypes = collect(AssetTypeEnum::cases())
            ->values()
            ->toArray();

        // Find fields that have exactly all asset types and set them back to null
        Field::where('asset_types', json_encode($allAssetTypes))
            ->update(['asset_types' => null]);
    }
};
