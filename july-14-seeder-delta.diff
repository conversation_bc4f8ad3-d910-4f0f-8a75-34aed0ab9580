diff --git a/database/seeders/AddRoleToUsersSeeder.php b/database/seeders/AddRoleToUsersSeeder.php
deleted file mode 100644
index 0bca7c1..0000000
--- a/database/seeders/AddRoleToUsersSeeder.php
+++ /dev/null
@@ -1,22 +0,0 @@
-<?php
-
-namespace Database\Seeders;
-
-
-use Illuminate\Database\Seeder;
-use App\Models\{Role, User};
-use App\Models\Enums\RoleEnum;
-
-class AddRoleToUsersSeeder extends Seeder
-{
-    /**
-     * Run the database seeds.
-     */
-    public function run(): void
-    {
-        $role = Role::where('name', RoleEnum::SuperAdmin->value)->first();
-        if ($role) {
-            User::query()->update(['role_id' => $role->id]);
-        }
-    }
-}
diff --git a/database/seeders/AdditionalValidatorsSeeder.php b/database/seeders/AdditionalValidatorsSeeder.php
index 167c68d..5609c49 100644
--- a/database/seeders/AdditionalValidatorsSeeder.php
+++ b/database/seeders/AdditionalValidatorsSeeder.php
@@ -2,6 +2,7 @@
 
 namespace Database\Seeders;
 
+use App\Models\Enums\ValidatorGroupEnum;
 use App\Models\Enums\ValidatorRulesEnum;
 use App\Models\Validator;
 use Illuminate\Database\Seeder;
@@ -32,6 +33,7 @@ public function run(): void
             [
                 'name' => ValidatorRulesEnum::DateOrder->value,
                 'description' => 'End date must be after start date',
+                'group' => ValidatorGroupEnum::DateOrder->value,
             ],
             [
                 'name' => ValidatorRulesEnum::ValueList->value,
@@ -66,7 +68,22 @@ public function run(): void
             [
                 'name' => ValidatorRulesEnum::AtLeastOneCategory->value,
                 'description' => 'At least one category must be selected',
+                'group' => ValidatorGroupEnum::Category->value
             ],
+            // category validators
+            [
+                'name' => ValidatorRulesEnum::MaxAssignedCategories->value,
+                'description' => 'Maximum assigned categories to the asset',
+                'group' => ValidatorGroupEnum::Category->value,
+                'parameters' => json_encode([
+                    'value' => 3
+                ])
+            ],
+            [
+                'name' => ValidatorRulesEnum::CategoryContentTypeMatch->value,
+                'description' => 'Content type must be matching types allowed in the selected category',
+                'group' => ValidatorGroupEnum::Category->value,
+            ]
         ]);
     }
 }
diff --git a/database/seeders/AdminUserSeeder.php b/database/seeders/AdminUserSeeder.php
deleted file mode 100644
index edc2bad..0000000
--- a/database/seeders/AdminUserSeeder.php
+++ /dev/null
@@ -1,35 +0,0 @@
-<?php
-
-namespace Database\Seeders;
-
-use Illuminate\Database\Console\Seeds\WithoutModelEvents;
-use Illuminate\Database\Seeder;
-use Illuminate\Support\Facades\Hash;
-
-class AdminUserSeeder extends Seeder
-{
-    /**
-     * Run the database seeds.
-     */
-    public function run(): void
-    {
-        // Here we create a root admin organization and a root admin user.
-        // Make the default Aero Admin organization account
-        $adminOrganization = \App\Models\Organization::factory()->create([
-            'name' => 'Root Organization'
-        ]);
-
-        // Set an admin user set as owner on the account
-        $adminUser = \App\Models\User::factory()->create([
-            'name' => 'Root Admin',
-            'email' => '<EMAIL>',
-            'password' => Hash::make('password'),
-            'organization_id' => $adminOrganization->id
-        ]);
-
-        // Set the admin user as the owner_id on the organization
-        $adminOrganization->owner_id = $adminUser->id;
-        $adminOrganization->save();
-
-    }
-}
diff --git a/database/seeders/AirlineSeeder.php b/database/seeders/AirlineSeeder.php
index 9d561f7..3da7244 100644
--- a/database/seeders/AirlineSeeder.php
+++ b/database/seeders/AirlineSeeder.php
@@ -2,7 +2,6 @@
 
 namespace Database\Seeders;
 
-use Illuminate\Database\Console\Seeds\WithoutModelEvents;
 use Illuminate\Database\Seeder;
 
 class AirlineSeeder extends Seeder
@@ -13,11 +12,13 @@ class AirlineSeeder extends Seeder
     public function run(): void
     {
         // Make airlines
-        \App\Models\Airline::factory()->create([
-            'organization_id' => \App\Models\Organization::first()->id
-        ]);
-        \App\Models\Airline::factory()->create([
-            'organization_id' => \App\Models\Organization::first()->id
-        ]);
+        $a1 = \App\Models\Airline::factory()->create();
+        $a2 = \App\Models\Airline::factory()->create();
+        \App\Models\Airline::factory()->create();
+
+        // attach 2 of the 3 airlines to the org (to assist in testing user roles)
+        $default_org = \App\Models\Organization::first();
+        $default_org->airlines()->attach($a1);
+        $default_org->airlines()->attach($a2);
     }
 }
diff --git a/database/seeders/CategorySeeder.php b/database/seeders/CategorySeeder.php
index 99299ab..17cc4a1 100644
--- a/database/seeders/CategorySeeder.php
+++ b/database/seeders/CategorySeeder.php
@@ -4,6 +4,8 @@
 
 use App\Models\Category;
 use App\Models\CategoryClosure;
+use App\Models\CategoryValue;
+use App\Models\Enums\AssetTypeEnum;
 use Illuminate\Database\Seeder;
 
 class CategorySeeder extends Seeder
@@ -13,45 +15,43 @@ class CategorySeeder extends Seeder
      */
     public function run(): void
     {
-        \App\Models\Category::factory()->create([
-            'cycle_id' => \App\Models\Cycle::first()->id,
-        ]);
-
         $airline_id = \App\Models\Airline::first()->id;
         $cycle_id = \App\Models\Cycle::first()->id;
 
         // create top level categories
         Category::factory()->createMany([
             [
-                'title' => 'Category List',
+                // 1
                 'type' => 'categories',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => true,
+                'asset_types' => [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value],
             ],
             [
-                'title' => 'Discover Categories',
+                // 2
                 'type' => 'categories',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => true,
             ],
             [
-                'title' => 'Kids Categories',
+                // 3
                 'type' => 'categories',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => true,
+                'asset_types' => [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value]
             ],
             [
-                'title' => 'Paramount+ Categories',
+                // 4
                 'type' => 'categories',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => true,
             ],
             [
-                'title' => 'Lifestyle Categories *',
+                // 5
                 'type' => 'categories',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
@@ -62,22 +62,24 @@ public function run(): void
         // create sub categories
         Category::factory()->createMany([
             [
-                'title' => 'All Movies',
+                // 6
                 'type' => 'assets',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => false,
+                'asset_types' => [AssetTypeEnum::Movie->value],
             ],
             [
-                'title' => 'International',
+                // 7
                 'type' => 'assets',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => false,
+                'asset_types' => [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value],
             ],
             // discover categories
             [
-                'title' => 'Native American Heritage Month',
+                // 8
                 'type' => 'assets',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
@@ -85,33 +87,82 @@ public function run(): void
             ],
             // kids categories
             [
-                'title' => 'Disney +',
+                // 9
                 'type' => 'assets',
                 'airline_id' => $airline_id,
                 'cycle_id' => $cycle_id,
                 'is_root' => false,
+                'asset_types' => [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value]
+            ],
+        ]);
+
+        CategoryValue::factory()->createMany([
+            [
+                'category_id' => 1,
+                'value' => 'Category List',
+                'language_id' => 13 // English
+            ],
+            [
+                'category_id' => 2,
+                'value' => 'Discover Categories',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 3,
+                'value' => 'Kids Categories',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 4,
+                'value' => 'Paramount+ Categories',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 5,
+                'value' => 'Lifestyle Categories *',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 6,
+                'value' => 'All Movies',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 7,
+                'value' => 'International',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 8,
+                'value' => 'Native American Heritage Month',
+                'language_id' => 13
+            ],
+            [
+                'category_id' => 9,
+                'value' => 'Disney +',
+                'language_id' => 13
             ],
         ]);
 
         CategoryClosure::factory()->createMany([
             [
-                'ancestor_id' => Category::where('title', 'Category List')->first()->id,
-                'descendant_id' => Category::where('title', 'All Movies')->first()->id,
+                'ancestor_id' => 1, // Category List
+                'descendant_id' => 6, // All Movies
                 'order' => 1
             ],
             [
-                'ancestor_id' => Category::where('title', 'Category List')->first()->id,
-                'descendant_id' => Category::where('title', 'International')->first()->id,
+                'ancestor_id' => 1, // Category List
+                'descendant_id' => 7, // International
                 'order' => 2
             ],
             [
-                'ancestor_id' => Category::where('title', 'Discover Categories')->first()->id,
-                'descendant_id' => Category::where('title', 'Native American Heritage Month')->first()->id,
+                'ancestor_id' => 2, // Discover Categories
+                'descendant_id' => 8, // Native American Heritage Month
                 'order' => 1
             ],
             [
-                'ancestor_id' => Category::where('title', 'Kids Categories')->first()->id,
-                'descendant_id' => Category::where('title', 'Disney +')->first()->id,
+                'ancestor_id' => 3, // Kids Categories
+                'descendant_id' => 9, // Disney + 
                 'order' => 1
             ]
         ]);
diff --git a/database/seeders/DatabaseSeeder.php b/database/seeders/DatabaseSeeder.php
index 33b8cfa..e5d4392 100644
--- a/database/seeders/DatabaseSeeder.php
+++ b/database/seeders/DatabaseSeeder.php
@@ -13,7 +13,8 @@ public function run(): void
     {
         $this->call([
             LanguageTableSeeder::class,
-            AdminUserSeeder::class,
+            RoleSeeder::class,
+            UserSeeder::class,
             AirlineSeeder::class,
             SchemaSeeder::class,
             CollectionSeeder::class,
@@ -25,8 +26,6 @@ public function run(): void
             AdditionalValidatorsSeeder::class,
             ComplexValidatorsSeeder::class,
             ValidatorFieldTypesSeeder::class,
-            RoleSeeder::class,
-            AddRoleToUsersSeeder::class,
         ]);
     }
 }
diff --git a/database/seeders/FieldSeeder.php b/database/seeders/FieldSeeder.php
index 60a7cb2..35321f5 100644
--- a/database/seeders/FieldSeeder.php
+++ b/database/seeders/FieldSeeder.php
@@ -57,21 +57,21 @@ public function run(): void
         ]);
 
         $rootSchema->fields()->create([
-            'field_type' => FieldEnum::String,
+            'field_type' => FieldEnum::Video,
             'name' => 'Feature File Name (.mp4)',
             'asset_types' => [AssetTypeEnum::Series, AssetTypeEnum::Movie],
             'is_external_api_value_source' => true
         ]);
 
         $rootSchema->fields()->create([
-            'field_type' => FieldEnum::String,
+            'field_type' => FieldEnum::Audio,
             'name' => 'Feature File Name (Song Files Name)',
             'asset_types' => [AssetTypeEnum::Music],
             'is_external_api_value_source' => true
         ]);
 
         $rootSchema->fields()->create([
-            'field_type' => FieldEnum::String,
+            'field_type' => FieldEnum::Video,
             'name' => 'Trailer File Name',
             'asset_types' => [AssetTypeEnum::Movie],
             'is_external_api_value_source' => true
@@ -83,12 +83,12 @@ public function run(): void
         ]);
 
         $rootSchema->fields()->create([
-            'field_type' => FieldEnum::String,
+            'field_type' => FieldEnum::Image,
             'name' => 'Image File Name (.jpg)'
         ]);
 
         $rootSchema->fields()->create([
-            'field_type' => FieldEnum::String,
+            'field_type' => FieldEnum::Image,
             'name' => 'Episode Image File Name (.jpg)'
         ]);
 
diff --git a/database/seeders/SchemaSeeder.php b/database/seeders/SchemaSeeder.php
index 19dd9cb..3cc6d0f 100644
--- a/database/seeders/SchemaSeeder.php
+++ b/database/seeders/SchemaSeeder.php
@@ -18,5 +18,12 @@ public function run(): void
             'version_number' => 1,
             'airline_id' => \App\Models\Airline::first()->id
         ]);
+
+        \App\Models\Schema::factory()->create([
+            'is_root' => true,
+            'name' => 'Some Other Schema',
+            'version_number' => 1,
+            'airline_id' => \App\Models\Airline::first()->id
+        ]);
     }
 }
diff --git a/database/seeders/UserSeeder.php b/database/seeders/UserSeeder.php
new file mode 100644
index 0000000..1122b52
--- /dev/null
+++ b/database/seeders/UserSeeder.php
@@ -0,0 +1,86 @@
+<?php
+
+namespace Database\Seeders;
+
+use App\Models\{User, Role};
+use App\Models\Enums\RoleEnum;
+use Illuminate\Database\Seeder;
+use Illuminate\Support\Facades\Hash;
+use Illuminate\Support\Facades\Log;
+
+class UserSeeder extends Seeder
+{
+    /**
+     * Run the database seeds.
+     */
+    public function run(): void
+    {
+        try {
+            // Here we create a root admin organization and a root admin user.
+            // Make the default Aero Admin organization account
+            $adminOrganization = \App\Models\Organization::factory()->create([
+                'name' => 'Root Organization'
+            ]);
+
+            // Set an admin user set as owner on the account
+            $adminUser = User::factory()->create([
+                'name' => 'Root Admin',
+                'email' => '<EMAIL>',
+                'password' => Hash::make('password'),
+                'organization_id' => $adminOrganization->id,
+                'role_id' =>  Role::where('name', RoleEnum::SuperAdmin->value)->first()->id, // super admin role
+            ]);
+
+            // Set the admin user as the owner_id on the organization
+            $adminOrganization->owner_id = $adminUser->id;
+            $adminOrganization->save();
+        } catch (\Exception $e) {
+            Log::error($e->getMessage());
+        }
+
+        try {
+            // viasat content ops user
+            User::factory()->create([
+                'name' => 'Viasat Content Ops',
+                'email' => '<EMAIL>',
+                'role_id' => Role::where('name', RoleEnum::ViasatContentOps->value)->first()->id,  // viasat content ops role
+            ]);
+        } catch (\Exception $e) {
+            Log::error($e->getMessage());
+        }
+
+        try {
+            // csp edit
+            User::factory()->create([
+                'name' => 'CSP Edit User',
+                'email' => '<EMAIL>',
+                'role_id' => Role::where('name', RoleEnum::CspEdit->value)->first()->id, // csp edit role
+            ]);
+        } catch (\Exception $e) {
+            Log::error($e->getMessage());
+        }
+
+
+        try {
+            // csp review
+            User::factory()->create([
+                'name' => 'CSP Review User',
+                'email' => '<EMAIL>',
+                'role_id' => Role::where('name', RoleEnum::CspReadOnly->value)->first()->id, // csp review role
+            ]);
+        } catch (\Exception $e) {
+            Log::error($e->getMessage());
+        }
+
+        try {
+            // airline read only
+            User::factory()->create([
+                'name' => 'Airline Read Only',
+                'email' => '<EMAIL>',
+                'role_id' => Role::where('name', RoleEnum::AirlineReadOnly->value)->first()->id, // airline read only role
+            ]);
+        } catch (\Exception $e) {
+            Log::error($e->getMessage());
+        }
+    }
+}
