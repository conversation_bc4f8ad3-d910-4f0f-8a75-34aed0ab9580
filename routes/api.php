<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\ImportController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\Api\StaticValuesController;

/**
 * These APIs are automatically prefixed '/api'.
 * They require an `Authentication: Bearer [token]`
 */
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
});

Route::get('/languages', [LanguageController::class, 'index']);
Route::get('/asset-types', [StaticValuesController::class, 'getAssetTypes']);
Route::get('/cycle-frequencies', [StaticValuesController::class, 'getCycleFrequencies']);

Route::put('airlines/upload-logo/{airline}/{fileName}', [\App\Http\Controllers\AirlineLogoController::class, 'storeAirlineLogo']);

Route::apiResource('imports', ImportController::class);
Route::put('imports/{import}/upload', [ImportController::class, 'upload'])->name('upload-imports');
Route::put('imports/{import}/process', [ImportController::class, 'process']);

