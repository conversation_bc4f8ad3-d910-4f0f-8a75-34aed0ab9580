<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AirlineController;
use App\Http\Controllers\CycleAssetController;
use App\Http\Controllers\AssetImageController;
use App\Http\Controllers\AssetVideoController;
use App\Http\Controllers\AssetAudioController;
use App\Http\Controllers\CycleController;
use App\Http\Controllers\FieldController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\Auth\TokenController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\CollectionValueController;
use App\Http\Controllers\FieldLabelController;
use App\Http\Controllers\FieldValueController;
use App\Http\Controllers\SchemaController;
use App\Http\Controllers\IssueController;
use App\Http\Controllers\ValidatorFieldController;
use App\Http\Controllers\ValidatorFieldTypeController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\ExportController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CategoryValueController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ValidatorAirlinesController;

Route::middleware(['web'])->group(function () {
    Route::get('/', function () {
        return 'API is running!';
    });

    Route::post('register', [UserController::class, 'register']);

    // TODO: Wrap with rate limiter middleware (or wrap whole app?)
    Route::post('token-sign-in', [TokenController::class, 'signIn']);
    Route::post('token-sign-out', [TokenController::class, 'signOut']);
    Route::post('token-sign-out-all', [TokenController::class, 'signOutAll']);

    Route::middleware(['auth:sanctum'])->group(function () {

        Route::get('/user', function (Request $request) {
            return $request->user();
        });
        Route::apiResource('users', UserController::class);
        Route::apiResource('airlines', AirlineController::class);
        Route::apiResource('cycles', CycleController::class);
        Route::apiResource('cycles.assets', CycleAssetController::class);
        Route::get('cycles/{cycle}/recallable-assets', [CycleController::class, 'getRecallableAssets']);
        Route::post('cycles/{cycle}/recall-asset', [CycleController::class, 'recallAssets']);
        Route::apiResource('organizations', OrganizationController::class);
        Route::apiResource('asset-images', AssetImageController::class);
        Route::apiResource('asset-videos', AssetVideoController::class)->only(['store', 'update']);
        Route::apiResource('asset-audios', AssetAudioController::class)->only(['store', 'update']);
        Route::apiResource('fields', FieldController::class);
        Route::apiResource('field-labels', FieldLabelController::class);
        Route::apiResource('field-values', FieldValueController::class);
        Route::apiResource('collections', CollectionController::class);
        Route::apiResource('collection-values', CollectionValueController::class);
        Route::apiResource('schemas', SchemaController::class);
        Route::apiResource('languages', LanguageController::class);
        Route::apiResource('issues', IssueController::class);
        Route::apiResource('validator-fields', ValidatorFieldController::class);
        Route::apiResource('validator-field-types', ValidatorFieldTypeController::class);
        Route::apiResource('validator-airline-config', ValidatorAirlinesController::class);
        Route::apiResource('exports', ExportController::class);
        Route::apiResource('categories', CategoryController::class);
        Route::apiResource('category-values', CategoryValueController::class);
        Route::apiResource('roles', RoleController::class);
        Route::post('/categories/{category}/add-items', [CategoryController::class, 'addItems']);
        Route::get('categories/{category}/get-addable-items', [CategoryController::class, 'getAddableItems']);
        Route::put('categories/{category}/update-item-order', [CategoryController::class, 'updateItemOrder']);
        Route::delete('categories/{category}/remove-items', [CategoryController::class, 'removeItems']);
        Route::post('cycles/{cycle}/assets/{asset}/attach-assets', [CycleAssetController::class, 'attachAssets']);
        Route::get('/exports/{export}/download', [ExportController::class, 'download']);
        Route::put('/cycles/{cycle}/auto-fill', [CycleController::class, 'autoFill']);

        Route::apiResource('snapshots', \App\Http\Controllers\SnapshotController::class);

        Route::get('/media-info-files', [\App\Http\Controllers\MediaInfoController::class, 'getMediaInfoFiles']);
    });
});
