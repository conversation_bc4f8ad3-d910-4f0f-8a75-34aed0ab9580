includes:
    - vendor/larastan/larastan/extension.neon
    - vendor/nesbot/carbon/extension.neon

parameters:
    # This rule checks the appends array to make sure the property being appended doesn't overwrite actual model data.
    # Disabled for now to allow cycle->description to be computed (it also exists, but is unused in the db)
    checkModelAppends: false

    paths:
        - app/

    # Level 10 is the highest level
    level: 5

#    ignoreErrors:
#        - '#PHPDoc tag @var#'
#
#    excludePaths:
#        - ./*/*/FileToBeExcluded.php
