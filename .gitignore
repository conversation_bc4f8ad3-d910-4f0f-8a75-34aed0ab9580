/vendor/
node_modules/
npm-debug.log
yarn-error.log

.idea/
.vscode/

# Laravel 4 specific
bootstrap/compiled.php
app/storage/

# Laravel 5 & <PERSON><PERSON> specific
public/storage
public/hot

# Laravel 5 & <PERSON><PERSON> specific with changed public path
public_html/storage
public_html/hot

storage/*.key
.env
.env.testing
Homestead.yaml
Homestead.json
/.vagrant
.phpunit.result.cache

# coverage dumps
reports/

.DS_Store
