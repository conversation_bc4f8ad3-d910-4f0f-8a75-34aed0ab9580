<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;

class GenerateSnapshot implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private int $cycleId;
    private int $snapshotQueueId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $cycleId, int $snapshotQueueId)
    {
        $this->cycleId = $cycleId;
        $this->snapshotQueueId = $snapshotQueueId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /*
         * Note: If the runtime on these snapshots gets to be fairly long (over 10-20 seconds) then the job
         * queue will need to be adjusted - OR - this task should be offloaded to the cron/task scheduler
         * which by default gets a much longer runtime (and higher memory limit)
         */
        Artisan::call('app:get-snapshot', [
            'cycle_id' => $this->cycleId,
            'snapshot_queue_id' => $this->snapshotQueueId,
        ]);
    }
}
