<?php

namespace App\Jobs;

use App\Models\Asset;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable; 

class ValidateAssetFields implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Asset $asset,
    )
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // TODO: run from CSV imports, validate asset fields 
    }
}
