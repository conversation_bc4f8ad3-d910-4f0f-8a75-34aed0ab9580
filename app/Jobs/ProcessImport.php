<?php

namespace App\Jobs;

use App\Models\Airline;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Asset;
use App\Models\Cycle;
use App\Models\Import;
use App\Models\FieldValue;
use App\Models\Enums\ImportStatus;
use App\Utilities\CycleAssetManager;
use App\Models\Enums\AssetStatus;
use Illuminate\Support\Facades\DB;

const DESTINATION = '/imports/CSV/';
const ASSET_IDENTIFIER = 'Asset Identifier';
const FAST_FAIL_MESSAGE = 'This is a fast fail error. Nothing will be saved until it is resolved: ';

class ProcessImport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $import;
    protected $cycleID;
    protected $assetType;


    public function __construct(Import $import, $cycleID, $assetType)
    {
        $this->import = $import;
        $this->cycleID = $cycleID;
        $this->assetType = $assetType;
    }

    public function handle(CycleAssetManager $cycleAssetManager)
    {
        $cycleID = $this->cycleID;
        $assetType = $this->assetType;
        $import = $this->import;
        $cycle = Cycle::find($cycleID);
        $airlineId = $cycle->airline_id;

        try {
            $assets = $cycle->assets()
                ->when($assetType, function ($query) use ($assetType) {
                    return $query->where('asset_type', $assetType);
                })
                ->get();

            // if its a series, we need to get the episodes as well
            if ($assetType === 'series') {
                $assets = $assets->merge($cycle->assets()
                    ->withPivot('status')
                    ->where('asset_type', 'episode')
                    ->get());
            }

            $file_contents = Storage::get($import->file_path);
            $temp_path = tempnam(sys_get_temp_dir(), 'import_');
            file_put_contents($temp_path, $file_contents);
            $handle = fopen($temp_path, 'r');
            if (!$handle) {
                throw new \Exception("Failed to open file");
            }

            $headers = fgetcsv($handle);

            if (!$headers || empty($headers)) {
                throw new \Exception('Invalid CSV file: Cannot read headers ' . $import->file_path);
            }

            // define expected headers, we need to query the list fields for the current asset
            $fields = $assets->flatMap(function ($asset) use ($cycleID) {
                $fieldNames = collect($asset->getFields($cycleID))->pluck('name');
                if ($asset->asset_type === 'series') {
                    // SERIES: should be prepended so we can distinguish
                    $fieldNames = $fieldNames->map(function ($fieldName) {
                        return 'SERIES:' . $fieldName;
                    });
                }
                return $fieldNames->all();
            })->unique()->values()->all();


            $expectedHeaders = $fields;
            $missingHeaders = array_diff($expectedHeaders, $headers);

            if (!empty($missingHeaders)) {
                fclose($handle);
                throw new \Exception('CSV file is missing required headers: ' . implode(', ', $missingHeaders));
            }

            $c = 1;
            $errors = [];
            $fastFailErrors = [];
            $hasFastFailErrors = false;
            $hasErrors = false;
            $hasWarnings = false;
            $isSeries = $assetType === 'series';

            // We'll use a transaction to rollback if any fast fail errors are present
            DB::beginTransaction();

            try {
                while (($data = fgetcsv($handle)) !== false) {
                    $rowData = array_combine($headers, $data);
                    $c++;
                    if (!isset($rowData[ASSET_IDENTIFIER]) || empty($rowData[ASSET_IDENTIFIER])) {
                        $errors[] = [
                            'line' => $c,
                            'message' => 'Asset ID does not exist in the CSV file',
                            'content' => $data
                        ];
                        $hasErrors = true;
                        continue;
                    }

                    $assetIdentifier = $isSeries ? $rowData["SERIES:" . ASSET_IDENTIFIER] : $rowData[ASSET_IDENTIFIER];
                    $asset = $cycleAssetManager->recallAsset(
                        Asset::forAirline($airlineId)
                            ->where('title', $assetIdentifier)
                            ->where('asset_type', $assetType)
                            ->orderByDesc('version_number')
                            ->first(),
                        $cycle
                    );



                    if (!isset($asset) || !$asset->exists()) {
                        $rowErrors = $this->checkForDateErrors($rowData, $c, $isSeries);
                        $fastFailErrors = array_merge($fastFailErrors, $rowErrors);

                        $dateErrors = !empty($rowErrors);
                        // if the uuid is not found, we need to create a new asset

                        // If date errors are present we can just create the asset with dummy dates since it wont be saved
                        $startDate = $dateErrors ? Carbon::now() : Carbon::parse($rowData['Start Date']);
                        $endDate = $dateErrors ? Carbon::now() : Carbon::parse($rowData['End Date']);
                        $asset = Asset::create([
                            'title' => $assetIdentifier,
                            'asset_type' => $assetType,
                            'start_date' => $startDate,
                            'end_date' => $endDate
                        ]);

                        if (!$dateErrors) {
                            $cycle->assets()->attach($asset->id, [
                                'status' => AssetStatus::New,
                            ]);
                        }
                    } else {
                        $rowErrors = $this->checkForDateErrors($rowData, $c, $isSeries);
                        $fastFailErrors = array_merge($fastFailErrors, $rowErrors);
                        if (empty($rowErrors)) {
                            $asset->start_date = Carbon::parse($rowData['Start Date']);
                            $asset->end_date = Carbon::parse($rowData['End Date']);
                            $asset->save();
                        }
                    }

                    if ($isSeries) {
                        // in here we should try and find / create the episode in the row
                        $episode = Asset::where('title', $rowData[ASSET_IDENTIFIER])
                            ->where('asset_type', 'episode')
                            ->orderByDesc('version_number')
                            ->first();

                        if ($episode) {
                            // Update the episode to attach it to this series
                            $episode->parent_asset_id = $asset->id;
                            $rowErrors = $this->checkForDateErrors($rowData, $c, false);
                            $fastFailErrors = array_merge($fastFailErrors, $rowErrors);
                            if (empty($rowErrors)) {
                                $episode->save();
                            }
                        } else {
                            $rowErrors = $this->checkForDateErrors($rowData, $c, false);
                            $fastFailErrors = array_merge($fastFailErrors, $rowErrors);
                            $dateErrors = !empty($rowErrors);

                            $startDate = $dateErrors ? Carbon::now() : Carbon::parse($rowData['Start Date']);
                            $endDate = $dateErrors ? Carbon::now() : Carbon::parse($rowData['End Date']);

                            $episode = Asset::create([
                                'title' => $rowData[ASSET_IDENTIFIER],
                                'asset_type' => 'episode',
                                'start_date' => $startDate,
                                'end_date' => $endDate,
                                'parent_asset_id' => $asset->id
                            ]);

                            if (!$dateErrors) {
                                $cycle->assets()->attach($episode->id, [
                                    'status' => AssetStatus::New,
                                ]);
                            }
                        }

                        $fieldValueErrors = $this->setFieldValues($episode, $rowData, $c, $cycleID, false);
                        if (!empty($fieldValueErrors)) {
                            $fastFailErrors = array_merge($fastFailErrors, $fieldValueErrors);
                        } else {
                            $episode->save();
                        }
                    }

                    $fieldValueErrors = $this->setFieldValues($asset, $rowData, $c, $cycleID, $isSeries);

                    if (!empty($fieldValueErrors)) {
                        $fastFailErrors = array_merge($fastFailErrors, $fieldValueErrors);
                    } else {
                        $asset->save();
                    }

                }
            } catch (\Exception $e) {
                Log::error(sprintf("Transaction Error: %s", $e->getMessage()));
                DB::rollBack();
                throw $e;
            }

            fclose($handle);

            $hasFastFailErrors = !empty($fastFailErrors);
            if ($hasErrors || $hasFastFailErrors) {
                $import->status = ImportStatus::Error;
            } elseif ($hasWarnings) {
                $import->status =  ImportStatus::Warn;
            } else {
                $import->status = ImportStatus::Good;
            }

            if ($hasFastFailErrors) {
                DB::rollBack();
            } else {
                DB::commit();
            }

            $import->errors = array_merge($errors, $fastFailErrors);
            $import->save();
        } catch (\Exception $e) {
            Log::error(sprintf("Exception Error: %s", $e));
            $import->status = 'errors';
            $import->errors = [
                [
                    'line' => 0,
                    'message' => 'Processing exception: ' . $e->getMessage(),
                    'content' => null
                ]
            ];
            $import->save();
        }
    }

    private function checkForDateErrors($rowData, $lineNumber, $isSeries)
    {
        $rowErrors = [];
        $assetIdentifier = $isSeries
            ? $rowData["SERIES:" . ASSET_IDENTIFIER] ?? null
            : $rowData[ASSET_IDENTIFIER] ?? null;

        foreach (['Start Date', 'End Date'] as $dateField) {
            if (!isset($rowData[$dateField]) || empty($rowData[$dateField])) {
                $rowErrors[] = [
                    'line' => $lineNumber,
                    'message' => FAST_FAIL_MESSAGE . sprintf(
                        'Missing %s for %s %s on line %d',
                        $dateField,
                        $isSeries ? 'series' : 'asset',
                        $assetIdentifier,
                        $lineNumber
                    ),
                    'content' => $rowData
                ];
                continue;
            }
            try {
                Carbon::parse($rowData[$dateField]);
            } catch (\Exception $e) {
                $rowErrors[] = [
                    'line' => $lineNumber,
                    'message' => FAST_FAIL_MESSAGE . sprintf(
                        'Invalid %s for %s %s on line %d: %s',
                        $dateField,
                        $isSeries ? 'series' : 'asset',
                        $assetIdentifier,
                        $lineNumber,
                        $e->getMessage()
                    ),
                    'content' => $rowData
                ];
            }
        }

        return $rowErrors;
    }

    private function setFieldValues($item, $rowData, $lineNumber, $cycleID, $isSeries = false)
    {
        $errors = [];
        $fields = $item->getFields($cycleID);

        foreach ($fields as $field) {
            $fieldName = $isSeries ? 'SERIES:' . $field->name : $field->name;

            if (isset($rowData[$fieldName]) && !empty($rowData[$fieldName])) {

                $data = [
                    'asset_id' => $item->id,
                    'field_id' => $field->id,
                ];

                if ($field->is_localizable) {
                    $cycle = Cycle::find($cycleID);
                    $airline = Airline::find($cycle->airline_id);
                    $data['language_id'] = $airline->getDefaultOrSystemLanguageId();
                }

                $fieldValue = FieldValue::firstOrCreate($data);

                $isBadValue = $fieldValue->setValue($rowData[$fieldName]);

                if ($isBadValue) {
                    $errors[] = [
                        'line' => $lineNumber,
                        'message' => FAST_FAIL_MESSAGE . sprintf(
                            'Invalid dropdown value "%s" for field "%s" on asset "%s" (line %d). This value does not exist in the schema. Either add it to the schema or update it in your CSV.',
                            $rowData[$fieldName],
                            $fieldName,
                            $rowData[ASSET_IDENTIFIER] ?? ($rowData['SERIES:' . ASSET_IDENTIFIER] ?? 'Unknown'),
                            $lineNumber
                        ),
                        'content' => $rowData
                    ];
                }

                $fieldValue->save();
            }
        }

        return $errors;
    }
}
