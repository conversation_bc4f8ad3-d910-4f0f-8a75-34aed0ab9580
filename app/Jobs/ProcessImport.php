<?php

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Asset;
use App\Models\Cycle;
use App\Models\Import;
use App\Models\FieldValue;
use App\Models\Enums\ImportStatus;
use App\Utilities\CycleAssetManager;
use App\Models\Enums\AssetStatus;

const DESTINATION = '/imports/CSV/';
const ASSET_IDENTIFIER = 'Asset Identifier';

class ProcessImport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $import;
    protected $cycleID;
    protected $assetType;

    public function __construct(Import $import, $cycleID, $assetType)
    {
        $this->import = $import;
        $this->cycleID = $cycleID;
        $this->assetType = $assetType;
    }

    public function handle(CycleAssetManager $cycleAssetManager)
    {
        $cycleID = $this->cycleID;
        $assetType = $this->assetType;
        $import = $this->import;
        $cycle = Cycle::find($cycleID);

        try {
            $assets = $cycle->assets()
                ->when($assetType, function ($query) use ($assetType) {
                    return $query->where('asset_type', $assetType);
                })
                ->get();

            // if its a series, we need to get the episodes as well
            if ($assetType === 'series') {
                $assets = $assets->merge($cycle->assets()
                    ->withPivot('status')
                    ->where('asset_type', 'episode')
                    ->get());
            }

            $file_contents = Storage::get($import->file_path);
            $temp_path = tempnam(sys_get_temp_dir(), 'import_');
            file_put_contents($temp_path, $file_contents);
            $handle = fopen($temp_path, 'r');
            if (!$handle) {
                throw new \Exception("Failed to open file");
            }

            $headers = fgetcsv($handle);

            if (!$headers || empty($headers)) {
                throw new \Exception('Invalid CSV file: Cannot read headers ' . $import->file_path);
            }

            // define expected headers, we need to query the list fields for the current asset
            $fields = $assets->flatMap(function ($asset) use ($cycleID) {
                $fieldNames = collect($asset->getFields($cycleID))->pluck('name');
                if ($asset->asset_type === 'series') {
                    // SERIES: should be prepended so we can distinguish
                    $fieldNames = $fieldNames->map(function ($fieldName) {
                        return 'SERIES:' . $fieldName;
                    });
                }
                return $fieldNames->all();
            })->unique()->values()->all();


            $expectedHeaders = $fields;
            $missingHeaders = array_diff($expectedHeaders, $headers);

            if (!empty($missingHeaders)) {
                fclose($handle);
                throw new \Exception('CSV file is missing required headers: ' . implode(', ', $missingHeaders));
            }

            $c = 1;
            $errors = [];
            $hasErrors = false;
            $hasWarnings = false;

            // loop through the CSV file, each row should be an asset
            while (($data = fgetcsv($handle)) !== false) {
                $rowData = array_combine($headers, $data);
                $c++;
                if (!isset($rowData[ASSET_IDENTIFIER]) || empty($rowData[ASSET_IDENTIFIER])) {
                    $errors[] = [
                        'line' => $c,
                        'message' => 'Asset ID does not exist in the CSV file',
                        'content' => $data
                    ];
                    $hasErrors = true;
                    continue;
                }

                $assetIdentifier = $assetType === 'series' ? $rowData["SERIES:" . ASSET_IDENTIFIER] : $rowData[ASSET_IDENTIFIER];

                $asset = $cycleAssetManager->recallAsset(
                    Asset::where('title', $assetIdentifier)
                        ->where('asset_type', $assetType)
                        ->orderByDesc('version_number')
                        ->first(),
                    $cycle
                );

                if (!isset($asset) || !$asset->exists()) {
                    // if the uuid is not found, we need to create a new asset
                    $asset = Asset::create([
                        'title' => $assetIdentifier,
                        'asset_type' => $assetType,
                        // Try and use the csv dates, otherwise we'll use the cycle dates
                        'start_date' => !empty($rowData['start_date'])
                            ? Carbon::parse($rowData['start_date'])
                            : $cycle->start_date,
                        'end_date' => !empty($rowData['end_date'])
                            ? Carbon::parse($rowData['end_date'])
                            : $cycle->end_date
                    ]);
                    $cycle->assets()->attach($asset->id, [
                        'status' => AssetStatus::New,
                    ]);

                    Log::info(sprintf("New asset created with ID: %s", $assetIdentifier));
                }

                if ($assetType === 'series') {
                    // in here we should try and find / create the episode in the row
                    $episode = Asset::where('title', $rowData[ASSET_IDENTIFIER])
                        ->where('asset_type', 'episode')
                        ->orderByDesc('version_number')
                        ->first();

                    if ($episode) {
                        // Update the episode to attach it to this series
                        $episode->parent_asset_id = $asset->id;
                        $episode->save();

                        Log::info("Linked episode {$episode->title} to series {$asset->title}");
                    } else {
                        $episode = Asset::create([
                            'title' => $rowData[ASSET_IDENTIFIER],
                            'asset_type' => 'episode',
                            'start_date' => !empty($rowData['start_date'])
                                ? Carbon::parse($rowData['start_date'])
                                : $cycle->start_date,
                            'end_date' => !empty($rowData['end_date'])
                                ? Carbon::parse($rowData['end_date'])
                                : $cycle->end_date,
                            'parent_asset_id' => $asset->id
                        ]);

                        $cycle->assets()->attach($episode->id, [
                            'status' => AssetStatus::New,
                        ]);
                    }

                    $episode->getFields($cycleID)->each(function ($field) use ($rowData, $episode) {
                        $fieldName = $field->name;

                        if (isset($rowData[$fieldName]) && !empty($rowData[$fieldName])) {
                            $fieldValue = FieldValue::firstOrCreate(
                                [
                                    'asset_id' => $episode->id,
                                    'field_id' => $field->id,
                                ]
                            );
                            $fieldValue->setValue($rowData[$fieldName]);
                            $fieldValue->save();
                        }
                    });
                }

                $asset->getFields($cycleID)->each(function ($field) use ($rowData, $asset, $assetType) {
                    $fieldName = $assetType === 'series' ? 'SERIES:' . $field->name : $field->name;
                    if (isset($rowData[$fieldName]) && !empty($rowData[$fieldName])) {
                        $fieldValue = FieldValue::firstOrCreate(
                            [
                                'asset_id' => $asset->id,
                                'field_id' => $field->id,
                            ]
                        );
                        $fieldValue->setValue($rowData[$fieldName]);
                        $fieldValue->save();
                    }
                });

                $asset->start_date ??= $cycle->start_date;
                $asset->end_date ??= $cycle->end_date;
                $asset->save();
            }
            fclose($handle);

            if ($hasErrors) {
                $import->status = ImportStatus::Error;
            } elseif ($hasWarnings) {
                $import->status =  ImportStatus::Warn;
            } else {
                $import->status = ImportStatus::Good;
            }

            $import->errors = $errors;
            $import->save();
        } catch (\Exception $e) {
            Log::error(sprintf("Exception Error: %s", $e));
            $import->status = 'errors';
            $import->errors = [
                [
                    'line' => 0,
                    'message' => 'Processing exception: ' . $e->getMessage(),
                    'content' => null
                ]
            ];
            $import->save();
        }
    }
}
