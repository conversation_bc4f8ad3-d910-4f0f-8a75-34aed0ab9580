<?php

namespace App\Jobs;

use Exception;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Export;
use App\Exporters\{AssetExporter, CategoryExporter};
use App\Models\Enums\ExportStatus;

class ProcessExport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $export;
    protected $cycleID;
    protected $type;
    protected $isTemplate;
    protected $langID;

    public function __construct(Export $export, $cycleID, $type, $isTemplate = false, $langID)
    {
        $this->export = $export;
        $this->cycleID = $cycleID;
        $this->type = $type;
        $this->isTemplate = $isTemplate;
        $this->langID = $langID;
    }

    public function handle()
    {
        $this->export->update(['status' => ExportStatus::Processing]);

        try {
            $exporter = $this->type === 'category' ?
            new CategoryExporter($this->cycleID, $this->isTemplate, $this->langID)
            : new AssetExporter($this->cycleID, $this->type, $this->isTemplate);

            $filePath = $exporter->export([]);

            $this->export->update([
                'path' => $filePath,
                'status' => ExportStatus::Ready,
                'processed_at' => Carbon::now()
            ]);
        } catch (Exception $e) {
            $this->export->update(['status' => ExportStatus::Failed]);
            Log::error(sprintf("Export failed Exception Error: %s", $e));
        }
    }
}
