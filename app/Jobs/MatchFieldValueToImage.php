<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\{AssetImage, FieldValue};

class MatchFieldValueToImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $fieldValueID;

    public function __construct($fieldValueID)
    {
        $this->fieldValueID = $fieldValueID;
    }

    public function handle()
    {
        $fieldValue = FieldValue::find($this->fieldValueID);

        Log::debug(sprintf("Field value created or updated! Attempting to match value %s to asset image file names", $fieldValue->value));

        // Let's grab the cycleIDs and airlineIDs that exist for this field value. Technically all cycle IDs belong to the same airline, but we need to be sure.
        $cycleIDs = $fieldValue->asset->cycles()->pluck('cycle_id')->toArray(); 
        $airlineIDs = $fieldValue->asset->cycles()->pluck('airline_id')->toArray(); 

        $image = AssetImage::where('file_name', $fieldValue->value)
            ->whereIn('cycle_id', $cycleIDs)
            ->whereIn('airline_id', $airlineIDs)
            ->first();

        if (!$image) {
            Log::debug(sprintf("No asset image found for field value %s", $fieldValue->value));
            return;
        }

        if ($image->asset_id === $fieldValue->asset_id && $image->field_id === $fieldValue->field_id) {
            Log::debug(sprintf("Image %s already attached to field value %s", $image->file_name, $fieldValue->value));
            return;
        }

        $image->attachToFieldValue($fieldValue);
        Log::debug(sprintf("Image %s matched to field value %s", $image->file_name, $fieldValue->value));
    }
}
