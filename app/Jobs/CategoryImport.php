<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\{Cycle, Import, Category, CategoryValue};
use App\Models\Enums\ImportStatus;

class CategoryImport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    protected const CATEGORY_IDENTIFIER = 'Category Identifier';
    protected const ITEM_IDENTIFIER = 'Item Identifier';

    protected $import;
    protected $cycleID;

    public function __construct(Import $import, $cycleID)
    {
        $this->import = $import;
        $this->cycleID = $cycleID;
    }

    public function handle()
    {
        $cycleID = $this->cycleID;
        $import = $this->import;
        $cycle = Cycle::find($cycleID);
        $categoryIDsMap = [];

        try {
            $categoryIDs = $cycle->categories()->pluck('categories.id')->toArray();
            $allCategoryValues = CategoryValue::whereIn('category_id', $categoryIDs)
                ->with('category')
                ->get();

            $file_contents = Storage::get($import->file_path);
            $temp_path = tempnam(sys_get_temp_dir(), 'import_');
            file_put_contents($temp_path, $file_contents);
            $handle = fopen($temp_path, 'r');
            if (!$handle) {
                throw new \Exception("Failed to open file");
            }

            $headers = fgetcsv($handle);

            if (!$headers || empty($headers)) {
                throw new \Exception('Invalid CSV file: Cannot read headers ' . $import->file_path);
            }

            $expectedHeaders = [
                self::CATEGORY_IDENTIFIER,
                self::ITEM_IDENTIFIER,
                'Type'
            ];
            $missingHeaders = array_diff($expectedHeaders, $headers);

            if (!empty($missingHeaders)) {
                fclose($handle);
                throw new \Exception('CSV file is missing required headers: ' . implode(', ', $missingHeaders));
            }

            $c = 1;
            $errors = [];
            $hasErrors = false;
            $hasWarnings = false;

            // loop through the CSV file, each row should be an category item
            while (($data = fgetcsv($handle)) !== false) {
                $rowData = array_combine($headers, $data);
                $c++;
                if (!isset($rowData[self::CATEGORY_IDENTIFIER]) || empty($rowData[self::CATEGORY_IDENTIFIER])) {
                    $errors[] = [
                        'line' => $c,
                        'message' => "Row {$c}: Missing or empty '" . self::CATEGORY_IDENTIFIER . "' field.",
                        'content' => $data
                    ];
                    $hasErrors = true;
                    continue;
                }
                if (!isset($rowData[self::ITEM_IDENTIFIER]) || empty($rowData[self::ITEM_IDENTIFIER])) {
                    $errors[] = [
                        'line' => $c,
                        'message' => "Row {$c}: Missing or empty '" . self::ITEM_IDENTIFIER . "' field.",
                        'content' => $data
                    ];
                    $hasErrors = true;
                    continue;
                }
                $categoryIdentifier = $rowData[self::CATEGORY_IDENTIFIER];
                $itemIdentifier = $rowData[self::ITEM_IDENTIFIER];
                $itemType = $rowData['Type'];
                $categoryValue = $allCategoryValues->firstWhere('value', $categoryIdentifier);
                $category = $categoryValue ? $categoryValue->category : null;

                if (!isset($category) || !$category->exists()) {
                    $errors[] = [
                        'line' => $c,
                        'message' => "Row {$c}: Category '{$categoryIdentifier}' does not exist in this cycle.",
                        'content' => $rowData
                    ];
                    $hasErrors = true;
                    continue;
                }

                if ($itemType !== 'asset' && $itemType !== 'category') {
                    $errors[] = [
                        'line' => $c,
                        'message' => "Row {$c}: Invalid item type '{$itemType}'. Expected 'asset' or 'category'.",
                        'content' => $rowData
                    ];
                    $hasErrors = true;
                    continue;
                }

                if ($itemType === 'asset') {
                    if ($category->is_root) {
                        $errors[] = [
                            'line' => $c,
                            'message' => "Row {$c}: Cannot add assets to top level categories.",
                            'content' => $rowData
                        ];
                        $hasErrors = true;
                        continue;
                    }
                    $item = $cycle->assets()
                        ->where('title', $itemIdentifier)
                        ->first();

                    if (!isset($item) || !$item->exists()) {
                        $errors[] = [
                            'line' => $c,
                            'message' => "Row {$c}: Asset '{$itemIdentifier}' does not exist in this cycle.",
                            'content' => $rowData
                        ];
                        $hasErrors = true;
                        continue;
                    }
                } else {
                    $itemCategoryValue = $allCategoryValues->firstWhere('value', $categoryIdentifier);
                    $item = $itemCategoryValue ? $itemCategoryValue->category : null;

                    if (!$category->is_root) {
                        $errors[] = [
                            'line' => $c,
                            'message' => "Row {$c}: Cannot add categories to non top-level categories.",
                            'content' => $rowData
                        ];
                        $hasErrors = true;
                        continue;
                    }

                    if (!isset($item) || !$item->exists()) {
                        $errors[] = [
                            'line' => $c,
                            'message' => "Row {$c}: Category '{$itemIdentifier}' does not exist in this cycle.",
                            'content' => $rowData
                        ];
                        $hasErrors = true;
                        continue;
                    }
                }

                if (!isset($categoryIDsMap[$category->id])) {
                    $categoryIDsMap[$category->id] = [];
                }
                $categoryIDsMap[$category->id][] = [
                    'item_id' => $item->id,
                    'type' => $itemType
                ];

            }
            fclose($handle);

            if ($hasErrors) {
                $import->status = ImportStatus::Error;
            } elseif ($hasWarnings) {
                $import->status =  ImportStatus::Warn;
            } else {
                $import->status = ImportStatus::Good;
            }

            $import->errors = $errors;
            $import->save();

            if (!$hasErrors) {
                $cycle->clearAllCategoryItems();

                foreach ($categoryIDsMap as $categoryID => $items) {
                    $category = Category::find($categoryID);
                    $assetIDs = [];
                    $categoryIDs = [];

                    foreach ($items as $item) {
                        if ($item['type'] === 'asset') {
                            $assetIDs[] = $item['item_id'];
                        } elseif ($item['type'] === 'category') {
                            $categoryIDs[] = $item['item_id'];
                        }
                    }

                    if (!empty($assetIDs)) {
                        $category->addAssets($assetIDs);
                        \Log::info(sprintf("Added %d assets to category '%s'", count($assetIDs), $category->title));
                    }

                    if (!empty($categoryIDs)) {
                        $category->addChildCategories($categoryIDs);
                        \Log::info(sprintf("Added %d categories to category '%s'", count($categoryIDs), $category->title));
                    }

                }
            }

        } catch (\Exception $e) {
            Log::error(sprintf("Exception Error: %s", $e));
            $import->status = 'errors';
            $import->errors = [
                [
                    'line' => 0,
                    'message' => 'Processing exception: ' . $e->getMessage(),
                    'content' => null
                ]
            ];
            $import->save();
        }
    }
}
