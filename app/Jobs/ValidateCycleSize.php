<?php

namespace App\Jobs;

use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

use App\Models\Cycle;
use App\Utilities\FileSizeConverter;
use App\Models\Enums\AssetStatus;

class ValidateCycleSize implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * The cycle instance.
     */
    protected $cycle;

    /**
     * Create a new job instance.
     */
    public function __construct(Cycle $cycle)
    {
        $this->cycle = $cycle;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // get airlines' snapshot size
        $airline = $this->cycle->airline()->first();
        $totalSize = $airline['max_total_snapshot_size'];
        $totalCycleSize = 0;
        $deltaSize = $airline['max_delta_snapshot_size'];
        $deltaCycleSize = 0;

        $sizeFields = $this->cycle->schema->fields->where('is_size_field', true);
        
        // get the list of assets in the cycle
        $assets = $this->cycle->assets()->get();
        foreach ($assets as $asset) {
            foreach ($sizeFields as $field) {
                $value = $field->fieldValues()->where('asset_id', $asset->id)->first();
                if ($value) {
                    $valueInBytes = FileSizeConverter::toBytes($value->value);
                    $totalCycleSize += $valueInBytes;
                } else {
                    Log::error(sprintf("No size value for field %s and asset %s", $field->name, $asset->id));
                }
            }
        }
        // get the list of holdover assets in the cycle
        $assets = $this->cycle->assets()->where('status', AssetStatus::Holdover)->get();
        foreach ($assets as $asset) {
            foreach ($sizeFields as $field) {
                $value = $field->fieldValues()->where('asset_id', $asset->id)->first();
                if ($value) {
                    $valueInBytes = FileSizeConverter::toBytes($value->value);
                    $deltaCycleSize += $valueInBytes;
                } else {
                    Log::error(sprintf("No size value for field %s and asset %s", $field->name, $asset->id));
                }
            }
        }
        
        $this->cycle->total_snapshot_size = $totalCycleSize;
        $this->cycle->recalled_snapshot_size = $deltaCycleSize;
        $this->cycle->save();

        // validating if the cycle sizes are an issue
        if ($totalCycleSize > $totalSize || $deltaCycleSize > $deltaSize) {

            Log::error(sprintf("Cycle %s has exceeded the delta size limit of %d or the total size limit of %d", $this->cycle->id, $deltaSize, $totalSize));
            // maybe create an issue
        }
    }
}
