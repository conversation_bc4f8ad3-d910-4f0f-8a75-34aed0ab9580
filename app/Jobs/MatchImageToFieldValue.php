<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\{AssetImage, FieldValue};

class MatchImageToFieldValue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $imageID;
    protected $fieldValueID;

    public function __construct($imageID)
    {
        $this->imageID = $imageID;
    }

    public function handle()
    {
        $image = AssetImage::find($this->imageID);

        Log::debug(sprintf("Image marked as uploaded! Attempting to match image file name %s to asset field", $image->file_name));

        // Based on the image file name, we are going to see if there are any asset field values that match.
        $cycle = $image->cycle()->where('airline_id', $image->airline_id)->first();
        if (!$cycle) {
            Log::debug(sprintf("No cycle found for image %s in airline %s", $image->file_name, $image->airline->name));
            return;
        }

        $assetIDs = $cycle->assets()->pluck('asset_id')->toArray();
        $fieldValue = FieldValue::where('value', $image->file_name)
            ->whereIn('asset_id', $assetIDs)
            ->first();

        if (!$fieldValue) {
            Log::debug(sprintf("No field value found for image %s", $image->file_name));
            return;
        }

        $image->attachToFieldValue($fieldValue);
        Log::debug(sprintf("Image %s matched to field value %s", $image->file_name, $fieldValue->value));
    }
}
