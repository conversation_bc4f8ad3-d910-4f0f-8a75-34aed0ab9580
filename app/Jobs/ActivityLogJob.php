<?php

namespace App\Jobs;

use Spatie\Activitylog\Models\Activity;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ActivityLogJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $activityLog;

    public function __construct(array $activityLog)
    {
        $this->activityLog = $activityLog;
    }

    public function handle()
    {
        Activity::create([
            'log_name' => $this->activityLog['log_name'],
            'description' => $this->activityLog['description'],
            'subject_type' => $this->activityLog['subject_type'],
            'subject_id' => $this->activityLog['subject_id'],
            'causer_type' => $this->activityLog['causer_type'] ?? null,
            'causer_id' => $this->activityLog['causer_id'] ?? null,
            'properties' => $this->activityLog['properties']
        ]);
    }
}