<?php

namespace App\Http\Controllers;

use Illuminate\Http\{Response, Request};
use App\Models\Cycle;
use App\Models\Schema;
use App\Models\Airline;
use App\Models\Asset;
use App\Http\Requests\StoreCycleRequest;
use App\Http\Requests\UpdateCycleRequest;
use App\Services\ViasatService;
use App\Utilities\CycleAssetManager;
use Illuminate\Support\Facades\Gate;

class CycleController extends Controller
{
    public function index()
    {
        $request = request();
        $query = Cycle::query()->with('airline');

        $airlineID = $request->input('airlineID');
        if ($airlineID) {
            $query->where('airline_id', $airlineID);
        }

        if ($request->filled('search')) {
            $search = '%' . $request->search . '%';
            $query->whereHas('airline', function ($q) use ($search) {
                $q->where('name', 'like', $search);
            });
        }

        if ($request->filled('filter.airline_id')) {
            $query->where('airline_id', $request->input('filter.airline_id'));
        }

        $query->forUser($request->user());

        return $query
            ->orderBy(request()->sortKey ?? 'start_date', request()->sortDirection ?? 'desc')
            ->get()->each->setAppends(['airline_name', 'status_counts']);
    }

    public function store(StoreCycleRequest $request)
    {
        Gate::authorize('create', Cycle::class);

        $data = $request->validated();

        $last_cycle = Cycle::where('airline_id', $data['airline_id'])->orderBy('id', 'desc')->with('airline')->first();

        $airline = Airline::where('id', $data['airline_id'])->first();

        $lastAssets = [];
        if ($last_cycle) {
            $lastAssets = $last_cycle->assets()->withPivot('status')->get();
        }

        // previous schema if exists or else root schema
        $schema_id = $last_cycle
            ? $last_cycle->schema_id
            : Schema::where('is_root', true)->first()->id;


        $last_cycle_end_date = $last_cycle->end_date ?? now();
        $month_to_add = $airline->cycle_frequency->toMonth();

        $cycle = Cycle::create(array_merge(
            $request->validated(),
            [
                'start_date' => $request->start_date ?? $last_cycle_end_date,
                'end_date' => $request->end_date ?? $last_cycle_end_date->copy()->addMonths($month_to_add),
                'schema_id' => $schema_id
            ]
        ));


        foreach ($lastAssets as $asset) {
            CycleAssetManager::migrateAsset($asset, $cycle);
        }

        if ($last_cycle) {
            CycleAssetManager::migrateCategories($last_cycle, $cycle, $data['airline_id']);
        }
        return response()->json([
            'success' => true,
            'message' => 'Cycle created successfully',
            'cycle' => $cycle,
        ], Response::HTTP_CREATED);
    }

    public function show(Cycle $cycle)
    {
        Gate::authorize('view', $cycle);

        $cycle->setAppends(['airline_name']);
        return $cycle;
    }

    public function update(UpdateCycleRequest $request, Cycle $cycle)
    {
        Gate::authorize('update', $cycle);

        $cycle->update($request->validated());
        return response()->json([
            'success' => true,
            'message' => 'Cycle updated successfully',
            'cycle' => $cycle
        ]);
    }

    public function destroy(Cycle $cycle)
    {
        Gate::authorize('delete', $cycle);

        $cycle->delete();
        return response()->json([
            'success' => true,
            'message' => 'Cycle deleted successfully'
        ], Response::HTTP_NO_CONTENT);
    }

    public function autoFill(Cycle $cycle, ViasatService $viasatService)
    {
        Gate::authorize('update', $cycle);

        $viasatService->autoFill($cycle);

        return response()->json([
            'success' => true,
            'message' => 'Fetch external API data job dispatched successfully'
        ]);
    }

    public function getRecallableAssets(Cycle $cycle)
    {
        $assetType = request()->assetType;
        return CycleAssetManager::getRecallableAssets($cycle, $assetType);
    }

    public function recallAssets(Request $request, Cycle $cycle)
    {
        $assetsToRecall = Asset::whereIn('id', $request->recalled_assets)->get();
        $recalledAssets = [];
        foreach ($assetsToRecall as $asset) {
            $recalledAsset = CycleAssetManager::recallAsset($asset, $cycle);
            if ($recalledAsset) {
                $recalledAssets[] = $recalledAsset;
            }
        }
        return response()->json([
            'success' => true,
            'message' => 'Assets recalled successfully',
            'recalled_assets' => $recalledAssets
        ], Response::HTTP_OK);
    }
}
