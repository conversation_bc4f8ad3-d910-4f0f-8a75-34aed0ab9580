<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreValidatorAirlineRequest;
use App\Http\Resources\ValidatorAirlineParamCollection;
use App\Models\{Airline, Validator, ValidatorAirlineParam};
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

/**
 * Validators that have goup value and params must be configured per airline.
 */
class ValidatorAirlinesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        Gate::authorize('viewAny', ValidatorAirlineParam::class);

        $airlineIDs = request()->input('filter.airline');
        $search = request()->query('search');

        $airlines = Airline::when($airlineIDs, function ($query) use ($airlineIDs) {
            return $query->whereIn('id', [$airlineIDs]);
        })->when($search, function ($query) use ($search) {
            return $query->where('name', 'like', "%{$search}%");
        })->get();

        $validators = Validator::whereNotNull(['group', 'parameters'])->get();

        $data = ValidatorAirlineParam::with(['airline', 'validator'])->when($airlineIDs, function ($query) use ($airlineIDs) {
            return $query->whereIn('airline_id', [$airlineIDs]);
        })->when($search, function ($query) use ($search) {
            return $query->whereHas('validator', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('airline', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        })->get();

        // prefill
        $airlines->each(function ($airline) use ($data, $validators) {
            $validators->each(function ($validator) use ($data, $airline) {
                if ($data->where('validator_id', $validator->id)->where('airline_id', $airline->id)->count() > 0) {
                    return;
                }

                // no value specified yet, prefill with default params
                $validatorAirlineParam = new ValidatorAirlineParam([
                    'validator_id' => $validator->id,
                    'airline_id' => $airline->id,
                    'parameters' => json_decode($validator->parameters), // cast is not triggered in here
                ]);
                $validatorAirlineParam->save();
                $data->push(
                    $validatorAirlineParam
                        ->setRelation('validator', $validator)
                        ->setRelation('airline', $airline)
                );
            });
        });

        return new ValidatorAirlineParamCollection($data);
    }

    /**
     * Store a newly created resource in storage.
     * - params must have unique validator/airline
     */
    public function store(StoreValidatorAirlineRequest $request)
    {
        $validatedData = $request->validated();

        Gate::authorize('create', ValidatorAirlineParam::class);

        $data = ValidatorAirlineParam::updateOrCreate([
            'validator_id' => $validatedData['validator_id'],
            'airline_id' => $validatedData['airline_id'],
        ], [
            'parameters' => $validatedData['parameters']
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Successfully saved airline specific validator',
            'data' => $data,
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
