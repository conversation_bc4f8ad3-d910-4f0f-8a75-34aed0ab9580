<?php

namespace App\Http\Controllers;

use Illuminate\Http\{Request};
use Illuminate\Support\Facades\Storage;
use App\Models\Export;
use App\Jobs\ProcessExport;
use App\Models\Enums\ExportStatus;
use App\Http\Requests\StoreExportRequest;

class ExportController extends Controller
{
    public function index(Request $request)
    {
        $query = Export::query();

        $exports = $query->orderBy('created_at', 'desc')->get();

        return response()->json(['exports' => $exports]);
    }

    public function store(StoreExportRequest $request)
    {
        $export = Export::create([
            'filename' => $request->name,
            'status' => ExportStatus::Pending,
        ]);

        ProcessExport::dispatchSync($export, $request->cycleID, $request->type, $request->isTemplate, $request->langID);

        // Refresh the model to get the updated file path
        $export->refresh();

        return response()->json([
            'message' => 'Export request created successfully',
            'export' => $export
        ], 201);
    }

    public function show(Export $export)
    {
        return response()->json(['export' => $export]);
    }

    public function download(Export $export)
    {
        return ['status' => 'ok', 'url' => Storage::temporaryUrl($export->path, now()->addMinutes(10))];
    }
}
