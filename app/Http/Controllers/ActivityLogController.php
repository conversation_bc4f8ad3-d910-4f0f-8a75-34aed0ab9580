<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use Illuminate\Support\Facades\Gate;

class ActivityLogController extends Controller
{
    public function index()
    {
        Gate::authorize('viewAny', ActivityLog::class);

        $search = request()->search;
        $causerID = request()->causerID;
        $subjectID = request()->subjectID;

        $perPage = (int) request()->input('pagination.rowsPerPage', 10);
        $page = (int) request()->input('pagination.currentPage', 0) + 1;

        return ActivityLog::when($search, function ($query) use ($search) {
                return $query->where('description', 'like', "%{$search}%");
            })
            ->when($causerID, function ($query) use ($causerID) {
                return $query->where('causer_id', $causerID);
            })
            ->when($subjectID, function ($query) use ($subjectID) {
                return $query->where('subject_id', $subjectID);
            })
            ->with(['causer', 'subject'])
            ->orderBy(request()->input('sorting.key', 'created_at'), request()->input('sorting.direction', 'desc'))
            ->paginate($perPage, ['*'], 'page', $page);
    }

}
