<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAssetAudioRequest;
use App\Http\Requests\UpdateAssetAudioRequest;
use App\Models\AssetAudio;
use App\Models\Cycle;
use Illuminate\Http\Response;
use App\Services\ValidationFieldService;
use App\Services\ViasatService;
use App\Utilities\CycleAssetManager;

class AssetAudioController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAssetAudioRequest $request, ValidationFieldService $validationService, ViasatService $viasatService)
    {
        $validatedData = $request->validated();
        $fileInfo = $viasatService->getFileInfo('audio', $validatedData['value']);

        $assetAudio = AssetAudio::updateOrCreate([
            'cycle_id' => $validatedData['cycle_id'],
            'airline_id' => Cycle::find($validatedData['cycle_id'])->airline_id,
            'field_id' => $validatedData['field_id'],
            'asset_id' => $validatedData['asset_id'],
        ], [
            'file_name' => $validatedData['value'],
            'size' => $fileInfo['size'],
            'viasat_id' => $fileInfo['viasat_id'],
        ]);

        $error = $validationService->runValidation($assetAudio, $validatedData['cycle_id'], true);
        $success = $error == null ? true : false;

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Successfully saved audio field value' : $error,
            'field' => $assetAudio,
        ], $success ? Response::HTTP_CREATED : Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAssetAudioRequest $request, AssetAudio $assetAudio, ValidationFieldService $validationService, ViasatService $viasatService, CycleAssetManager $cycleAssetManager)
    {
        $validatedData = $request->validated();

        $duplicatedAsset = $cycleAssetManager->ensureEditableAssetVersion($assetAudio->asset_id, $validatedData['cycle_id'], $assetAudio);
        $fileInfo = $viasatService->getFileInfo('audio', $validatedData['value']);

        if ($duplicatedAsset->id !== $assetAudio->asset_id) {
            $assetAudio = $assetAudio->replicate();
            $assetAudio->asset_id = $duplicatedAsset->id;
        }
        $assetAudio->setValue($validatedData['value']);
        $assetAudio->size = $fileInfo['size'];
        $assetAudio->viasat_id = $fileInfo['viasat_id'];
        $assetAudio->save();

        $error = $validationService->runValidation($assetAudio, $validatedData['cycle_id'], true);
        $success = $error == null ? true : false;

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Audio field value updated successfully' : $error,
            'field' => $assetAudio
        ], $success ? Response::HTTP_ACCEPTED : Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
