<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSchemaRequest;
use App\Http\Requests\UpdateSchemaRequest;
use App\Models\Schema;
use App\Models\Cycle;
use Illuminate\Support\Facades\Gate;

class SchemaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();
        return Schema::all()->when(
            !$user->has_unrestricted_edit_role,
            function ($query) use ($user) {
                $allowed_schemas = Cycle::forUser($user)->distinct()->pluck('schema_id');

                return $query->whereIn('id', $allowed_schemas);
            }
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchemaRequest $request)
    {
        Gate::authorize('create', Schema::class);

        $data = $request->validated();
        $schemaToClone = Schema::find($data['schemaID']);

        $newSchema = $schemaToClone->cloneSchema();
        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new schema from previous schema',
            'schema' => $newSchema
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Schema $schema)
    {
        Gate::authorize('view', $schema);

        return $schema;
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Schema $schema)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchemaRequest $request, Schema $schema)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Schema $schema)
    {
        //
    }
}
