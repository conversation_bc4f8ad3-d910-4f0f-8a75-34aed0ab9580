<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\CycleFrequency;

class StaticValuesController extends Controller
{
    public function getCycleFrequencies()
    {
        $cycle_frequencies = CycleFrequency::cases();
        $values = array_map(fn(CycleFrequency $cf) => ["value" => $cf->value, "label" => $cf->name], $cycle_frequencies);

        return response()->json($values);
    }

    public function getAssetTypes()
    {
        $asset_types = AssetTypeEnum::cases();
        $values = array_map(fn($type) => [
            "value" => $type->value,
            "route_value" => $type->routeValue(),
            "plural_label" => $type->pluralLabel(),
            "label" => $type->label(),
            "parent_type" => $type->getParentType(),
            'is_parent' => $type->isParent(),
        ], $asset_types);

        return response()->json($values);
    }
}
