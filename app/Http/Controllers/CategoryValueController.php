<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCategoryValueRequest;
use App\Models\CategoryValue;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class CategoryValueController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCategoryValueRequest $request)
    {
        $data = $request->validated();

        $category = Category::find($data['category_id']);
        Gate::authorize('update', $category);

        $success = CategoryValue::upsert($data, uniqueBy: ['category_id', 'language_id'], update: ['value']);

        return response()->json([
            'success' => $success,
            'message' => 'Successfully saved category value',
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $values = CategoryValue::whereHas('category', fn ($q) => $q->where('id', $category->id))->get('value', 'language_id');

        return response()->json([
            'success' => true,
            'message' => 'Successfully fetched category value',
            'data' => $values->toArray(),
        ], Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
