<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreValidatorFieldRequest;
use App\Http\Resources\ValidatorFieldCollection;
use App\Http\Resources\ValidatorFieldResource;
use App\Models\Enums\FieldEnum;
use App\Models\Validator;
use App\Models\ValidatorField;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class ValidatorFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     * list of validators available for the field type
     * mapped to the enabled validators
     */
    public function index()
    {
        Gate::authorize('viewAny', ValidatorField::class);

        $fieldType = FieldEnum::tryFrom(request()->input('type'))->value;
        $fieldId = (int) request()->input('field_id');

        $validators = Validator::whereHas(
            'validatorFieldType',
            fn ($query) =>
                $query->where('field_type', $fieldType)->where('enabled', true)
        )
            ->with([
                'validatorFieldType' => fn ($query) =>
                    $query->where('field_type', $fieldType)->where('enabled', true),
                'validatorField' => fn ($query) =>
                    $query->where('field_id', $fieldId)
            ])
            ->get();

        return new ValidatorFieldCollection($validators);
    }

    /**
     * Save/Update the validator field relation
     * Only create if doesn't exist otherwise update
     */
    public function store(StoreValidatorFieldRequest $request)
    {
        $validatedData = $request->validated();

        Gate::authorize('create', ValidatorField::class);

        $validatorField = ValidatorField::firstOrNew([
            'validator_id' => $validatedData['validator_id'],
            'field_id' => $validatedData['field_id']
        ]);

        $validatorField->enabled = $validatedData['enabled'];
        $validatorField->severity = $validatedData['severity'];

        if (isset($validatedData['parameters'])) {
            $validatorField->parameters = $validatedData['parameters'];
        }

        $validatorField->save();

        return response()->json([
            'success' => true,
            'data' => new ValidatorFieldResource($validatorField),
            'message' => 'Validator Field Type entry saved successfully.'
        ], $validatorField->wasRecentlyCreated ? Response::HTTP_CREATED : Response::HTTP_OK);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
