<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreIssueRequest;
use App\Http\Requests\UpdateIssueRequest;
use App\Http\Resources\IssueCollection;
use App\Http\Resources\IssueResource;
use App\Models\Enums\IssueEnum;
use App\Models\Issue;
use Illuminate\Http\Response; 

class IssueController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {   
        $type = IssueEnum::tryFrom(request()->query('type'));
        $sortDirection = strtolower(request()->query('sort_direction', 'desc'));
        $sortKey = request()->query('sort_key', 'created_at');
        $search = request()->query('search');

        $sortKeys = [
            'severity' => 'validator_fields.severity',
            'description' => 'issues.description',
            'asset' => 'assets.title',
            'created_at' => 'issues.created_at',
            'created_by' => 'issues.created_by'
        ];
    
        $orderBy = $sortKeys[$sortKey] ?? 'issues.created_at';
        $orderByDirection = $sortDirection === 'asc' ? 'asc' : 'desc';

        $query = Issue::query();
        $query->select('issues.*');
        if ($sortKey === 'severity') {
            $query->join('validator_fields', 'issues.validator_field_id', '=', 'validator_fields.id');            
        }
        else if($sortKey === 'asset') {
            $query->join('assets', 'issues.asset_id', '=', 'assets.id');
        }

        if ($search){
            $query->where('description', 'like', "%{$search}%");
        }

        $query->where('type', $type)
            ->where('cycle_id', request()->cycleID)
            ->with(['asset', 'fieldValue', 'validatorField'])
            ->orderBy($orderBy, $orderByDirection)
            ->orderBy('issues.id', 'asc');

        $issues = $query->paginate((int) request()->input('per_page', 10), ['*'], 'page', (int) request()->input('page') + 1);
        return new IssueCollection($issues);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreIssueRequest $request)
    { 
        $validatedRequest = $request->validated();
        $issue = Issue::create(array_merge($validatedRequest, [
            'created_by' => $validatedRequest['is_custom'] ? auth()->user()->email : null
        ]));  

        return response()->json([
            'success' => true,
            'issue' => new IssueResource($issue),
            'message' => 'Issue created successfully.'
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Issue $issue)
    {
        return new IssueResource($issue);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateIssueRequest $request, Issue $issue)
    {
        $issue->update($request->validated());
        
        return response()->json([
            'success' => true,
            'message' => 'Issue updated successfully.'
        ], Response::HTTP_ACCEPTED);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Issue $issue)
    {
        $issue->delete(); 
        // we need response for the popup notification
        return response()->json([
            'success' => true,
            'message' => 'Issue deleted successfully.'
        ], Response::HTTP_OK);
    }
}
