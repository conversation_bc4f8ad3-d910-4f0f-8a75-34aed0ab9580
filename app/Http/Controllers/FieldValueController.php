<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreFieldValueRequest;
use App\Http\Requests\UpdateFieldValueRequest;
use App\Models\Asset;
use App\Models\Enums\FieldEnum;
use App\Models\FieldValue;
use App\Services\AwsBucketService;
use App\Services\ValidationFieldService;
use App\Utilities\CycleAssetManager;
use Illuminate\Http\Response;

class FieldValueController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(AwsBucketService $awsBucketService)
    {
        $assetID = (int) request()->assetID;
        $cycleID = (int) request()->cycleID;

        // Find the cycle, which has a schema definition.
        $asset = Asset::find($assetID);
        $data = $asset->getFieldsWithValues($cycleID);

        foreach ($data as $fieldValue) {
            if ($fieldValue->field_type === FieldEnum::Image && $fieldValue->asset_image) {
                $fieldValue->asset_image->url = $awsBucketService->generateSignedUriGet($fieldValue->asset_image->path)['url'];
            }
        }

        return $data;
    }

    /**
     * Store a newly created resource in storage.
     * - field value must be created in order to report an issue
     * - field value must stay unique per asset/field/language so there is no multiple issues for the same field per asset
     */
    public function store(StoreFieldValueRequest $request, ValidationFieldService $validationService, CycleAssetManager $cycleAssetManager)
    {
        $validatedData = $request->validated();

        $fieldValue = FieldValue::where('field_id', $validatedData['field_id'])
            ->where('asset_id', $validatedData['asset_id'])
            ->where('language_id', $validatedData['language_id'] ?? null)
            ->first();

        if ($fieldValue) {
            $duplicatedAsset = $cycleAssetManager->ensureEditableAssetVersion($validatedData['asset_id'], $validatedData['cycle_id'], $fieldValue);
            if ($duplicatedAsset->id !== $fieldValue->asset_id) {
                $fieldValue = $fieldValue->replicate();
                $fieldValue->asset_id = $duplicatedAsset->id;
            }
        } else {
            $fieldValue = new FieldValue();
            $fieldValue->field_id = $validatedData['field_id'];
            $fieldValue->asset_id = $validatedData['asset_id'];
            $fieldValue->language_id = $validatedData['language_id'] ?? null;
        }

        $fieldValue->value = $validatedData['value'];
        $fieldValue->save();

        $error = $validationService->runValidation($fieldValue, $validatedData['cycle_id'], true);
        $success = $error == null ? true : false;

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Successfully created a new field value' : $error,
            'field' => $fieldValue,
        ], $success ? Response::HTTP_CREATED : Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Display the specified resource.
     */
    public function show(FieldValue $fieldValue)
    {
        //
    }

    /**
     * Update the specified resource in storage. 
     * @param UpdateFieldValueRequest $request
     * @param int $fieldDataID Identifier for FieldValue | AssetVideo | AssetAudio
     */
    public function update(
        UpdateFieldValueRequest $request,
        FieldValue $fieldValue,
        ValidationFieldService $validationService,
        CycleAssetManager $cycleAssetManager,
    ) {
        $validatedData = $request->validated();

        $duplicatedAsset = $cycleAssetManager->ensureEditableAssetVersion($fieldValue->asset_id, $validatedData['cycle_id'], $fieldValue);

        if ($duplicatedAsset->id !== $fieldValue->asset_id) {
            $fieldValue = $fieldValue->replicate();
            $fieldValue->asset_id = $duplicatedAsset->id;
        }
        $fieldValue->setValue($validatedData['value']);
        $fieldValue->save();

        $error = $validationService->runValidation($fieldValue, $validatedData['cycle_id'], true);
        $success = $error == null ? true : false;

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Field value updated successfully' : $error,
            'field' => $fieldValue
        ], $success ? Response::HTTP_ACCEPTED : Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FieldValue $fieldValue)
    {
        //
    }
}
