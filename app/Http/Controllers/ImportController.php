<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\Import;
use App\Jobs\ProcessImport;
use App\Models\Enums\ImportStatus;
use App\Http\Requests\{StoreImportRequest, UpdateImportRequest, ProccessImportRequest};
use App\Jobs\CategoryImport;

class ImportController extends Controller
{
    private const DESTINATION = '/imports/CSV/';

    public function index()
    {
        // top be revisited once we figure the UI
        $imports = Import::latest()->paginate(10);
        return response()->json($imports);
    }

    public function store(StoreImportRequest $request)
    {
        try {
            $file_info = $request['file_list'][0];
            $file_name = $file_info['name'];

            $fileExtension = pathinfo($file_name, PATHINFO_EXTENSION);
            if (strtolower($fileExtension) !== 'csv') {
                return response()->json([
                    'success' => false,
                    'message' => 'File must be a CSV file'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            $content = $request->input('file_content', '');

            Storage::put(self::DESTINATION . $file_name, $content);

            $import = Import::create([
                'file_name' => $file_name,
                'file_path' => self::DESTINATION . $file_name,
                'status' => ImportStatus::Pending,
                'errors' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Import file information recorded successfully',
                'data' => $import,
                'upload_url' => route('upload-imports', $import->id),
                'upload_details' => [
                    '_id' => $import->id,
                ],
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            Log::error(sprintf("Exception Error: %s", $e));
            return response()->json([
                'success' => false,
                'message' => 'Error processing import: ' . $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Update an existing import record.
     */
    public function update(UpdateImportRequest $request, Import $import): JsonResponse
    {
        $validated = $request->validated();

        if (isset($validated['status'])) {

            $status = $validated['status'];
            $allowedStatuses = ['Progress', 'Complete', 'Error'];

            if (!in_array($status, $allowedStatuses)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid status value'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            if ($status === 'Complete') {
                if (!Storage::exists($import->file_path) || Storage::size($import->file_path) == 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot mark as uploaded: File content is missing'
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }

                $file_contents = Storage::get($import->file_path);
                $temp_path = tempnam(sys_get_temp_dir(), 'import_');
                file_put_contents($temp_path, $file_contents);
                $handle = fopen($temp_path, 'r');
                if (!$handle) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid CSV file: File is empty or cannot be opened'
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                $headers = fgetcsv($handle);
                if (!$headers || empty($headers)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid CSV file: Cannot read headers'
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                fclose($handle);
            } else if ($status == 'Error') {
                return response()->json([
                    'success' => false,
                    'message' => 'Error while uploading the file'
                ], Response::HTTP_BAD_REQUEST);
            }
            return response()->json([
                'success' => true,
                'message' => 'Import status updated successfully',
                'data' => $import
            ], Response::HTTP_OK);
        }

        // file is might be replaced
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $fileName = $file->getClientOriginalName();

            if (Storage::exists($import->file_path)) {
                Storage::delete($import->file_path);
            }

            $filePath = Storage::put(self::DESTINATION, $file);

            $import->update([
                'file_name' => $fileName,
                'file_path' => $filePath,
                'processed_at' => null,
            ]);
        } elseif ($request->has('file_content')) {
            $fileContent = $request->input('file_content');

            Storage::put($import->file_path, $fileContent);
        }

        $updateData = collect($validated)
            ->except(['file', 'file_content', 'status'])
            ->toArray();

        if (!empty($updateData)) {
            $import->update($updateData);
        }

        return response()->json([
            'success' => true,
            'message' => 'Import updated successfully',
            'data' => $import
        ], Response::HTTP_OK);
    }

    /**
     * Upload the actual file content for an existing import.
     */
    public function upload(Request $request, Import $import): JsonResponse
    {
        try {
            // Get file content from request
            $fileContent = $request->getContent();

            if (empty($fileContent)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No file content provided'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            // Store the actual file content
            Storage::put($import->file_path, $fileContent);

            // Update the import status
            $import->update([
                'status' => 'uploaded',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File content uploaded successfully',
                'data' => $import
            ]);
        } catch (\Exception $e) {
            Log::error(sprintf("Exception Error: %s", $e));
            return response()->json([
                'success' => false,
                'message' => 'Error uploading file content: ' . $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    public function process(ProccessImportRequest $request, Import $import): JsonResponse
    {
        $validated = $request->validated();

        $validated['asset_type'] === 'category' ? 
            CategoryImport::dispatchSync($import, $validated['cycle_id']) 
            : ProcessImport::dispatchSync($import, $validated['cycle_id'], $validated['asset_type']);

        $import->refresh();

        return response()->json([
            'success' => true,
            'data' => $import
        ], Response::HTTP_OK);
    }
}
