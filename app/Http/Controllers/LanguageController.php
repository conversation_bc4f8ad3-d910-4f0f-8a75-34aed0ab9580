<?php

namespace App\Http\Controllers;

use App\Http\Requests\{StoreLanguageRequest, UpdateLanguageRequest};
use App\Models\Language;

class LanguageController extends Controller
{
    public function index()
    {
        $languages = Language::all();
        return response()->json($languages, 200);
    }

    public function store(StoreLanguageRequest $request)
    {
        $validatedData = $request->validated();
        $language = Language::create($validatedData);

        return response()->json($language, 201);
    }

    public function update(UpdateLanguageRequest $request, Language $language)
    {
        $validatedData = $request->validated();
        $language->update($validatedData);

        return response()->json($language);
    }
}
