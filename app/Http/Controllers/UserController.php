<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Models\User;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class UserController extends Controller
{
    public function index()
    {

        // return a minimum amount of data.
        $query = User::query()->select(
            'id',
            'name',
            'role_id',
            'email',
            'organization_id'
        )->with([
            'role:id,name',
            'organization:id,name'
        ]);

        if (request()->filled('search')) {
            $search = '%' . request()->get('search') . '%';
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', $search)
                  ->orWhere('email', 'like', $search);
            });
        }

        $query->when(
            !auth()->user()->has_unrestricted_edit_role,
            fn ($query) => $query->where('id', auth()->user()->id)
        );

        return $query->paginate(1000); // todo: return all users for now, but return the paginated structure.
    }

    public function register(StoreUserRequest $request)
    {
        Gate::authorize('create', User::class);

        $validatedData = $request->validated();

        $user = User::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'password' => bcrypt($validatedData['password']),
            'organization_id' => $validatedData['organization_id'],
        ]);

        return response()->json(['message' => 'User registered successfully', 'user' => $user], Response::HTTP_CREATED);
    }


    public function show(User $user)
    {
        Gate::authorize('view', $user);

        return $user->load('role');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        Gate::authorize('create', User::class);

        $validatedData = $request->validated();
        $user = User::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'password' => bcrypt($validatedData['password']),
            'organization_id' => $validatedData['organization_id'],
            'role_id' => $validatedData['role_id'],
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new user',
            'user' => $user
        ], Response::HTTP_CREATED);
    }

    public function update(UpdateUserRequest $request, $id)
    {
        $user = User::findOrFail($id);
        $validatedData = $request->validated();

        Gate::authorize('update', [$user, $validatedData]);

        if (isset($validatedData['password'])) {
            $validatedData['password'] = bcrypt($validatedData['password']);
        }

        $user->update($validatedData);

        return response()->json(['message' => 'User updated successfully', 'user' => $user]);
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);

        Gate::authorize('delete', $user);

        $user->delete();

        return response()->json(['message' => 'User deleted successfully'], Response::HTTP_NO_CONTENT);
    }
}
