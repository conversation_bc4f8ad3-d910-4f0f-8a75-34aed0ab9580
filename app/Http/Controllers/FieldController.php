<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreFieldRequest;
use App\Http\Requests\UpdateFieldRequest;
use App\Http\Resources\FieldCollection;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Field;
use App\Utilities\CycleAssetManager;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        Gate::authorize('viewAny', Field::class);

        $schemaID = (int) request()->schemaID;
        $fields = Field::when($schemaID, function ($query) use ($schemaID) {
            return $query->where('schema_id', $schemaID);
        })->with(['validatorFields' => fn ($query) => $query->where('enabled', true)->with('validator')])->get();

        return new FieldCollection($fields);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFieldRequest $request)
    {
        Gate::authorize('create', Field::class);

        $field = Field::create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new field',
            'field' => $field
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Field $field)
    {
        Gate::authorize('view', $field);

        return $field;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFieldRequest $request, Field $field, CycleAssetManager $cycleAssetManager)
    {
        Gate::authorize('update', $field);
        $isUpdate = true;
        $cycleAssetManager->ensureEditableAssetVersionByField($field, $isUpdate);

        // There can be only 1 key field for any particular asset type
        // Due to the way the data is stored in the DB a unique index is not possible, so it is enforced in code.
        $data = $request->validated();
        if (isset($data['is_external_api_key_field']) && $data['is_external_api_key_field']) {
            $assetTypes = collect($field->asset_types);

            // if no asset types are set, then this field is for all asset types
            if ($assetTypes->count() === 0) {
                $assetTypes = array_map(fn ($assetType) => $assetType->value, AssetTypeEnum::cases());
            }

            foreach ($assetTypes as $type) {
                $keyFieldName = Field::query()
                    ->whereNot('id', $field->id)
                    ->where('schema_id', $field->schema_id)
                    ->where(
                        fn ($query) =>
                            $query->where('asset_types', 'like', "%{$type}%")
                                // since empty asset_types means "all asset types"
                                ->orWhere('asset_types', '[]')
                                ->orwhere('asset_types', null)
                    )
                    ->where('is_external_api_key_field', true)
                    ->first()
                    ?->name;

                if ($keyFieldName) {
                    return abort(400, "Asset Type '{$type}' already has a key field '{$keyFieldName}'. Remove it before setting a new key field for this Asset Type.");
                }
            }
        }

        $field->update($data);


        return response()->json([
            'success' => true,
            'message' => 'Field updated successfully',
            'field' => $field
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Field $field, CycleAssetManager $cycleAssetManager)
    {
        Gate::authorize('delete', $field);

        $cycleAssetManager->ensureEditableAssetVersionByField($field);
        $field->delete();
        return response()->json([
            'success' => true,
            'message' => 'Field deleted successfully'
        ], Response::HTTP_NO_CONTENT);
    }
}
