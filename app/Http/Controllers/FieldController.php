<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreFieldRequest;
use App\Http\Requests\UpdateFieldRequest;
use App\Http\Resources\FieldCollection;
use App\Models\Field;
use App\Utilities\CycleAssetManager;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        Gate::authorize('viewAny', Field::class);

        $schemaID = (int) request()->schemaID;
        $fields = Field::when($schemaID, function ($query) use ($schemaID) {
            return $query->where('schema_id', $schemaID);
        })->with(['validatorFields' => fn ($query) => $query->where('enabled', true)->with('validator')])->get();

        return new FieldCollection($fields);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFieldRequest $request)
    {
        Gate::authorize('create', Field::class);

        $field = Field::create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new field',
            'field' => $field
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Field $field)
    {
        Gate::authorize('view', $field);

        return $field;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFieldRequest $request, Field $field)
    {
        Gate::authorize('update', $field);

        $field->update($request->validated());
        return response()->json([
            'success' => true,
            'message' => 'Field updated successfully',
            'field' => $field
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Field $field, CycleAssetManager $cycleAssetManager)
    {
        Gate::authorize('delete', $field);

        $cycleAssetManager->duplicateAssetByField($field);
        $field->delete();
        return response()->json([
            'success' => true,
            'message' => 'Field deleted successfully'
        ], Response::HTTP_NO_CONTENT);
    }
}
