<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCollectionValueRequest;
use App\Http\Requests\UpdateCollectionValueRequest;
use App\Models\CollectionValue;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class CollectionValueController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $collectionID = request()->collectionID;

        if (request()->languageID) {
            return $this->getLocalizedLabels($collectionID);
        }

        return CollectionValue::when($collectionID, function ($query) use ($collectionID) {
            return $query->where('collection_id', $collectionID);
        })->get();
    }

    private function getLocalizedLabels($collectionID)
    {
        return CollectionValue::where('collection_id', $collectionID)
            ->where('language_id', request()->languageID)
            ->whereNotNull('collection_value_id')
            ->get()
            ->each(function ($item) {
                $item->id = $item->collection_value_id;
            });
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCollectionValueRequest $request)
    {
        $request_values = $request->validated();
        Gate::authorize('create', [CollectionValue::class, $request_values['collection_id']]);

        // If there's a language ID present, we never want to overwrite an existing collection value... so double check.
        // Otherwise, we can create a new one.
        if (request()->has('language_id')) {
            $cv = CollectionValue::firstOrCreate($request->validated());
        }
        else {
            $cv = CollectionValue::create($request->validated());
        }

        return response()->json([
            'success' => true,
            'message' => $cv->wasRecentlyCreated ? 'Successfully created a new list item' : 'Successfully updated a list item',
            'collection_value' => $cv
        ], $cv->wasRecentlyCreated ? Response::HTTP_CREATED : Response::HTTP_OK);

    }

    /**
     * Display the specified resource.
     */
    public function show(CollectionValue $collectionValue)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CollectionValue $collectionValue)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCollectionValueRequest $request, CollectionValue $collectionValue)
    {
        Gate::authorize('update', $collectionValue);

        $collectionValue->update($request->validated());
        return response()->json([
            'success' => true,
            'message' => 'List item updated successfully',
            'collection_value' => $collectionValue
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CollectionValue $collectionValue)
    {
        Gate::authorize('delete', $collectionValue);

        $collectionValue->delete();
        return response()->json([
            'success' => true,
            'message' => 'List item deleted successfully'
        ], Response::HTTP_OK);
    }
}
