<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreFieldLabelRequest;
use App\Http\Requests\UpdateFieldLabelRequest;
use App\Models\FieldLabel;
use Illuminate\Support\Facades\Gate;

class FieldLabelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        Gate::authorize('viewAny', FieldLabel::class);

        $fieldID = request()->fieldID;
        return FieldLabel::when($fieldID, function ($query) use ($fieldID) {
            return $query->where('field_id', $fieldID);
        })->get();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFieldLabelRequest $request)
    {
        Gate::authorize('create', FieldLabel::class);

        $fieldLabel = FieldLabel::create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new field label',
            'fieldLabel' => $fieldLabel
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(FieldLabel $fieldLabel)
    {
        Gate::authorize('view', $fieldLabel);

        return $fieldLabel;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFieldLabelRequest $request, FieldLabel $fieldLabel)
    {
        Gate::authorize('update', $fieldLabel);

        $fieldLabel->update($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Field label updated successfully',
            'fieldLabel' => $fieldLabel
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FieldLabel $fieldLabel)
    {
        //
    }
}
