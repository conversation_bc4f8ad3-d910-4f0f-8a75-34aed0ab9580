<?php


namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash as Hash;
use Illuminate\Validation\ValidationException;

class TokenController extends Controller
{

    public function signIn(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if (! $user || ! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        return response()->json([
            'token' => $user->createToken(request()->headers->get('referer', 'no_referer'))->plainTextToken
        ]);
    }

    public function signOut()
    {
        $user = Auth::guard('sanctum')->user();
        if ($user) {
            // delete the active token.
            $user->currentAccessToken()->delete();
            // return something useful to the UI which would cause it to redirect to sign in page.
            return ['token' => null];
        }
        return ['no-auth'];
    }


    public function signOutAll()
    {
        $user = Auth::guard('sanctum')->user();
        if ($user) {
            // delete all the user's tokens
            $user->tokens()->delete();
            return ['token' => null];
        }
        return ['no-auth'];
    }
}
