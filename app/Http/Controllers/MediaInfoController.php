<?php

namespace App\Http\Controllers;

use App\Models\Asset;
use App\Models\Enums\AssetTypeEnum;
use App\Services\ViasatService;

class MediaInfoController extends Controller
{
    public function getMediaInfoFiles()
    {
        if (isset(request()->fileType)) {
            return $this->getMediaInfoFilesByFileType(request()->fileType);
        }
        else if (isset(request()->assetID)) {
            return $this->getMediaInfoFilesByAssetID(request()->assetID);
        }
        return response()->json(['error' => 'File type or asset ID is required'], 400);    
    }

    private function getMediaInfoFilesByFileType(string $fileType)
    {
        return (new ViasatService)->getMediaInfoFiles($fileType);
    }

    private function getMediaInfoFilesByAssetID(int $assetID)
    {
        // Find the asset then get the file type.
        $asset = Asset::find($assetID);

        $fileType = AssetTypeEnum::from($asset->asset_type)->fileType();

        return $this->getMediaInfoFilesByFileType($fileType);
    }
}
