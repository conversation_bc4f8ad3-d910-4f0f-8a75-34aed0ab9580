<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAssetImageRequest;
use App\Http\Requests\UpdateAssetImageRequest;
use App\Models\AssetImage;
use App\Models\Cycle;
use App\Services\AwsBucketService; 
use App\Services\ValidationFieldService; 
use Illuminate\Http\Response;  


// api endpoints for manual asset image actions
class AssetImageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->search;
        $currentPage = (int) (request()->pagination['currentPage'] ?? 1);
        $rowsPerPage = (int) (request()->pagination['rowsPerPage'] ?? 25);
        $airlineID = request()->airlineID;
        $cycleID = request()->cycleID;
        $type = request()->type;

        $assetImages = AssetImage::with(['field', 'asset'])
            ->when($search, function ($query) use ($search) {
                $query->where('file_name', 'like', "%{$search}%");
            })
            ->when($type, function ($query) use ($type) {
                if ($type === 'all') {
                    return;
                }
                else if ($type === 'matched') {
                    $query->whereNotNull('field_id')
                        ->whereNotNull('asset_id');
                }
                else if ($type === 'unmatched') {
                    $query->whereNull('field_id')
                        ->whereNull('asset_id');
                }
                else if ($type === 'pending') {
                    $query->where('status', 'pending');
                }
                else if ($type === 'uploaded') {
                    $query->where('status', 'uploaded');
                }
            })
            ->when($airlineID, function ($query) use ($airlineID) {
                $query->where('airline_id', $airlineID);
            })
            ->when($cycleID, function ($query) use ($cycleID) {
                $query->where('cycle_id', $cycleID);
            })
            ->orderBy(request()->input('sorting.key', 'created_at'), request()->input('sorting.direction', 'desc'));
    
        $total = $assetImages->count();
        $assetImages->skip(($currentPage - 1) * $rowsPerPage)->take($rowsPerPage);

        return response()->json([
            'success' => true,
            'message' => 'Asset images retrieved successfully.',
            'data' => [
                'data' => $assetImages->get(),
                'total' => $total,
                'current_page' => $currentPage,
                'rows_per_page' => $rowsPerPage
            ]
        ]);

    }

    /**
     * Generate S3 signed uri for storing an asset image file.
     */
    public function store(StoreAssetImageRequest $request)
    {  
        $validatedData = $request->validated(); 

        // Find an existing image, it's possible this should be an override.
        if (AssetImage::where('file_name', $validatedData['file_name'])
            ->where('airline_id', $validatedData['airline_id'])
            ->where('cycle_id', $validatedData['cycle_id'])
            ->first()) {
            return response()->json([
                'success' => false,
                'message' => 'This file name already exists. Please use a different file name or delete the existing asset image and try again.'
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        // Create our new asset image...
        $assetImage = AssetImage::firstOrCreate($validatedData);

        return response()->json([
            'success' => true,
            'message' => 'Created new asset image successfully. Generated a signed upload URL for asset image.',
            'asset_image' => $assetImage,
            'signed_upload_url' => $assetImage->getSignedUploadURL()
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetImage $assetImage, AwsBucketService $awsBucketService, ValidationFieldService $validationService)
    { 
        try {
            $assetImage->withSignedDownloadURL();
            $assetImage->load(['field', 'asset']);
  
            return response()->json([
                'success' => true,
                'message' => 'Found image asset and signed URL successfully.',
                'asset_image' => $assetImage
            ], Response::HTTP_OK);
        }
        catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create signed URL for asset image.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * User always using post method
     */
    public function update(UpdateAssetImageRequest $request, AssetImage $assetImage, AwsBucketService $awsBucketService)
    {
        // Update the asset image with the new data.
        $assetImage->update($request->validated());

        // If we are updating the file name and uploading a new file, we need to generate a new signed URL.
        if (isset($request->validated()['file_name'])) {

            // Then generate a new signed URL for the front-end to upload.
            $signedUploadURL = $assetImage->getSignedUploadURL();
        }
        else {

            // Otherwise we should generate a new download URL.
            $assetImage->withSignedDownloadURL();
        }

        return response()->json([
            'success' => true,
            'message' => 'Updated asset image.',
            'asset_image' => $assetImage,
            'signed_upload_url' => $signedUploadURL ?? null
        ], Response::HTTP_OK);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetImage $assetImage, AwsBucketService $awsBucketService, ValidationFieldService $validationService)
    {
        // Really all we do is delete the file in S3 then delete this image.
        if ($assetImage->path && !$awsBucketService->deleteObject($assetImage->path)) {

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete asset image.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        // We deleted the object, so let's delete the model.
        $assetImage->delete();

        return response()->json([
            'success' => true,
            'message' => 'Asset image deleted successfully.',
        ], Response::HTTP_OK);
    }
}
