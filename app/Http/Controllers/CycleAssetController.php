<?php

namespace App\Http\Controllers;

use App\Models\{Asset, Cycle};
use App\Models\Enums\AssetTypeEnum;
use App\Services\ValidationAssetService;
use App\Http\Requests\StoreAssetRequest;
use App\Http\Requests\UpdateAssetRequest;
use App\Http\Requests\AttachAssetsRequest;
use App\Http\Resources\CycleAssetCollection;
use Illuminate\Http\Response;
use App\Models\Enums\AssetStatus;
use App\Models\Enums\ValidatorSeverityEnum;
use App\Utilities\CycleAssetManager;
use Illuminate\Support\Facades\Gate;

class CycleAssetController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Cycle $cycle)
    {
        $search = request()->search;
        $assetType = request()->assetType;
        $filter = request()->filter;
        $perPage = (int) request()->input('pagination.rowsPerPage', 10);
        $page = (int) request()->input('pagination.currentPage', 0) + 1;

        $assets = $cycle->assets()
            ->when($assetType, fn($query) => $query->where('asset_type', $assetType))
            ->when($search, fn($query) => $query->where('title', 'like', "%{$search}%"))
            ->when($filter, fn($query) => $query->when($filter['type'], fn($q) => $q->whereIn('status', explode(',', $filter['type']))))
            ->withCount([
                'issues as errors' => fn($query) =>
                $query->whereHas('validatorField', fn($query) => $query->where('severity', ValidatorSeverityEnum::Error->value))
            ])
            ->withCount([
                'issues as warnings' => fn($query) =>
                $query->whereHas('validatorField', fn($query) => $query->where('severity', ValidatorSeverityEnum::Warning->value))
            ])
            ->withPivot('status')
            ->with(['childAssets' => function ($query) use ($cycle) {
                $query->with(['cycles' => function ($cycleQuery) use ($cycle) {
                    $cycleQuery->where('cycles.id', $cycle->id)->withPivot('status');
                }]);
            }])
            ->paginate($perPage, ['*'], 'page', $page);

        return new CycleAssetCollection($assets);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAssetRequest $request, Cycle $cycle, ValidationAssetService $validationService)
    {
        $data = $request->validated();
        $asset = Asset::create(array_merge($data, [
            'start_date' => $cycle->start_date,
            'end_date' => $cycle->end_date,
        ]));
        $validationService->validateAssetFields($asset, $cycle);

        // TODO: verify asset is not blocked
        $cycle->assets()->attach($asset->id, [
            'status' => AssetStatus::New
        ]);
        $asset->status = AssetStatus::New->value;

        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new asset',
            'asset' => $asset
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Cycle $cycle, Asset $asset)
    {
        $cycleAsset = $cycle->assets()
            ->where('assets.id', $asset->id)
            ->withPivot('status')
            ->with(['childAssets' => function ($query) use ($cycle) {
                $query->with(['cycles' => function ($cycleQuery) use ($cycle) {
                    $cycleQuery->where('cycles.id', $cycle->id)->withPivot('status');
                }]);
            }])
            ->with(['parentAsset', 'categories' => fn($q) => $q->with('categoryValues')])
            ->firstOrFail();
        $cycleAsset->withCompletionPercentage($cycle);
        $cycleAsset->status = $cycleAsset->pivot->status;
        $cycleAsset->childAssets->each(function ($childAsset) use ($cycle) {
            $childAsset->status = $childAsset->cycles->first()->pivot->status;
            $childAsset->withCompletionPercentage($cycle);
        });

        return $cycleAsset;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        UpdateAssetRequest $request,
        Cycle $cycle,
        Asset $asset,
        ValidationAssetService $validationService,
        CycleAssetManager $cycleAssetManager
    ) {
        $data = $request->validated();
        Gate::authorize('update', [$asset, $data]);

        $start_date_changed = isset($data['start_date']) && $data['start_date'] !== $asset->start_date;
        $end_date_changed = isset($data['end_date']) && $data['end_date'] !== $asset->end_date;

        if ($start_date_changed || $end_date_changed) {
            $asset = $cycleAssetManager->ensureEditableAssetVersion($asset->id, $cycle->id);
        }

        if (isset($data['asset_type']) && $data['asset_type'] != $asset->asset_type) {
            return response()->json([
                'success' => false,
                'error' => 'Asset type cannot be changed'
            ], Response::HTTP_BAD_REQUEST);
        }
        $asset->update($data);

        $validationService->validateAssetFields($asset, $cycle);

        $asset->withCompletionPercentage($cycle);
        // TODO: verify asset is not blocked

        return response()->json([
            'success' => true,
            'message' => 'Asset updated successfully',
            'asset' => $asset
        ], Response::HTTP_ACCEPTED);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Cycle $cycle, Asset $asset)
    {
        // Detach the asset from the cycle
        $cycle->assets()->detach($asset->id);
        $asset->delete();
        return response()->noContent(); // no response body required, status 204
    }

    public function attachAssets(Cycle $cycle, Asset $asset, AttachAssetsRequest $request)
    {
        $data = $request->validated();

        $ids = $data['ids'];
        $type = $data['asset_type'];

        $enumType = AssetTypeEnum::contains($type);
        if (!$enumType || !$enumType->isSecondLevel()) {
            $topLevelTypes = collect(AssetTypeEnum::cases())
                ->filter(fn($type) => $type->isTopLevel())
                ->map(fn($type) => $type->label())
                ->implode(', ');
            return response()->json([
                'success' => false,
                'error' => 'Failed to add assets, asset type ' . $enumType->value . ' should be one of: ' . $topLevelTypes
            ], Response::HTTP_BAD_REQUEST);
        }

        if ($asset->asset_type != $enumType->getParentType()->value) {
            return response()->json([
                'success' => false,
                'error' => 'Asset type, ' . $enumType->getParentType()->value . ', cannot be added to ' . $asset->asset_type
            ], Response::HTTP_BAD_REQUEST);
        }
        $res = Asset::whereIn('id', $ids)->update(['parent_asset_id' => $asset->id]);
        if ($res == 0) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to add assets'
            ], Response::HTTP_BAD_REQUEST);
        }
        return response()->json([
            'success' => true,
            'message' => 'Assets added successfully.',
            'asset' => $asset,
        ], Response::HTTP_OK);
    }
}
