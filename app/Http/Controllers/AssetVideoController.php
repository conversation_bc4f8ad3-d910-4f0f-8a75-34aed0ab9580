<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use App\Utilities\CycleAssetManager;
use App\Services\ValidationFieldService;
use App\Http\Requests\StoreAssetVideoRequest;
use App\Http\Requests\UpdateAssetVideoRequest;
use App\Models\AssetVideo;
use App\Models\Cycle;
use App\Services\ViasatService;

class AssetVideoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(
        StoreAssetVideoRequest $request,
        ValidationFieldService $validationService,
        ViasatService $viasatService
    ) {
        $validatedData = $request->validated();

        $fileInfo = $viasatService->getFileInfo('video', $validatedData['value']);

        $assetVideo = AssetVideo::updateOrCreate([
            'cycle_id' => $validatedData['cycle_id'],
            'airline_id' => Cycle::find($validatedData['cycle_id'])->airline_id,
            'field_id' => $validatedData['field_id'],
            'asset_id' => $validatedData['asset_id'],
        ], [
            'file_name' => $validatedData['value'],
            'size' => $fileInfo['size'],
            'viasat_id' => $fileInfo['viasat_id'],
        ]);

        $error = $validationService->runValidation($assetVideo, $validatedData['cycle_id'], true);
        $success = $error == null ? true : false;

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Successfully saved video field value' : $error,
            'field' => $assetVideo,
        ], $success ? Response::HTTP_CREATED : Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        UpdateAssetVideoRequest $request,
        AssetVideo $assetVideo,
        ValidationFieldService $validationService,
        CycleAssetManager $cycleAssetManager,
        ViasatService $viasatService
    ) {
        $validatedData = $request->validated();
        $fileInfo = $viasatService->getFileInfo('video', $validatedData['value']);

        $duplicatedAsset = $cycleAssetManager->ensureEditableAssetVersion($assetVideo->asset_id, $validatedData['cycle_id'], $assetVideo);

        if ($duplicatedAsset->id !== $assetVideo->asset_id) {
            $assetVideo = $assetVideo->replicate();
            $assetVideo->asset_id = $duplicatedAsset->id;
        }
        $assetVideo->setValue($validatedData['value']);
        $assetVideo->size = $fileInfo['size'];
        $assetVideo->viasat_id = $fileInfo['viasat_id'];
        $assetVideo->save();

        $error = $validationService->runValidation($assetVideo, $validatedData['cycle_id'], true);
        $success = $error == null ? true : false;

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Video field value updated successfully' : $error,
            'field' => $assetVideo
        ], $success ? Response::HTTP_ACCEPTED : Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
