<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreValidatorFieldTypeRequest;
use App\Http\Resources\ValidatorFieldTypeCollection;
use App\Http\Resources\ValidatorFieldTypeResource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\ValidatorFieldType;
use App\Models\Validator;
use Illuminate\Support\Facades\Gate;

class ValidatorFieldTypeController extends Controller
{
    /**
     * List all available validators and map their field types as enabled|disabled from the database.
     */
    public function index()
    {
        Gate::authorize('viewAny', ValidatorFieldType::class);

        $perPage = (int) request()->input('per_page', 10);
        $page = (int) request()->input('page', 0) + 1;
        $assetValidators = (bool) request()->input('asset_validators', false);

        $validators = Validator::with(['ValidatorFieldType'])
            ->when($assetValidators, fn ($q) => $q->whereNotNull('group'), fn ($q) => $q->whereNull('group'))
            ->paginate($perPage, ['*'], 'page', $page);

        return new ValidatorFieldTypeCollection($validators);
    }

    /**
     * Update validator status on Toggele button click
     * Stores or updates the `enable` flag for the selected field type and validator
     * db record must have unique validator_id and field_type pair
     */
    public function store(StoreValidatorFieldTypeRequest $request)
    {
        $validatedData = $request->validated();

        Gate::authorize('create', ValidatorFieldType::class);

        $validatorFieldType = ValidatorFieldType::firstOrNew([
            'validator_id' => $validatedData['validator_id'],
            'field_type' => $validatedData['field_type']
        ]);

        $validatorFieldType->enabled = $validatedData['enabled'];
        $validatorFieldType->save();

        return response()->json([
            'success' => true,
            'data' => new ValidatorFieldTypeResource($validatorFieldType),
            'message' => 'Validator Field Type entry saved successfully.'
        ], $validatorFieldType->wasRecentlyCreated ? Response::HTTP_CREATED : Response::HTTP_OK);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
