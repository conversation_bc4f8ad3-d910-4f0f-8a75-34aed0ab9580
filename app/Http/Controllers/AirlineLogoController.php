<?php

namespace App\Http\Controllers;

use App\Models\Airline;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class AirlineLogoController extends Controller
{
    private const DESTINATION = '/airlines/images/';

    public function storeAirlineLogo(Request $request, Airline $airline)
    {
        try {
            // Get file content from request
            $fileContent = $request->getContent();

            if (empty($fileContent)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No file content provided'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $imageInfo = getimagesizefromstring($fileContent);
            if (!$imageInfo) {
                return response()->json(['status' => 'failed', 'message' => 'Invalid image format'], Response::HTTP_BAD_REQUEST);
            }
            $image = imagecreatefromstring($fileContent);
            if (!$image) {
                return response()->json(['success' => false, 'message' => 'Unable to process image'], Response::HTTP_BAD_REQUEST);
            }
            $success = false;
            // Check if the image is of a supported type
            switch ($imageInfo[2]) {
                case IMAGETYPE_GIF:
                    ob_start();
                    $success = imagegif($image);
                    ob_end_clean();
                    $ext = '.gif';
                    break;
                case IMAGETYPE_JPEG:
                    ob_start();
                    $success = imagejpeg($image);
                    ob_end_clean();
                    $ext = '.jpg';
                    break;
                case IMAGETYPE_PNG:
                    ob_start();
                    $success = imagepng($image);
                    ob_end_clean();
                    $ext = '.png';
                    break;
                default:
                    return response()->json(['success' => false, 'message' => 'Only JPEG, PNG and GIF formats are supported'], Response::HTTP_BAD_REQUEST);
                    break;
            }
            // Check if the image was successfully created
            if (!$success) {
                return response()->json(['success' => false, 'message' => 'Unable to create image'], Response::HTTP_BAD_REQUEST);
            }
            $storageImagePath = self::DESTINATION . 'airline_' . $airline->id . $ext;
            // Store the actual file content
            Storage::put($storageImagePath, $fileContent);

            $airline->update(['logo_url' => $storageImagePath]);

            // Clean up image resource
            imagedestroy($image);

            return response()->json([
                'success' => true,
                'message' => 'File content uploaded successfully',
                'data' => $airline
            ]);
        } catch (\Exception $e) {
            Log::error(sprintf("Exception Error: %s", $e));
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading the file: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
