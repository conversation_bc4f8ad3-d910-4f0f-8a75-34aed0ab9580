<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreOrganizationRequest;
use App\Http\Requests\UpdateOrganizationRequest;
use App\Models\Organization;
use Illuminate\Support\Facades\Gate;

class OrganizationController extends Controller
{
    public function index()
    {
        $organizations = Organization::forUser(request()->user())->get();
        return response()->json($organizations, 200);
    }

    public function show($id)
    {
        $organization = Organization::with(['users', 'airlines'])->find($id);
        if ($organization) {
            Gate::authorize('view', $organization);

            return response()->json($organization, 200);
        }
        else {
            return response()->json(['message' => 'Organization not found'], 404);
        }
    }

    public function store(StoreOrganizationRequest $request)
    {
        Gate::authorize('create', Organization::class);

        $organization = Organization::create($request->all());
        return response()->json([
            'organization' => $organization,
            'message' => 'Organization created successfully'
        ], 201);
    }

    public function update(UpdateOrganizationRequest $request, $id)
    {
        $organization = Organization::find($id);

        Gate::authorize('update', $organization);

        if (!$organization) {
            return response()->json(['message' => 'Organization not found'], 404);
        }

        $updateData = $request->except('airlines');
        $organization->update($updateData);

        if ($request->has('airlines')) {
            $airlineIDs = $request->input('airlines');
            if (is_array($airlineIDs)) {
                $organization->airlines()->sync($airlineIDs);
            }
        }

        $organization->load(['users', 'airlines']);

        return response()->json([
            'organization' => $organization,
            'message' => 'Organization updated successfully'
        ], 200);
    }

    public function destroy($id)
    {
        $organization = Organization::find($id);

        Gate::authorize('delete', $organization);

        if ($organization) {
            $organization->delete();
            return response()->json(null, 204);
        } else {
            return response()->json(['message' => 'Organization not found'], 404);
        }
    }
}
