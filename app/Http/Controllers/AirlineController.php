<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use App\Models\Airline;
use App\Models\Language;
use App\Http\Requests\StoreAirlineRequest;
use App\Http\Requests\UpdateAirlineRequest;
use Illuminate\Support\Facades\Gate;

class AirlineController extends Controller
{
    public function index()
    {
        $search = request()->search;

        return Airline::forUser(request()->user())
            ->when($search, function ($query) use ($search) {
                return $query->where('name', 'like', "%{$search}%");
            })->with([
                'languages',
                'cycles' => function ($query) {
                    $query->orderBy('created_at', 'desc');
                }
            ])->get();
    }

    public function store(StoreAirlineRequest $request)
    {
        Gate::authorize('create', Airline::class);

        $airline = Airline::create(array_merge(
            $request->validated(),
            ['organization_id' => auth()->user()->organization_id]
        ));
        return response()->json([
            'success' => true,
            'message' => 'Airline created successfully',
            'airline' => $airline
        ], Response::HTTP_CREATED);
    }

    public function show($id)
    {
        // Eager load languages for a specific airline
        $airline = Airline::with('languages')->findOrFail($id);

        Gate::authorize('view', $airline);

        return response()->json($airline);
    }

    public function update(UpdateAirlineRequest $request, Airline $airline)
    {
        Gate::authorize('update', $airline);

        $airline->update($request->validated());
        // Handle languages if they are included in the request
        if ($request->has('languages')) {
            $languageIds = is_array($request->input('languages'))
                ? $request->input('languages')
                : explode(',', $request->input('languages'));

            // Remove any empty or null values
            $languageIds = array_filter($languageIds);
            if (!empty($languageIds)) {
                // Validate and sync languages
                $validLanguageIds = Language::whereIn('id', $languageIds)->pluck('id');
                $airline->languages()->sync($validLanguageIds);
            }
        }

        // if default language id is no longer in the list of languages, clear out the default language id
        if (array_search($airline->default_language_id, $airline->languages()->get()->pluck('id')->toArray()) === false) {
            $airline->default_language_id = null;
            $airline->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Airline updated successfully',
            'airline' => $airline,
            'languages' => $airline->languages
        ]);
    }

    public function destroy(Airline $airline)
    {
        Gate::authorize('delete', $airline);

        $airline->delete();
        return response()->json([
            'success' => true,
            'message' => 'Airline deleted successfully'
        ], Response::HTTP_NO_CONTENT);
    }
}
