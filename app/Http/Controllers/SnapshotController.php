<?php

namespace App\Http\Controllers;

use App\Jobs\GenerateSnapshot;
use App\Models\{Snapshot, Cycle};
use Illuminate\Http\Request;

class SnapshotController extends Controller
{
    //
    public function index()
    {
        return Snapshot::where('cycle_id', request()->cycle_id)->with(['user'])->latest()->paginate(50);
    }

    public function store()
    {
        // todo: when roles and access are in, make sure whoever is triggering this is allowed to!
        $cycleID = request()->post('cycle_id');

        $cycle = Cycle::findOrFail($cycleID)->append('has_errors');


        // Grab the user from the request
        $snapshotQueue = Snapshot::create([
            'cycle_id' => $cycleID,
            'user_id' => auth()->user()->id,
            'generated_with_errors' => $cycle->has_errors
        ]);

        GenerateSnapshot::dispatch($cycleID, $snapshotQueue->id);
    }

    public function destroy(Snapshot $snapshot)
    {
        $snapshot->delete();
        return response()->json(['message' => 'Snapshot deleted successfully.']);
    }
}
