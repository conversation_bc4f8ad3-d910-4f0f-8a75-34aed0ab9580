<?php

namespace App\Http\Controllers;

use App\Jobs\GenerateSnapshot;
use App\Models\Snapshot;
use Illuminate\Http\Request;

class SnapshotController extends Controller
{
    //
    public function index()
    {
        return Snapshot::where('cycle_id', request()->cycle_id)->latest()->paginate(50);
    }

    public function store()
    {
        // todo: when roles and access are in, make sure whoever is triggering this is allowed to!
        $cycleId = request()->post('cycle_id');

        $snapshotQueue = Snapshot::create([
            'cycle_id' => $cycleId,
            'user_id' => auth()->user()->id,
        ]);

        GenerateSnapshot::dispatch($cycleId, $snapshotQueue->id);
    }

    public function destroy(Snapshot $snapshot)
    {
        $snapshot->delete();
        return response()->json(['message' => 'Snapshot deleted successfully.']);
    }
}
