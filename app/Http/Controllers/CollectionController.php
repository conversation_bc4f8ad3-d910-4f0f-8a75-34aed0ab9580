<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCollectionRequest;
use App\Http\Requests\UpdateCollectionRequest;
use App\Models\Collection;
use App\Models\CollectionValue;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class CollectionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        Gate::authorize('viewAny', Collection::class);

        $request = request();
        $schemaID = $request->schemaID;
        $search = $request->search;

        return Collection::when($schemaID, function ($query) use ($schemaID) {
            $query->where('schema_id', $schemaID);
        })
            ->when($search, function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy(request()->sortKey ?? 'name', request()->sortDirection ?? 'asc')
            ->get();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCollectionRequest $request)
    {
        Gate::authorize('create', Collection::class);

        $collection = Collection::create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new list',
            'collection' => $collection
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Collection $collection)
    {
        Gate::authorize('view', $collection);

        return $collection;
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Collection $collection)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCollectionRequest $request, Collection $collection)
    {
        Gate::authorize('update', $collection);

        $collection->update($request->validated());

        // If the collection is no longer localizable, we need to delete those localized collection values as well.
        if (!$collection->is_localizable) {
            CollectionValue::where('collection_id', $collection->id)->whereNotNull('language_id')->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'List updated successfully',
            'collection' => $collection
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Collection $collection)
    {
        Gate::authorize('delete', $collection);

        $collection->delete();
        return response()->json([
            'success' => true,
            'message' => 'List deleted successfully'
        ]);
    }
}
