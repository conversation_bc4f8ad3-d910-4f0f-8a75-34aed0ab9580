<?php

namespace App\Http\Controllers;

use App\Http\Requests\AddItemsRequest;
use App\Http\Requests\UpdateCategoryItemsRequest;
use App\Http\Requests\StoreCategoryRequest;
use App\Http\Requests\UpdateCategoryRequest;
use App\Models\Category;
use App\Models\Cycle;
use App\Services\ValidationAssetCategoryService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $request = request();
        $query = Category::query();

        $cycleID = $request->input('cycleID');
        $search = request()->query('search');
        if ($cycleID) {
            $query->where('cycle_id', $cycleID);
        }

        if ($request->filled('search')) {
            $search = '%' . $request->search . '%';
            $query->whereHas('categoryValues', function ($q) use ($search) {
                $q->where('value', 'like', $search);
            });
        }

        if ($request->filled('filter.cycle_id')) {
            $query->where('cycle_id', $request->input('filter.cycle_id'));
        }

        if ($request->filled('filter.type')) {
            $query->where('type', $request->input('filter.type'));
        }
        // TODO: fix language code
        return $query->with(['categoryItems', 'childCategories', 'categoryValues' => fn ($q) => $q->where('language_id', 13)])->get();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCategoryRequest $request)
    {
        $validatedData = $request->validated();

        Gate::authorize('create', [Category::class, $validatedData['is_root'] ?? false]);

        $category = Category::create(array_merge(
            $validatedData,
            [
                'airline_id' => Cycle::find($validatedData['cycle_id'])->airline_id
            ]
        ));

        return response()->json([
            'success' => true,
            'message' => 'Successfully created a new category',
            'category' => $category
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        return $category->load(['categoryValues', 'categoryItems', 'childCategories' => fn ($q) => $q->with('categoryItems'), 'issues'])
            ->setAppends(['airline_name']);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCategoryRequest $request, Category $category, ValidationAssetCategoryService $validationService)
    {
        Gate::authorize('update', $category);

        $category->update($request->validated());
        // run validator on update
        $validationService->runValidation($category);

        return response()->json([
            'success' => true,
            'message' => 'Category updated successfully.'
        ], Response::HTTP_ACCEPTED);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        Gate::authorize('delete', $category);
        $category->delete();

        return response()->json(['message' => 'Category deleted successfully.']);
    }

    public function addItems(Category $category, AddItemsRequest $request, ValidationAssetCategoryService $validationService)
    {
        Gate::authorize('update', $category);

        $data = $request->validated();

        if ($category->type && $category->type != $data['type']) {
            return response()->json(['error' => 'Category type does not match item type.'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $category->type = $data['type'];
        $category->save();

        ($data['type'] === 'assets')
            ? $category->addAssets($data['ids'])
            : $category->addChildCategories($data['ids']);

        // if category type is asset run validation
        if ($category->type === 'assets') {
            $validationService->runValidation($category, $data['ids']);
        }

        return response()->json(['message' => 'Items added to category successfully.']);
    }

    public function getAddableItems(Category $category)
    {
        $type = request()->query('type');
        if ($type !== 'assets' && $type !== 'categories') {
            return response()->json(['error' => 'Invalid type.'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $items = ($type === 'assets')
            ? $category->getAddableAssets()
            : $category->getAddableCategories();

        return response()->json($items);
    }

    public function updateItemOrder(Category $category, UpdateCategoryItemsRequest $request)
    {
        Gate::authorize('update', $category);

        $data = $request->validated();

        $sorted = $category->updateItemsOrder($data['ids'], $data['type']);
        if (!$sorted) {
            return response()->json(['error' => 'Unable to sort items.'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return response()->json(['message' => 'Item order updated successfully.']);
    }

    public function removeItems(Category $category, UpdateCategoryItemsRequest $request, ValidationAssetCategoryService $validationService)
    {
        Gate::authorize('update', $category);

        $data = $request->validated();

        ($data['type'] === 'assets')
            ? $category->removeAssets($data['ids'])
            : $category->removeChildCategories($data['ids']);

        // if category type is asset run validation
        if ($category->type === 'assets') {
            $validationService->runValidation($category, $data['ids']);
        }

        return response()->json(['message' => 'Items removed from category successfully.']);
    }
}
