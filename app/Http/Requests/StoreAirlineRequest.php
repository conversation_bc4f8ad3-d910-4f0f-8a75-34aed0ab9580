<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

use App\Models\Enums\CycleFrequency;

class StoreAirlineRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'icao_code' => 'nullable|max:4',
            'cycle_frequency' => ['required', Rule::enum(CycleFrequency::class)],
            'organization_id' => 'sometimes|required|exists:organizations,id',
        ];
    }
}
