<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLanguageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Adjust authorization logic as needed
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        // Get the language ID from the route
        $languageId = $this->route('language');

        return [
            'ife_code' => [
                'sometimes',
                'required',
                'string',
                'max:10',
                Rule::unique('languages', 'ife_code')->ignore($languageId)
            ],
            'bcp_47_code' => [
                'nullable',
                'string',
                'max:10',
                Rule::unique('languages', 'bcp_47_code')->ignore($languageId)
            ],
            'iso_639_2_t_code' => [
                'nullable',
                'string',
                'max:10',
                Rule::unique('languages', 'iso_639_2_t_code')->ignore($languageId)
            ],
            'eng_description' => [
                'sometimes',
                'required',
                'string',
                'max:255'
            ],
            'local_description' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ife_code.unique' => 'A language with this IFE code already exists.',
            'bcp_47_code.unique' => 'A language with this BCP 47 code already exists.',
            'iso_639_2_t_code.unique' => 'A language with this ISO 639-2 T code already exists.'
        ];
    }
}
