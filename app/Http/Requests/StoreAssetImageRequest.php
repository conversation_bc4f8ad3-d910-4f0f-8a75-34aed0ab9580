<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAssetImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'airline_id' => 'required|exists:airlines,id',
            'cycle_id' => 'required|exists:cycles,id',
            'field_id' => 'sometimes|exists:fields,id',
            'asset_id' => 'sometimes|exists:assets,id',
            'file_name' => 'required|string', 
            'size' => 'required|integer',
            'mime' => ['string', Rule::in(['image/jpeg'])],
        ];
    }
}
