<?php

namespace App\Http\Requests;

use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\FieldEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'field_type' => ['required', Rule::enum(FieldEnum::class)],
            'schema_id' => 'required|exists:schemas,id',
            'asset_types.*' => [
                'sometimes',
                Rule::enum(AssetTypeEnum::class),
            ]
        ];
    }
}
