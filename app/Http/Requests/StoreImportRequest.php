<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreImportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'method' => 'required|string',
            'file_list' => 'required|array',
            'file_list.*.name' => 'required|string',
            'file_list.*.size' => 'required|numeric',
            'specifications' => 'present|array',
        ];
    }
}
