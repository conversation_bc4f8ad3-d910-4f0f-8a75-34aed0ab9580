<?php

namespace App\Http\Requests;

use App\Models\Enums\AssetTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAssetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required:string',
            'asset_type' => ['required', Rule::enum(AssetTypeEnum::class)],
            'parent_asset_id' => 'nullable|exists:assets,id',
        ];
    }
}
