<?php

namespace App\Http\Requests;

use App\Rules\NoTags;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\Enums\IssueEnum;
use Illuminate\Validation\Rule;

class StoreIssueRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'description' => ['required', 'string', 'max:255', new NoTags()],
            'cycle_id' => ['required', 'exists:cycles,id'],
            'field_value_id' => ['sometimes', 'exists:field_values,id'],
            'validator_field_id' => ['sometimes', 'exists:validator_fields,id'],
            'asset_id' => ['sometimes', 'exists:assets,id'],
            'type' => ['required', Rule::enum(IssueEnum::class)],
            'is_custom' => ['nullable', 'boolean']
        ];
    }
}
