<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCycleRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date',
            'schema_id' => 'sometimes|exists:schemas,id',
            'is_locked' => 'sometimes|boolean',
        ];
    }
}