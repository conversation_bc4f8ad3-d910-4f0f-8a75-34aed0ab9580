<?php

namespace App\Http\Requests;

use App\Models\Enums\FieldEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreValidatorFieldTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'validator_id' => 'required|numeric|exists:validators,id',
            'field_type' => ['required', Rule::enum(FieldEnum::class)],
            'enabled' => 'required|boolean'
        ];
    }

    /**
     * Prepare data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        if($this->has('enabled')){
            $this->merge([
                'enabled' => filter_var($this->enabled, FILTER_VALIDATE_BOOL, FILTER_NULL_ON_FAILURE) ?? false
            ]);
        }
    }
}
