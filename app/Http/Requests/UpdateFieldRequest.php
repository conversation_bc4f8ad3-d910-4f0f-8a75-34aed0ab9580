<?php

namespace App\Http\Requests;

use App\Models\Enums\AssetTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string',
            'field_type' => 'sometimes|string',
            'collection_id' => 'sometimes|nullable|exists:collections,id',
            'use_languages' => 'sometimes|boolean',
            'asset_types' => [
                'sometimes',
                'array'
            ],
            'asset_types.*' => [
                'sometimes',
                Rule::enum(AssetTypeEnum::class),
            ],
            'is_localizable' => 'sometimes|boolean',
            'is_size_field' => 'sometimes|boolean',
            'is_external_api_value_source' => 'sometimes|boolean',
            'input_key' => 'sometimes|nullable|string',
            'output_key' => 'sometimes|nullable|string',
            'is_external_api_key_field' => 'sometimes|boolean',
        ];
    }
}
