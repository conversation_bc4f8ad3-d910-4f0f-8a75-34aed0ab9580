<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Enums\ValidatorSeverityEnum;

class StoreValidatorFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'field_id' => 'required|numeric|exists:fields,id', 
            'validator_id' => 'required|numeric|exists:validators,id', 
            'parameters' => 'sometimes|nullable|string', 
            'severity' => ['required', Rule::enum(ValidatorSeverityEnum::class)], 
            'enabled' => 'required|boolean'
        ];
    }

    /**
     * Prepare data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        if($this->has('parameters')){
            $this->merge([
                'parameters' => json_encode($this->parameters)
            ]);
        }
        if($this->has('enabled')){
            $this->merge([
                'enabled' => filter_var($this->enabled, FILTER_VALIDATE_BOOL, FILTER_NULL_ON_FAILURE) ?? false
            ]);
        }
    }
}
