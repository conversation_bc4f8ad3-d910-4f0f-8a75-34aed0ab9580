<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'description' => 'sometimes|nullable|string',
            'is_root' => 'sometimes|boolean',
            'asset_types' => 'sometimes|array',
            'start_date' => 'sometimes|nullable|date',
            'viasat_id' => 'sometimes|nullable|string',
            'end_date' => 'sometimes|nullable|date',
            'viasat_unique_name' => 'sometimes|nullable|string',
        ];
    }
}
