<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLanguageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Adjust authorization logic as needed
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ife_code' => [
                'required', 
                'string', 
                'max:10', 
                'unique:languages,ife_code'
            ],
            'bcp_47_code' => [
                'nullable', 
                'string', 
                'max:10', 
                'unique:languages,bcp_47_code'
            ],
            'iso_639_2_t_code' => [
                'nullable', 
                'string', 
                'max:10', 
                'unique:languages,iso_639_2_t_code'
            ],
            'eng_description' => [
                'required', 
                'string', 
                'max:255'
            ],
            'local_description' => [
                'nullable', 
                'string', 
                'max:255'
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ife_code.unique' => 'A language with this IFE code already exists.',
            'bcp_47_code.unique' => 'A language with this BCP 47 code already exists.',
            'iso_639_2_t_code.unique' => 'A language with this ISO 639-2 T code already exists.'
        ];
    }
}
