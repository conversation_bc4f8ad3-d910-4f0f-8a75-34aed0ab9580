<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrganizationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'owner_id' => 'sometimes|required|exists:users,id',
            'airlines' => 'sometimes|array',
            'airlines.*' => 'integer|exists:airlines,id'
        ];
    }
}