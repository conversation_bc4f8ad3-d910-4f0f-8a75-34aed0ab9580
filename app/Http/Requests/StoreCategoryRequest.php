<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'sometimes|string|in:categories,assets',
            'cycle_id' => 'required|exists:cycles,id',
            'is_root' => 'sometimes|boolean',
            'viasat_id' => 'sometimes|nullable|string',
            'asset_types' => 'sometimes|array',
            'start_date' => 'sometimes|nullable|date',
            'end_date' => 'sometimes|nullable|date',
            'title' => 'sometimes|string',
        ];
    }
}
