<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAssetImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => 'sometimes|image|mimes:jpg',
            'file_name' => 'sometimes|required|string|max:255',
            'mime' => ['sometimes', 'string', Rule::in(['image/jpeg'])],
            'extension' => ['sometimes', 'string', Rule::in(['jpg'])],
            'width' => 'sometimes|numeric',
            'height' => 'sometimes|numeric',
            'size' => 'sometimes|numeric', 
            'status' => 'sometimes|string',
            'cycle_id' => 'sometimes|exists:cycles,id',
            'asset_id' => 'sometimes|exists:assets,id|nullable',
            'field_id' => 'sometimes|exists:fields,id|nullable',
        ];
    }
}
