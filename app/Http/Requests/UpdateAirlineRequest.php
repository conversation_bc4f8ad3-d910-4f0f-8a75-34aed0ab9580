<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\Enums\CycleFrequency;
use App\Models\Enums\AssetTypeEnum;

class UpdateAirlineRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'icao_code' => 'nullable|max:4',
            'cycle_frequency' => [Rule::enum(CycleFrequency::class)],
            'asset_types' => [
                'sometimes',
                'array'
            ],
            'asset_types.*' => [
                'sometimes',
                Rule::enum(AssetTypeEnum::class),
            ],
            'max_total_snapshot_size' => 'sometimes|required|integer|min:0|max:18446744073709551615', 
            'max_delta_snapshot_size' => 'sometimes|nullable|integer|min:0|max:18446744073709551615',
            'max_for_eis_size' => 'sometimes|nullable|integer|min:0|max:18446744073709551615',
            'organization_id' => 'sometimes|required|exists:organizations,id',
            'recall_period' => 'sometimes|integer|min:0',
        ];
    }
}
