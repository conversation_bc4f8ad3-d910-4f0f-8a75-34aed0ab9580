<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProccessImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cycle_id' => 'required',
            'asset_type' => 'required',
        ];
    }
}
