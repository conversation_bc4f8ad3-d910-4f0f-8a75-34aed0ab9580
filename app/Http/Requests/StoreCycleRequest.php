<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCycleRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date',
            'airline_id' => 'required|integer|exists:airlines,id',
            'recalled_assets' => 'sometimes|array',
        ];
    }
}