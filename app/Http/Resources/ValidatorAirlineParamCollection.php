<?php

namespace App\Http\Resources;

use App\Models\Enums\ValidatorRulesEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ValidatorAirlineParamCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(fn($item) => [
                'validator_id' => $item->validator_id,
                'airline_id' => $item->airline_id,
                'parameters' => $item->parameters,
                'validator_name' => ValidatorRulesEnum::getLabel($item->validator->name),
                'airline_name' => $item->airline->name,
            ])
        ];
    }
}
