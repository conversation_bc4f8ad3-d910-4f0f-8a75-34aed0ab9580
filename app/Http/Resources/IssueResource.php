<?php

namespace App\Http\Resources;

use App\Models\Enums\ValidatorSeverityEnum;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IssueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    { 
        return [
            'uuid' => $this->uuid,
            'description' => $this->description,
            'type' => $this->type,
            'field_value_id' => $this->field_value_id,
            'asset' => $this->asset, 
            'validator_field_id' => $this->validator_field_id,
            'severity' => $this->validatorField ? $this->validatorField->severity : ValidatorSeverityEnum::Warning->value,
            'is_custom' => $this->is_custom,
            'created_at' => (new DateTime($this->created_at))->format('Y-m-d H:i:s'),
            'created_by' => $this->created_by,
        ];
    }
}
