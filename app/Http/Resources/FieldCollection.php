<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Models\Enums\ValidatorRulesEnum;

class FieldCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {        
        return [
            'data' => $this->collection->map(fn($field) => [
                'id' => $field->id,
                'name' => $field->name,
                'field_type' => $field->field_type,
                'schema_id' => $field->schema_id,
                'collection_id' => $field->collection_id,
                'output_key' => $field->output_key,
                'asset_types' => $field->asset_types,
                'is_size_field' => $field->is_size_field,
                'is_localizable' => $field->is_localizable,
                'is_external_api_value_source' => $field->is_external_api_value_source,
                'enabled_validators' => $field->validatorFields->map(fn($validatorField) => [
                    'id' => $validatorField->id ?? 0,
                    'validator_id' => $validatorField->validator_id ?? 0,
                    'name' => ValidatorRulesEnum::getLabel($validatorField->validator->name) ?? 'N/A',
                    'parameters' => json_decode($validatorField->parameters)
                ]) 
            ])
        ];
    }
}
