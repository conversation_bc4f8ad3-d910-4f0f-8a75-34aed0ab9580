<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CycleAssetCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $cycle = $request->cycle;
        $airline = $cycle->airline;
        $languageID = $airline->getDefaultOrSystemLanguageId();
        return [
            'data' => $this->collection->map(function ($asset) use ($cycle, $languageID) {
                $asset->status = $asset->pivot?->status;
                $asset->withCompletionPercentage($cycle);
                $asset->withDefaultTitle($cycle, $languageID);
                $asset->childAssets->each(function ($childAsset) use ($cycle) {
                    if ($childAsset->cycles->isNotEmpty()) {
                        $childAsset->status = $childAsset->cycles->first()->pivot->status;
                    }

                    $childAsset->withCompletionPercentage($cycle);
                });
                return $asset;
            })
        ];
    }
}
