<?php

namespace App\Http\Resources;

use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Enums\ValidatorSeverityEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ValidatorFieldCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    { 
        return [
           'data' => $this->collection->map(fn($validator) => [
                'id' => $validator->id,
                'name' => ValidatorRulesEnum::getLabel($validator->name),
                'description' => $validator->description,
                'parameters' => json_decode($validator->validatorField[0]->parameters ?? $validator->parameters),
                'active' => $validator->validatorField[0]->enabled ?? false,
                'enabled' => $validator->validatorFieldType[0]->enabled ?? false,  
                'severity' => $validator->validatorField[0]->severity ?? ValidatorSeverityEnum::Warning->value,   
            ])
        ];
    }

    public function with(Request $request)
    {
        $severityLevels = ValidatorSeverityEnum::cases();

        return [
            'severity_levels' => array_map(fn(ValidatorSeverityEnum $severity) => [ 'value' => $severity->value, 'label' => $severity->value], $severityLevels)
        ];
    }
}
