<?php

namespace App\Http\Resources;

use App\Models\Issue; 
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class IssueCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return parent::toArray($request); 
    }

    public function with(Request $request)
    {  
        $counts = Issue::where('cycle_id', request()->cycleID)->select('type', DB::raw('count(*) as count'))->groupBy('type')->get();

        return [
            'issues_count' => $counts 
        ];
    }
}
