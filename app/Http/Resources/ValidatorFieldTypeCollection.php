<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Models\Enums\FieldEnum;
use App\Models\Enums\ValidatorRulesEnum; 

class ValidatorFieldTypeCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(fn($validator) => [
                'id' => $validator->id,
                'name' => ValidatorRulesEnum::getLabel($validator->name),
                'description' => $validator->description,
                'parameters' => json_decode($validator->parameters),
                'field_types' => $validator->ValidatorFieldType->map(fn($field) => [
                    'field_type' => $field->field_type,
                    'enabled' => (bool)$field->enabled,
                ])
            ])
        ];
    }

    public function with(Request $request)
    {
        $fieldTypes = FieldEnum::cases();

        return [
            'field_types' => array_map(fn(FieldEnum $type) => $type->value, $fieldTypes)
        ];
    }
}
