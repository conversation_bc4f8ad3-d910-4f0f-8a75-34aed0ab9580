<?php

namespace App\Policies;

use App\Models\Cycle;
use App\Models\User;

class CyclePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // not yet implemented
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Cycle $cycle): bool
    {

        if ($user->has_unrestricted_edit_role) {
            return true;
        }

        $airline_ids = $user->getAirlineIdsForOrganization();

        return in_array($cycle->airline_id, $airline_ids, true);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Cycle $cycle): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Cycle $cycle): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Cycle $cycle): bool
    {
        // not yet implemented
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Cycle $cycle): bool
    {
        // not yet implemented
        return false;
    }
}
