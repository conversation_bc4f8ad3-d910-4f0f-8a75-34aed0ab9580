<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // not yet implemented
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        if ($user->has_unrestricted_edit_role) {
            return true;
        }

        return $user->id === $model->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model, array $fields): bool
    {
        if ($user->has_unrestricted_edit_role) {
            return true;
        }

        if ($user->id === $model->id) {
            $allowed_keys = ['name', 'email', 'password', 'password_confirmation'];
            $updated_keys = array_keys($fields);
            return empty(
                array_diff($updated_keys, $allowed_keys)
            );
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return false;
    }
}
