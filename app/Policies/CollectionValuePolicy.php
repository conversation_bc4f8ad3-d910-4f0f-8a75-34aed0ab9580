<?php

namespace App\Policies;

use App\Models\CollectionValue;
use App\Models\User;

class CollectionValuePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CollectionValue $collectionValue): bool
    {
        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CollectionValue $collectionValue): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CollectionValue $collectionValue): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CollectionValue $collectionValue): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CollectionValue $collectionValue): bool
    {
        return false;
    }
}
