<?php

namespace App\Policies;

use App\Models\Cycle;
use App\Models\Schema;
use App\Models\User;

class SchemaPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // not implemented
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Schema $schema): bool
    {
        if ($user->has_unrestricted_edit_role) {
            return true;
        }

        $allowed_schemas = Cycle::forUser($user)->distinct()->pluck('schema_id')->toArray();
        return in_array($schema->id, $allowed_schemas);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->has_unrestricted_edit_role;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Schema $schema): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Schema $schema): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Schema $schema): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Schema $schema): bool
    {
        return false;
    }
}
