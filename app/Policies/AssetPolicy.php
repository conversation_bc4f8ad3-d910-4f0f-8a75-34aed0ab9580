<?php

namespace App\Policies;

use App\Models\Asset;
use App\Models\Cycle;
use App\Models\User;

class AssetPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Asset $asset): bool
    {
        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Asset $asset, array $updated_fields, Cycle $cycle): bool
    {
        if ($user->has_unrestricted_edit_role) {
            return true;
        }

        $asset->withUserPermissions($user, $cycle);

        if (array_key_exists('start_date', $updated_fields)
            && in_array('start_date', $asset->user_permissions['disabled_fields'])) {
            return false;
        }

        // title = "Asset Identifier" in UI
        return ! array_key_exists('title', $updated_fields);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Asset $asset): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Asset $asset): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Asset $asset): bool
    {
        return false;
    }
}
