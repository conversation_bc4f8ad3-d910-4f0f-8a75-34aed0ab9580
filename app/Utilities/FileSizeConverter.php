<?php

namespace App\Utilities;


class FileSizeConverter {
    
    private static $multipliers = [
        'B'  => 1,
        'KB' => 1024,
        'MB' => 1024 ** 2,
        'GB' => 1024 ** 3,
        'TB' => 1024 ** 4,
        'PB' => 1024 ** 5,
    ];

    private static $units = [
        'B',
        'KB',
        'MB',
        'GB',
        'TB',
        'PB'
    ];

    /**
     * Convert a human-readable file size to bytes
     *
     * @param string $size A string containing a file size (e.g., "123 MB", "1.2 GB")
     * @return int|float|false The size in bytes or false on failure
     */
    public static function toBytes($size) {
        // Remove any whitespace and make the string uppercase for consistency
        $size = strtoupper(trim($size));
        
        /**
         * The regex pattern matches:
         * - 1st Capturing Group (\d+(?:\.\d+)?) a number with optional decimal
         * - Match a single character present [\s] optional whitespace
         * - 2nd Capturing Group ([KMGTP]?B) a unit (K, M, G, T, P) and B
         *
         * Example matches:
         * - 1.5GB
         * - 1024 KB
         * - 2TB
        */
        if (!preg_match('/^(\d+(?:\.\d+)?)[\s]*([KMGTP]?B)$/', $size, $matches)) {
            return false; // Invalid format
        }
        
        $num = (float) $matches[1];
        $unit = $matches[2];
        
        // If the unit exists in our multipliers array, convert to bytes
        if (isset(self::$multipliers[$unit])) {
            return $num * self::$multipliers[$unit];
        }
        
        return false; // Unknown unit
    }

    /** 
     * convert bytes to a human-readable format
     * 
     * @param int|float $bytes The number of bytes
     * @param int $precision The number of decimal places to round to
     * @return string The formatted file size
     */
    public static function fromBytes($bytes, $precision = 2) {
        if ($bytes <= 0) {
            return '0 B';
        }
        
        // Calculate the appropriate unit
        $base = log($bytes) / log(1024);
        $unit = self::$units[min(floor($base), count(self::$units) - 1)];
        
        // Calculate the value in the determined unit
        $value = round($bytes / pow(1024, floor($base)), $precision);
        
        return $value . ' ' . $unit;
    }

}