<?php

namespace App\Utilities;

use App\Models\Enums\AssetStatus;
use Carbon\Carbon;
use App\Models\Asset;
use App\Models\{Category, CategoryClosure, CategoryItem, CategoryValue, Cycle, Field, FieldValue, AssetAudio, AssetImage, AssetVideo};
use App\Models\Enums\FieldEnum;
use App\Services\ValidationAssetService;
use App\Services\ValidationIssueService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CycleAssetManager
{
    // items in a data chunk for batch inserts
    private const BATCH_SIZE = 500;

    private string $currentTimestamp;

    private ValidationAssetService $validationAssetService;

    private ValidationIssueService $validationIssueService;

    public function __construct(ValidationAssetService $validationAssetService, ValidationIssueService $validationIssueService)
    {
        $this->currentTimestamp = Carbon::now()->format('Y-m-d H:i:s');
        $this->validationAssetService = $validationAssetService;
        $this->validationIssueService = $validationIssueService;
    }
    /**
     * Duplicate Assets when schema changes
     *
     * - Asset is duplicated if a field is removed via schema change
     * - Asset is duplicated if a collection item has been removed for a list-backed collection and a past field value references that item.
     */
    public function ensureEditableAssetVersionByField(Field $field, $isUpdate = false): void
    {
        $relation = match ($field->field_type) {
            FieldEnum::Image->value => 'images',
            FieldEnum::Video->value => 'videos',
            FieldEnum::Audio->value => 'audio',
            default => 'fieldValues'
        };
        // get assets with field
        $assets = Asset::whereHas($relation, fn($q) => $q->where('field_id', $field->id))->get();

        // duplicate assets in all cycles that have the field
        foreach ($assets as $asset) {
            $asset->cycles()->each(fn($cycle) => $this->ensureEditableAssetVersion($asset->id, $cycle->id, $isUpdate ? null : $field->withValue($asset->id)->value));
        }
    }

    /**
     * Duplicate an Asset
     *
     * - Asset is duplicated if a field value is changed
     * - Asset is only duplicated a single time within a given cycle
     * - Asset is duplicated if start or end date is edited
     * - Asset is not duplicated if a field and field value is added via schema change
     *
     * @return Asset duplicate or origin asset
     */
    public function ensureEditableAssetVersion(int $assetID, int $cycleID, FieldValue|AssetVideo|AssetAudio|null $fieldValue = null): Asset
    {
        $asset = Asset::where(fn($q) => $q->where('id', $assetID)->orWhere('origin_asset_id', $assetID))
            ->where('origin_cycle_id', $cycleID)
            ->first();

        // if duplicate exist return it
        if ($asset) {
            return $asset;
        }

        $asset = Asset::where('id', $assetID)->first();
        $cycle = Cycle::where('id', $cycleID)->first();

        // if asset does not belong to the cycle it status must be set as recalled
        $assetInCycle = $cycle->assets()->withPivot('status')
            ->where('assets.id', $assetID)
            ->first();
        $currentAssetStatus = $assetInCycle?->pivot->status ?? AssetStatus::Recalled->value;
        if ($currentAssetStatus === AssetStatus::New->value) {
            return $asset;
        }

        // Asset is not duplicated if a field and field value is added via schema change
        if ($fieldValue && $fieldValue->field->created_at > $cycle->start_date) {
            return $asset;
        }

        $duplicateAsset = $asset->replicate()->fill([
            'version_number' =>  $asset->version_number + 1,
            'origin_asset_id' => $asset->origin_asset_id ?? $assetID,
            'origin_cycle_id' => $cycleID,
        ]);
        $duplicateAsset->save();

        $cycle->assets()->attach($duplicateAsset->id, ['status' => $currentAssetStatus]);
        $cycle->assets()->detach($assetID);
        // carry over all values from original asset
        $this->duplicateAssetCategoryRelations($assetID, $duplicateAsset->id, $cycleID);
        $this->duplicateAssetFieldValues($assetID, $duplicateAsset->id, ($fieldValue instanceof FieldValue) ? $fieldValue : null);
        $this->duplicateAssetAudios($assetID, $duplicateAsset->id, ($fieldValue instanceof AssetAudio) ? $fieldValue : null);
        $this->duplicateAssetVideos($assetID, $duplicateAsset->id, ($fieldValue instanceof AssetVideo) ? $fieldValue : null);
        $this->duplicateAssetImages($assetID, $duplicateAsset->id);
        // run asset validation instead of duplicating issues
        $this->validationAssetService->validateAssetFields($duplicateAsset, $cycle);
        // clean original asset issues
        $this->validationIssueService->fixValidatorIssue($assetID, $cycleID);

        return $duplicateAsset;
    }

    private function duplicateAssetCategoryRelations(int $assetID, int $newAssetID, int $cycleID): void
    {
        $categories = Category::where('cycle_id', $cycleID)->pluck('id'); // current cycle categories
        $categoryItems = CategoryItem::where('asset_id', $assetID)->whereIn('category_id', $categories)->get();

        $dataToUpsert = [];
        foreach ($categoryItems as $categoryItem) {
            $dataToUpsert[] = [
                'order' => $categoryItem->order,
                'category_id' => $categoryItem->category_id,
                'asset_id' => $newAssetID,
            ];
        }

        if (!empty($dataToUpsert)) {
            $chunks = collect($dataToUpsert)->chunk(self::BATCH_SIZE);
            foreach ($chunks as $chunk) {
                CategoryItem::upsert($chunk->toArray(), ['category_id', 'asset_id'], ['order']);
            }
        }
    }

    private function duplicateAssetFieldValues(int $assetID, int $newAssetID, FieldValue|null $fieldValue): void
    {
        $fieldValues = FieldValue::where('asset_id', $assetID)->when($fieldValue, fn($q) => $q->whereNot('id', $fieldValue->id))->get();
        $values_data = [];
        foreach ($fieldValues as $fieldValue) {
            $data = $fieldValue->replicate()->toArray();
            $data['asset_id'] = $newAssetID;
            $data['created_at'] = $this->currentTimestamp;
            $data['updated_at'] = $this->currentTimestamp;
            $values_data[] = $data;
        }
        self::runBatchInsert((new FieldValue())->getTable(), $values_data);
    }

    private function duplicateAssetVideos(int $assetID, int $newAssetID, AssetVideo|null $assetVideo): void
    {
        $videos = AssetVideo::where('asset_id', $assetID)->when($assetVideo, fn($q) => $q->whereNot('id', $assetVideo->id))->get();
        $video_data = [];
        foreach ($videos as $video) {
            $data = $video->replicate()->toArray();
            $data['asset_id'] = $newAssetID;
            $data['created_at'] = $this->currentTimestamp;
            $data['updated_at'] = $this->currentTimestamp;
            $data['uuid'] = Str::uuid();
            $video_data[] = $data;
        }
        self::runBatchInsert((new AssetVideo())->getTable(), $video_data);
    }

    private function duplicateAssetAudios(int $assetID, int $newAssetID, AssetAudio|null $assetAudio): void
    {
        $audios = AssetAudio::where('asset_id', $assetID)->when($assetAudio, fn($q) => $q->whereNot('id', $assetAudio->id))->get();
        $audio_data = [];
        foreach ($audios as $audio) {
            $data = $audio->replicate()->toArray();
            $data['asset_id'] = $newAssetID;
            $data['created_at'] = $this->currentTimestamp;
            $data['updated_at'] = $this->currentTimestamp;
            $data['uuid'] = Str::uuid();
            $audio_data[] = $data;
        }
        self::runBatchInsert((new AssetAudio())->getTable(), $audio_data);
    }

    private function duplicateAssetImages(int $assetID, int $newAssetID): void
    {
        $images = AssetImage::where('asset_id', $assetID)->get();
        $image_data = [];
        foreach ($images as $image) {
            $data = $image->replicate()->toArray();
            $data['asset_id'] = $newAssetID;
            $data['created_at'] = $this->currentTimestamp;
            $data['updated_at'] = $this->currentTimestamp;
            $data['uuid'] = Str::uuid();
            $image_data[] = $data;
        }
        self::runBatchInsert((new AssetImage())->getTable(), $image_data);
    }

    public static function migrateAsset($asset, $cycle): void
    {
        $startOfCycle = Carbon::parse($cycle->start_date);
        // If the asset end date is before the start of the cycle, it should not be attached
        if ($asset->end_date && Carbon::parse($asset->end_date)->lt($startOfCycle)) {
            return;
        }

        // if the asset end date is before the cycle end date, set it as expiring
        if ($asset->end_date && Carbon::parse($asset->end_date)->lt(Carbon::parse($cycle->end_date))) {
            // Expiring
            $cycle->assets()->attach($asset->id, [
                'status' => AssetStatus::Expiring
            ]);
            return;
        } else {
            // Otherwise → Holdover
            $cycle->assets()->attach($asset->id, [
                'status' => AssetStatus::Holdover
            ]);
            return;
        }
    }

    public function recallAsset($asset, $cycle)
    {
        if (!$asset) {
            return;
        }

        $recallCutoff = now()->subDays($cycle->airline->recall_period);
        if ($asset->end_date && Carbon::parse($asset->end_date)->lt($recallCutoff)) {
            // Asset is outside of the recall window. Bail.
            return;
        }

        $recalledAsset = self::ensureEditableAssetVersion($asset->id, $cycle->id);
        // If the asset already belongs to the cycle, bail
        if ($recalledAsset->id === $asset->id) {
            return $recalledAsset;
        }

        $recalledAsset->start_date = $cycle->start_date;
        $recalledAsset->end_date = $cycle->end_date;
        $recalledAsset->save();

        return $recalledAsset;
    }

    public static function getRecallableAssets($cycle, $type)
    {
        $recallCutoff = now()->subDays($cycle->airline->recall_period);
        $exclusionIDs = $cycle->assets->pluck('id')
            ->merge($cycle->assets->pluck('origin_asset_id'))
            ->filter()
            ->unique();

        $assets = Asset::where('asset_type', $type)
            ->whereBetween('end_date', [$recallCutoff, $cycle->start_date])
            ->where(function ($query) use ($exclusionIDs) {
                $query->whereNotIn('id', $exclusionIDs)
                    ->where(function ($q) use ($exclusionIDs) {
                        $q->whereNull('origin_asset_id')
                            ->orWhereNotIn('origin_asset_id', $exclusionIDs);
                    });
            })
            ->orderBy('origin_asset_id')
            ->orderByDesc('version_number')
            ->get();

        $formattedResponse = $assets->groupBy('title')->map(function ($group) {
            $latestAsset = $group->sortByDesc('version_number')->first();
            return [
                'title' => $group->first()->title,
                'latest_version_id' => $latestAsset->id,
                'versions' => $group->map(function ($asset) {
                    return [
                        'title' => $asset->title,
                        'id' => $asset->id,
                        'version' => $asset->version_number,
                    ];
                }),
            ];
        });

        return $formattedResponse->values()->all();
    }

    /**
     * Migrate cycle categories with categories relatios
     * @param $old_cycle origin Cycle to migrate from
     * @param $new_cycle new Cycle to migrate to
     * @param $airline_id Airline ID
     * @return void
     */
    public static function migrateCategories(Cycle $old_cycle, Cycle $new_cycle, int $airline_id)
    {
        $now = Carbon::now();
        // 1.
        // Clone categories from the previous cycle
        $old_categories = Category::where('cycle_id', $old_cycle->id)->where('airline_id', $airline_id)->get();
        $old_categories_ids = $old_categories->pluck('id');
        $category_data = [];
        foreach ($old_categories as $category) {
            // convert models collection to array, model is overwriting timestamps processing and does not work for insert (only in save)
            $data = $category->replicate()->toArray();
            // do not carry over ended categories
            if ($data['end_date'] && $data['end_date'] < $new_cycle->start_date) {
                continue;
            }
            // same reason as above, when reading from a model timestamps are overriden
            $data['start_date'] = $data['start_date'] ? Carbon::parse($data['start_date'])->format('Y-m-d H:i:s') : null;
            $data['end_date'] = $data['end_date'] ? Carbon::parse($data['end_date'])->format('Y-m-d H:i:s') : null;
            $data['cycle_id'] = $new_cycle->id;
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
            $data['origin_id'] = $category->id;
            $data['asset_types'] = json_encode($category->asset_types); // model casting will not be called here

            $category_data[] = $data;
        }
        self::runBatchInsert('categories', $category_data);

        // new cycle categoris collection
        $categories = Category::where('cycle_id', $new_cycle->id)->where('airline_id', $airline_id)->get();

        // 2.
        // Clone category closures from the previous cycle
        $closures = CategoryClosure::whereIn('ancestor_id', $old_categories_ids)
            ->orWhereIn('descendant_id', $old_categories_ids)
            ->get();

        $closures_data = [];
        foreach ($closures as $closure) {
            $ancestor = $categories->where('origin_id', $closure->ancestor_id)->first();
            $descendant = $categories->where('origin_id', $closure->descendant_id)->first();
            if (!$ancestor || !$descendant) {
                continue; // could be filtered by ended categories
            }
            $data = $closure->replicate()->toArray();
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
            $data['ancestor_id'] = $ancestor->id;
            $data['descendant_id'] = $descendant->id;

            $closures_data[] = $data;
        }

        self::runBatchInsert('category_closures', $closures_data);

        // 3.
        // Clone category items from the previous cycle
        $items = CategoryItem::whereIn('category_id', $old_categories->pluck('id'))
            ->whereHas('item', fn($q) => $q->where('end_date', '>=', $new_cycle->start_date))
            ->get();

        $items_data = [];
        foreach ($items as $item) {
            $new_category = $categories->where('origin_id', $item->category_id)->first();
            if (!$new_category) {
                continue; // could be filtered by ended categories
            }
            $data = $item->replicate()->toArray();
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
            $data['category_id'] = $new_category->id;
            $data['asset_id'] = $item->asset_id;

            $items_data[] = $data;
        }

        self::runBatchInsert('category_items', $items_data);

        // 4.
        // Clone category_values (localizations) from the previous cycle
        $values = CategoryValue::whereIn('category_id', $old_categories->pluck('id'))->get();

        $values_data = [];
        foreach ($values as $value) {
            $new_category = $categories->where('origin_id', $value->category_id)->first();
            if (!$new_category) { // could be filtered by ended categories
                continue;
            }
            $data = $value->replicate()->toArray();
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
            $data['category_id'] = $new_category->id;

            $values_data[] = $data;
        }

        self::runBatchInsert('category_values', $values_data);
    }

    private static function runBatchInsert(string $table, array $data): void
    {
        if (empty($data)) {
            return; // nothing to insert
        }
        $chunks = collect($data)->chunk(self::BATCH_SIZE);
        foreach ($chunks as $chunk) {
            DB::table($table)->insert($chunk->toArray());
        }
    }
}
