<?php

namespace App\Console\Commands;

use App\Models\Asset;
use App\Models\Category;
use App\Models\Collection;
use App\Models\CollectionValue;
use App\Models\Cycle;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\FieldEnum;
use App\Models\FieldValue;
use App\Models\Language;
use App\Models\Snapshot;
use App\Services\ValidationFieldService;
use App\Services\ValidationIssueService;
use App\Services\ViasatService;
use App\Utilities\CycleAssetManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use stdClass;

class GetSnapshot extends Command
{
    public const SNAPSHOT_ROOT = 'snapshots/';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:get-snapshot {cycle_id?} {snapshot_queue_id?} {--send-to-viasat}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'generate a snapshot from cycle_id';
    private ?Snapshot $snapshot;
    private int $itemsToProcess;
    private ?Cycle $cycle;
    private int $itemsProcessed;

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        //
        $cycleId = $this->argument('cycle_id');
        if ($cycleId) {
            $this->cycle = Cycle::findOrFail($cycleId);
            $snapshotId = $this->argument('snapshot_queue_id');
            if (!$snapshotId) {
                // generate one.
                $this->snapshot = Snapshot::create(
                    [
                        'cycle_id' => $this->cycle->id,
                        'is_running' => true,
                    ]
                );
            } else {
                $this->snapshot = Snapshot::find($snapshotId);
                $this->snapshot->is_running = true;
                $this->snapshot->save();
            }
        } else {
            // try and find one in the DB via the snapshot queue
            $this->snapshot = Snapshot::where('is_running', false)
                ->where('is_completed', false)
                ->where('is_failed', false)
                ->first();
            if (!$this->snapshot) {
                $this->warn("Nothing to Process");
                return self::INVALID;
            }
            $this->cycle = $this->snapshot->cycle;
        }

        $this->itemsToProcess = 1;
        $this->itemsProcessed = 0;

        if (!$this->cycle) {
            $this->error('No cycle found');
            return self::FAILURE;
        }


        $lookupValues = $this->buildLookupValues();

        /*
         * Build out a stdClass object with top-level property names as specified.
         * using stdClass will result in proper JSON object notation without any special treatment.
         */
        $output = new \stdClass();
        $output->specification = [
            'groupCode' => $this->cycle->airline->group_code,
            'defaultLang' => 'en', // todo: temporary. Pull from airline record.
            'images' => [
                // todo: fill this out based on image fields in the cycle.
                ['type' => 'seriesPoster', 'width' => 100, 'height' => 200]
            ]];

        $output->lookupData = $lookupValues;

        // sections, parent with categories?
        $output->structure = new \stdClass();
        $output->structure->sections = $this->getSectionsWithCategories();

        $output->content = new \stdClass();
        // videoGroups (seasons?)
        $output->content->videoGroups = array_values($this->getVideoGroups());

        // videoItems (all videos with a refId to their group if they have one)
        $output->content->videoItems = array_values($this->buildVideoItems());

        // audioGroups (albums)
        $output->content->audioGroups = array_values($this->getAudioGroups());

        // audioItems (all tracks, with a refId to the audioGroup that they belong to)
        $output->content->audioItems = array_values($this->buildAudioItems());

        // todo: others?

        // save this to the configured snapshot root on the default file system/bucket. (S3)
        $filename = sprintf('snapshot-%s-%s.json', $this->cycle->id, now()->format('Ymd-His'));
        Storage::put(
            self::SNAPSHOT_ROOT . $filename,
            json_encode($output, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        $this->info("Snapshot has been saved to " . self::SNAPSHOT_ROOT . $filename);
        \Log::debug("Snapshot has been saved to " . self::SNAPSHOT_ROOT . $filename);

        // finalize the job information for the UI.
        $this->snapshot->is_running = false;
        $this->snapshot->is_completed = true;
        $this->snapshot->filename = $filename;
        $this->snapshot->progress = 100;
        $this->snapshot->save();

        // shovel it to viasat.
        if ($this->option('send-to-viasat')) {
            $viasatService = new ViasatService(
                new ValidationIssueService(),
                app(ValidationFieldService::class),
                app(CycleAssetManager::class)
            );
            $viasatService->sendSnapshot($this->snapshot);
        }

        return self::SUCCESS;
    }

    private function buildLookupValues(): array
    {
        $lookupValues = [];
        $languages = Language::selectRaw('uuid, iso_639_2_t_code isoCode, eng_description name, local_description nativeName')
            ->get();

        // convert to a 'dumb' collection to override casting.
        $languages = collect($languages->toArray())->map(function ($language) {
            $language['id'] = $language['uuid'];
            unset($language['uuid']);
            return $language;
        });

        $lookupValues['languages'] = $languages;

        // in the cycle find any field that is a dropdown to generate the `lookupData` node
        $dropdownFields = $this->cycle->schema->fields()->where('field_type', FieldEnum::Dropdown)->get();
        $this->itemsToProcess += count($dropdownFields);

        $this->snapshot->setProgress($this->itemsProcessed / $this->itemsToProcess); // this is totally arbitrary.

        foreach ($dropdownFields as $field) {
            // languages is a fixed list, one-time lookup table
            if ($field->use_languages) {
                $this->itemsProcessed++;
                continue;
            }

            if ($field->collection_id) {
                // get all possible values for this field's lookup
                $collection = Collection::find($field->collection_id);
                $valueStack = [];

                /** @var CollectionValue $cv */
                foreach ($collection->collectionValues->sortBy('id') as $cv) {
                    $fnCollectionValueMatcher = fn ($value) => $value->_matching_id === $cv->collection_value_id;

                    if ($cv->collection_value_id && array_find($valueStack, $fnCollectionValueMatcher)) {
                        $value = array_find($valueStack, $fnCollectionValueMatcher);
                    } else {
                        $value = new \stdClass();
                        $value->id = $cv->uuid ?: $cv->id;
                        $value->_matching_id = $cv->id;
                    }

                    if ($cv->language_id) {
                        // e.g., genres: when it's translated, append ['translation'][$lang] = $value
                        if (isset($value->translation)) {
                            $value->translation[$cv->language->bcp_47_code] = $cv->label;
                        } else {
                            $value->translation = [
                                $cv->language->bcp_47_code => $cv->label,
                            ];
                        }
                        $existingValueIndex = array_find_key($valueStack, $fnCollectionValueMatcher);
                        $valueStack[$existingValueIndex] = $value;
                    } else {
                        // e.g., ratings: when it's not translated ['name'] = $value
                        $value->name = $cv->label;
                        $valueStack[] = $value;
                    }
                }

                // cleanup helper property and `name` if localizable
                $valueStack = array_map(function ($value) use ($collection) {
                    unset($value->_matching_id);
                    if ($collection->is_localizable) {
                        unset($value->name);
                    }
                    return $value;
                }, $valueStack);

                $key = ($collection->output_key ?? $field->output_key) ?? Str::camel(Str::slug($field->name));
                $lookupValues[$key] = array_values($valueStack);
            }

            $this->itemsProcessed++;
            $this->snapshot->setProgress($this->itemsProcessed / $this->itemsToProcess);
        }

        // todo: placeholders when content becomes ready.

        /**
         * Note: If any of these 'keys' become managed in collections, (maybe badgeStyles, badgeTexts, and fleets?)
         * they will be automatically handled above and can be removed from here.
         */
        $lookupValues['interstitialAds'] = $this->getInterstitialAds();
        $lookupValues['prerollAds'] = $this->getPrerollAds(); // aka warning slates
        $lookupValues['adGroups'] = $this->getAdGroups();
        $lookupValues['featured'] = $this->getFeatured();
        $lookupValues['fleets'] = $this->getFleets();
        $lookupValues['availabilityRules'] = $this->getAvailabilityRules();
        $lookupValues['badgeStyles'] = $this->getBadgeStyles();
        $lookupValues['badgeTexts'] = $this->getBadgeTexts();

        return $lookupValues;
    }

    private function buildAudioItems(): array
    {
        $audioAssets = $this->cycle->assets()
            ->whereIn('asset_type', [AssetTypeEnum::Track, AssetTypeEnum::Audiobook, AssetTypeEnum::Podcast])->get();

        $this->itemsToProcess += count($audioAssets);
        $this->info("Found " . count($audioAssets) . " audio assets");
        $audioItems = [];

        /** @var Asset $asset */
        foreach ($audioAssets as $asset) {
            // grab this asset's fields/values
            $audioItems[] = $this->getAssetFields($asset);

            $this->itemsProcessed++;
            $this->snapshot->setProgress($this->itemsProcessed / $this->itemsToProcess);
        }

        return $audioItems;
    }
    private function buildVideoItems(): array
    {
        $videoAssets = $this->cycle->assets()->whereIn('asset_type', [AssetTypeEnum::Movie, AssetTypeEnum::Episode])->get();
        $this->itemsToProcess += count($videoAssets);
        $videoItems = [];

        $this->info("Found " . count($videoAssets) . " video assets");

        /** @var Asset $asset */
        foreach ($videoAssets as $asset) {
            // grab this asset's fields/values
            $videoItems[] = $this->getAssetFields($asset);

            $this->itemsProcessed++;
            $this->snapshot->setProgress($this->itemsProcessed / $this->itemsToProcess);
        }

        return $videoItems;
    }

    private function getAssetFields($asset): stdClass
    {
        $assembledAsset = new stdClass();
        $assembledAsset->id = $asset->uuid ?: $asset->id;
        $assembledAsset->startDate = $asset->start_date->format('Y-m-d');
        $assembledAsset->endDate = $asset->end_date->format('Y-m-d');

        $imageFiles = [];
        $mediaFiles = [];

        /** @var FieldValue $fv */
        foreach ($asset->fieldValues as $fv) {
            $key = $fv->field->output_key ?? Str::camel(Str::slug($fv->field->name));

            // If it's an image, add this FV to the image list, to be appended to the object when finished to support
            // multiple images and todo: multiple media files.
            if ($fv->field->field_type == FieldEnum::Image) {
                $cleanImage = new stdClass();
                // todo: CS-1675
                // derive $clearImage->type by asset and field name. (eg: poster + ASSET_SERIES = `seriesPoster` type.)
                $cleanImage->type = strtolower($asset->asset_type) . ucfirst($key);
                $cleanImage->name = $fv->value;
                if ($assetImage = $fv->field->getAssetImage($asset->id)) {
                    $cleanImage->id = $assetImage->uuid;
                    // todo: populated via CS-1597
                    $cleanImage->sha256 = $assetImage->sha256 ?? 'not-calculated';
                    $cleanImage->url = $assetImage->getSignedDownloadURL();
                }
                $imageFiles[] = $cleanImage;
            } else if ($fv->language_id) {
                // when the field is translated, break it down [key]['lang'] = value
                if (isset($assembledAsset->{$key}) && !is_array($assembledAsset->{$key})) {
                    $assembledAsset->{$key} = [];
                }
                $assembledAsset->{$key}[$fv->language->bcp_47_code] = $fv->value;
            } else if ($fv->field->use_languages || $fv->field->collection_id) {
                if ($fv->field->is_multi_select) {
                    $valueIds = json_decode($fv->value, JSON_OBJECT_AS_ARRAY);

                    if (!is_array($valueIds)) {
                        $valueIds = [$fv->value];
                    }
                    // Get the UUIDs from the source values.
                    $uuids = CollectionValue::whereIn('id', $valueIds)->get()->map(fn ($cv) => $cv->uuid);

                    foreach ($uuids as $uuid) {
                        $assembledAsset->{$key}[] = ["idRef" => $uuid];
                    }
                } else {
                    // or a single value
                    $cv = CollectionValue::where('id', $fv->value)->first();
                    $assembledAsset->{$key}["idRef"] = $cv?->uuid ?? $fv->value;
                }
            } else {
                // todo: cast these based on configured types (int), (bool), leave as string?
                $assembledAsset->{$key} = $fv->value;
            }
        }

        foreach ($asset->videos as $video) {
            $mediaFiles[] = $this->createMediaFile($video, 'video');
        }

        foreach ($asset->audio as $audio) {
            $mediaFiles[] = $this->createMediaFile($audio, 'audio');
        }


        $assembledAsset->files = new stdClass();
        $assembledAsset->files->images = $imageFiles;
        $assembledAsset->files->media = $mediaFiles;

        return $assembledAsset;
    }

    private function getSectionsWithCategories(): array
    {

        $sections = $this->cycle->categories()->with('categoryValues')->where('is_root', 1)->get();
        $this->itemsToProcess += count($sections);
        $this->info("Found " . count($sections) . " sections");

        $assembledSections = [];

        /** @var Category $section */
        foreach ($sections as $section) {

            $cleanSection = new stdClass();
            $cleanSection->id = $section->uuid ?? $section->id;
            // $cleanSection->viasatId = $section->viasat_id; // removed for now because it fails the validation
            $cleanSection->viasatUniqueName = $section->viasat_unique_name;
            $assetTypes  = collect($section->asset_types);
            if (count($assetTypes) > 0) {
                // mediaType is a single value, we may want to validate the `asset_types` on save if there is a mix of different media types
                $assetType = $assetTypes->first();
                try {
                    $cleanSection->mediaType = AssetTypeEnum::from($assetType)->fileType();
                } catch (ValueError $vex) {
                    // continue, we'll just not have a mediaType and validation will catch it
                }
            }
            $cleanSection->name = $this->languageKeys($section->categoryValues()->with('language')->get());

            $this->info("Processing section: " . $section->categoryValues[0]->value);

            $categoryItems = $section->childCategories()->get();

            // don't export empty sections, they need at least 1 category
            if (count($categoryItems) === 0) {
                continue;
            }

            foreach ($categoryItems as $categoryItem) {

                if ($categoryItem instanceof Category) {
                    $cleanSubCategory = new stdClass();
                    $cleanSubCategory->id = $categoryItem->uuid ?? $categoryItem->id;
                    // $cleanSubCategory->viasatId = $categoryItem->viasat_id; // removed for now because it fails the validation
                    $cleanSubCategory->viasatUniqueName = $categoryItem->viasat_unique_name;
                    $translated = $this->languageKeys($categoryItem->categoryValues()->with('language')->get());
                    $cleanSubCategory->name = $translated;
                    $cleanSubCategory->startDate = $categoryItem->start_date; //$this->cycle->start_date->format('Y-m-d');
                    $cleanSubCategory->endDate = $categoryItem->end_date; //$this->cycle->end_date->format('Y-m-d');
                    $cleanSubCategory->items = $categoryItem->categoryItems()
                        ->get()->map(fn (Asset $item) => array_merge(
                            ['idRef' => $item->uuid ?: $item->item_id]
                        ));

                    $cleanSection->categories[] = $cleanSubCategory;
                }
            }

            $this->itemsProcessed++;
            $this->snapshot->setProgress($this->itemsProcessed / $this->itemsToProcess);

            $assembledSections[] = $cleanSection;
        }

        return $assembledSections;
    }

    private function getAudioGroups(): array
    {

        $audioAssets = $this->cycle->assets()->whereIn('asset_type', [AssetTypeEnum::Music])->get();
        $this->itemsToProcess += count($audioAssets);
        $this->info("Found " . count($audioAssets) . " audio groups");

        $audioGroups = [];

        /** @var Asset $asset */
        foreach ($audioAssets as $asset) {
            $audioGroup = $this->getAssetFields($asset);

            $audioGroup->audioItems = $asset->childAssets()
                ->get()->map(fn ($child) => array_merge(
                    ['idRef' => $child->uuid ?: $child->id]
                ));

            $audioGroups[] = $audioGroup;
        }


        return $audioGroups;
    }

    private function getVideoGroups(): array
    {
        $videoAssets = $this->cycle->assets()->whereIn('asset_type', [AssetTypeEnum::Series])->get();
        $this->itemsToProcess += count($videoAssets);
        $this->info("Found " . count($videoAssets) . " video groups");
        $videoGroups = [];

        foreach ($videoAssets as $asset) {
            $videoGroup = $this->getAssetFields($asset);
            $videoGroup->videoItems = $asset->childAssets()
                ->get()->map(fn ($child) => array_merge(
                    ['idRef' => $child->uuid ?: $child->id]
                ));
            $videoGroups[] = $videoGroup;
        }

        return $videoGroups;
    }

    private function languageKeys($collection): array
    {

        $languageKeys = [];

        foreach ($collection as $item) {
            if (! $item->language) {
                continue;
            }
            $languageKeys[$item->language->bcp_47_code] = $item->value;
        }

        return $languageKeys;
    }

    private function getInterstitialAds(): array
    {
        return [];
    }

    public function getPrerollAds(): array
    {
        return [];
    }

    private function getAdGroups(): array
    {
        return [];
    }

    private function getFeatured(): array
    {
        return [];
    }

    private function getFleets(): array
    {
        return [];
    }

    private function getAvailabilityRules(): array
    {
        return [];
    }

    private function getBadgeStyles(): array
    {
        return [];
    }

    private function getBadgeTexts(): array
    {
        return [];
    }

    private function createMediaFile($assetMedia, $fileType)
    {
        $fileName = $assetMedia->file_name;

        $mediaFile = new stdClass();
        $mediaFile->id = $assetMedia->uuid ?? $assetMedia->id;
        $mediaFile->type = $fileType === 'video' ? $this->getVideoType($fileName) : 'audio';
        $mediaFile->name = $fileName;
        $mediaFile->viasatId = $assetMedia->viasat_id ?? null;

        return $mediaFile;
    }

    // Let's use the file name to determine the type of video
    private function getVideoType($fileName)
    {
        if (preg_match('/_Movie_Feature_/i', $fileName)) {
            return 'videoMain';
        }
        if (preg_match('/_Movie_Trailer_/i', $fileName)) {
            return 'videoTrailer';
        }

        return 'videoMain';
    }
}
