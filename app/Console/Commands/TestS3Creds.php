<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestS3Creds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-s3-creds';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        if (app()->environment() !== 'production') {
            try {
                $files = Storage::allFiles();
                $this->info("Successfully connected to S3 to READ");

                $fileTest = uniqid('test-file');
                Storage::put($fileTest, "test");
                $this->info("Successfully connected to S3 to WRITE");

                Storage::delete($fileTest);
                $this->info("Successfully connected to S3 to DELETE");

            } catch (\Exception $e) {
                $this->error($e->getMessage());
                $this->error("Credential Test Failed. Please check your .env file.");
            }
        }
        else {
            $this->warn("Not testing in Production.");
        }

    }
}
