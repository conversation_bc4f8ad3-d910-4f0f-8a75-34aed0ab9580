<?php

namespace App\Models;

use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property mixed $order
 * @property mixed $item
 */

#[\AllowDynamicProperties]
class CategoryItem extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = ['category_id', 'order', 'asset_id'];
    protected $hidden = ['id', 'created_at', 'updated_at'];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function item(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'asset_id');
    }
}
