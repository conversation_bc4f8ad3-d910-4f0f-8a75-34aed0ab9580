<?php

namespace App\Models;

use App\Models\Enums\FieldEnum;
use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

// Defines individual metadata fields within a cycle.

// - `id`: Unique identifier for the field.
// - `field_type_id`: Links to a predefined field type.
// - `name`: The name of the field (e.g., `Title`).
// - `ui_key`: e.g. `title` (optional<Enum>)
// - `collection_id` (optional)

/**
 * @property null $value
 * @property null $asset_image
 */
#[\AllowDynamicProperties]
class Field extends Model
{
    /** @use HasFactory<\Database\Factories\FieldFactory> */
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    protected $fillable = [
        'name',
        'field_type',
        'schema_id',
        'use_languages',
        'collection_id',
        'input_key',
        'output_key',
        'asset_types',
        'is_localizable',
        'is_external_api_value_source',
        'is_external_api_key_field',
        'is_size_field'
    ];

    protected $casts = [
        'field_type' => FieldEnum::class,
        'collection_id' => 'integer',
        'asset_types' => 'array',
        'is_localizable' => 'boolean',
        'is_external_api_key_field' => 'boolean',
        'is_external_api_value_source' => 'boolean',
        'is_size_field' => 'boolean',
        'use_languages' => 'boolean'
    ];

    public function schema(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Schema::class);
    }

    public function fieldValues(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FieldValue::class);
    }

    public function assetImages(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(AssetImage::class);
    }

    public function isImage(): bool
    {
        return $this->field_type === FieldEnum::Image;
    }

    public function validatorFields(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ValidatorField::class);
    }

    public function assetVideo(): HasMany
    {
        return $this->hasMany(AssetVideo::class);
    }

    public function assetAudio(): HasMany
    {
        return $this->hasMany(AssetAudio::class);
    }

    public function withValue($assetID, $languageID = null): static
    {
        $this->value = match ($this->field_type) {
            FieldEnum::Video => $this->getAssetVideoValue($assetID),
            FieldEnum::Audio => $this->getAssetAudioValue($assetID),
            default => $this->getValue($assetID, $languageID)
        };

        // If this is an image, also include the asset_image!
        if ($this->field_type === FieldEnum::Image) {
            $this->withAssetImage($assetID);
        }

        return $this;
    }

    public function getValue($assetID, $languageID = null)
    {
        $hasLocalizedValue = $this->is_localizable && $this->field_type === FieldEnum::String;

        return $this->fieldValues()
            ->with(['issues' => fn ($query) => $query->latest()->limit(1)])
            ->when($hasLocalizedValue && $languageID, function ($query) use ($languageID) {
                return $query->where('language_id', $languageID);
            })
            ->where('asset_id', $assetID)
            ->first();
    }

    public function getAssetVideoValue($assetID)
    {
        return $this->assetVideo()
            ->with(['issues' => fn ($q) => $q->latest()->limit(1)])
            ->where('asset_id', $assetID)->first();
    }

    public function getAssetAudioValue($assetID)
    {
        return $this->assetAudio()
            ->with(['issues' => fn ($q) => $q->latest()->limit(1)])
            ->where('asset_id', $assetID)->first();
    }


    public function withAssetImage($assetID)
    {
        $this->asset_image = $this->getAssetImage($assetID);
        return $this;
    }

    public function getAssetImage($assetID)
    {
        return $this->assetImages()
            ->with(['issues' => fn ($query) => $query->latest()->limit(1)])
            ->where('asset_id', $assetID)->first();
    }

    public function addValue($assetID, $value)
    {
        /** @var FieldValue $fieldValue */
        $fieldValue = $this->fieldValues()->where('asset_id', $assetID)->first();
        if ($fieldValue->exists) {
            $fieldValue = $this->fieldValues()->create([
                'asset_id' => $assetID,
            ]);
        }
        $fieldValue->setValue($value);
        $fieldValue->save();
    }
}
