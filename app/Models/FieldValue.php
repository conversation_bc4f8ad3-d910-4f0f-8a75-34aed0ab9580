<?php

namespace App\Models;

use App\Jobs\MatchFieldValueToImage;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Enums\FieldEnum;
use App\Models\{Collection, CollectionValue};

/**
 * @property mixed $field
 * @property mixed $asset
 * @property mixed $name
 * @property false|string $value
 */
#[\AllowDynamicProperties]
class FieldValue extends Model
{
    /** @use HasFactory<\Database\Factories\FieldValueFactory> */
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['value', 'field_id', 'asset_id', 'language_id'];

    protected $attributes = [
        'value' => 'json'
    ];

    public static function booted(): void
    {
        // If a field value is created (via import) or has a value field updated, and the field is an image, we can attempt to match it to an image.
        static::created(function ($model) {
            if ($model->field && $model->field->isImage() && isset($model->value)) {
                $model->attemptImageMatching();
            }
        });
        static::updated(function ($model) {
            if ($model->field && $model->field->isImage() && isset($model->value) && $model->isDirty('value')) {
                $model->attemptImageMatching();
            }
        });
    }

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class);
    }

    public function getOutput(): array
    {
        return [
            $this->getProperty() => $this->getValue()
        ];
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    public function getProperty(): string
    {
        return $this->field->name;
    }

    public function setValue($value): void
    {
        if ($this->field && $this->field->field_type === FieldEnum::Dropdown) {
            $this->setDropdownValue($value);
            return;
        }

        $this->value = $value;
    }

    public function getValue(): false|string
    {
        // Later this might be used in the export to pull the necessary value (if it's a dropdown we need to find the localized collection value label)
        return $this->value ?? false;
    }

    private function getCollectionItems($value)
    {
        $value = json_decode($value);
        if (isset($value) && is_array($value)) {
            $collectionValues = CollectionValue::whereIn('id', $value)->get();
            $res = implode(",", $collectionValues->map(function ($item) {
                return $item->label;
            })->values()->all());
        }
        else {
            $collectionValue = CollectionValue::find($value);
            $res = $collectionValue->label;
        }
        return $res;
    }

    public function attemptImageMatching(): void
    {
        MatchFieldValueToImage::dispatch($this->id);
    }

    public function getFormattedDropdownValue(): string
    {
        if ($this->field->field_type !== FieldEnum::Dropdown) {
            return $this->getValue();
        }

        $value = $this->getValue();

        if (empty($value)) {
            return '';
        }

        if($this->field->use_languages) {
            $language = Language::find($value);
            return $language ? $language->ife_code : '';
        }

        if (!$this->field->collection_id) {
            return is_array($value) ? implode(', ', $value) : (string)$value;
        }

        if ($this->field->is_multi_select) {
            if (is_string($value)) {
                $ids = json_decode($value, true);
            } else {
                $ids = is_array($value) ? $value : [$value];
            }

            if (empty($ids)) {
                return '';
            }

            $collectionValues = CollectionValue::whereIn('id', $ids)
                ->where('collection_id', $this->field->collection_id)
                ->get();

            if ($collectionValues->isEmpty()) {
                return '';
            }

            return $collectionValues->pluck('label')->implode(', ');
        }

        $collectionValue = CollectionValue::where('id', $value)
            ->where('collection_id', $this->field->collection_id)
            ->first();


        return $collectionValue ? $collectionValue->label : '';
    }

    private function setDropdownValue($value): void
    {
        if (!$this->field->collection_id) {
            $this->value = $value;
            $isLang = $this->field->use_languages;
            if($isLang) {
                $language = Language::where('ife_code', $value)->first();
                if ($language) {
                    $this->value = $language->id;
                }
            };

            return;
        }

        $collection = Collection::find($this->field->collection_id);

        if (!$collection) {
            $this->value = $value;
            return;
        }

        if ($this->field->is_multi_select) {
            if ($this->isJsonArray($value) || is_array($value)) {
                $this->value = is_array($value) ? json_encode($value) : $value;
                return;
            }

            $labels = array_map('trim', explode(',', $value));
            $ids = [];

            $availableValues = $collection->collectionValues()->get(['id', 'label'])->toArray();

            foreach ($labels as $label) {
                if (empty($label)) {
                    continue;
                }

                $collectionValue = $collection->collectionValues()
                    ->whereRaw('LOWER(label) = ?', [strtolower($label)])
                    ->first();

                if ($collectionValue) {
                    $ids[] = $collectionValue->id;
                } else {
                    // Raise an issue here
                    $ids[] = $label;
                }
            }

            if (count($ids) > 0) {
                $this->value = json_encode($ids);
            } else {
                $this->value = $value;
            }
        } else {
            $collectionValue = $collection->collectionValues()
                ->whereRaw('LOWER(label) = ?', [strtolower(trim($value))])
                ->first();

            if ($collectionValue) {
                $this->value = $collectionValue->id;

            } else {
                // Raise an issue here
                $this->value = $value;
            }
        }
    }

    private function isJsonArray($value): bool
    {
        if (!is_string($value)) {
            return false;
        }

        $decoded = json_decode($value, true);
        return (json_last_error() === JSON_ERROR_NONE) && is_array($decoded);
    }
}
