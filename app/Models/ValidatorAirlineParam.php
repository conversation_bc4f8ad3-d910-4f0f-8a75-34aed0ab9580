<?php

namespace App\Models;

use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ValidatorAirlineParam extends Model
{
    use LogsActivity;

    protected $fillable = ['validator_id', 'airline_id', 'parameters'];

    protected $casts = [
        'parameters' => 'json'
    ];

    public function validator(): BelongsTo
    {
        return $this->belongsTo(Validator::class);
    }

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }
}
