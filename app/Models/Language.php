<?php

namespace App\Models;

use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

#[\AllowDynamicProperties]
class Language extends Model
{
    use LogsActivity;

    protected $fillable = [
        'ife_code',
        'bcp_47_code',
        'iso_639_2_t_code',
        'eng_description',
        'local_description',
    ];

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public static function getDefaultLanguageId()
    {
        // First, ensure 'ENG' language exists
        $language = self::firstOrCreate(
            ['ife_code' => 'ENG'],
            [
                'bcp_47_code' => 'en',
                'iso_639_2_t_code' => 'eng',
                'eng_description' => 'English',
                'local_description' => 'English'
            ]
        );

        return $language->id;
    }

}
