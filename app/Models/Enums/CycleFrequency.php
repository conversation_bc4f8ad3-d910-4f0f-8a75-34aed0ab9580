<?php

namespace App\Models\Enums;

enum CycleFrequency: string
{
    case Monthly = 'monthly';
    case BiMonthly = 'bi-monthly';
    case Quarterly = 'quarterly';
    case SemiAnnually = 'semi-annually';
    case Annually = 'annually';

    public function label()
    {
        return match ($this) {
            CycleFrequency::Monthly => 'Monthly',
            CycleFrequency::BiMonthly => 'Bi Monthly',
            CycleFrequency::Quarterly => 'Quarterly',
            CycleFrequency::SemiAnnually => 'Semi Annually',
            CycleFrequency::Annually => 'Annually',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }

    public function toMonth(): int
    {
        return match($this) {
            CycleFrequency::Monthly => 1,
            CycleFrequency::BiMonthly => 2,
            CycleFrequency::Quarterly => 3,
            CycleFrequency::SemiAnnually => 6,
            CycleFrequency::Annually => 12,
        };
    }
}
