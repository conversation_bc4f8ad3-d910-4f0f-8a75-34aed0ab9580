<?php

namespace App\Models\Enums;

enum ValidatorGroupEnum: string
{
    case Category = 'category';
    case DateOrder = 'date_order';

    public function label()
    {
        return match ($this) {
            ValidatorGroupEnum::Category => 'Category',
            ValidatorGroupEnum::DateOrder => 'Date Order',
        };
    }

    public static function contains(string $value)
    {
        return self::tryFrom($value);
    }
}
