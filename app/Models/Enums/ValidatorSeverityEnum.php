<?php

namespace App\Models\Enums;

enum ValidatorSeverityEnum: string
{
    case Warning = 'warning';
    case Error = 'error';

    private const DEFAULT = self::Warning;

    public function label()
    {
        return match($this){
            ValidatorSeverityEnum::Warning => 'Warning',
            ValidatorSeverityEnum::Error => 'Error',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value) ?? self::DEFAULT;
    }

    public function toIntLevel(): int
    {
        return match($this) {
            ValidatorSeverityEnum::Warning => 1,
            ValidatorSeverityEnum::Error => 2,
        };
    }
}