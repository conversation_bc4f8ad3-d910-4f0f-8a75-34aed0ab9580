<?php

namespace App\Models\Enums;

enum FieldEnum: string
{
    case String = 'string';
    case Dropdown = 'dropdown';
    case Boolean = 'boolean';
    case Datetime = 'datetime';
    case Image = 'image';
    case Video = 'video';
    case Audio = 'audio';

    public function label()
    {
        return match ($this) {
            FieldEnum::String => 'string',
            FieldEnum::Dropdown => 'dropdown',
            FieldEnum::Boolean => 'boolean',
            FieldEnum::Datetime => 'datetime',
            FieldEnum::Image => 'image',
            FieldEnum::Video => 'video',
            FieldEnum::Audio => 'audio',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
