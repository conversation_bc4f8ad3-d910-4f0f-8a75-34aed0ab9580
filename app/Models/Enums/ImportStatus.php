<?php

namespace App\Models\Enums;

enum ImportStatus: string
{
    case Pending = 'pending';
    case Good = 'good';
    case Warn = 'warning';
    case Error = 'error';

    public function label()
    {
        return match ($this) {
            ImportStatus::Pending => 'Pending',
            ImportStatus::Good => 'Good',
            ImportStatus::Warn => 'Warn',
            ImportStatus::Error => 'Error',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
