<?php 

namespace App\Models\Enums;

enum IssueEnum: string 
{
    case Metadata = 'metadata';
    case Images = 'images';
    case Content = 'content'; 

    const DEFAULT = self::Metadata;

    public function label()
    {
        return match($this) {
            IssueEnum::Metadata => 'Metadata',
            IssueEnum::Images => 'Images',
            IssueEnum::Content => 'Content',
        };
    }

    public static function contains($val): self|null
    {
        return self::tryFrom($val) ?? self::DEFAULT;
    }
}