<?php

namespace App\Models\Enums;

use App\Rules\{
    RequiredRule,
    StringRule,
    MinRule,
    CaseStyleRule,
    FormattingRule,
    ValueListRule,
    MaxStringLengthRule,
    EnumerationRule,
    FileNamePatternRule,
    DuplicateCheckRule,
    MaxCountRule,
    AtLeastOneCategoryRule,
    UniqueInCycle,
    UniqueInSeason,
    UniqueInTVSeries,
    PositiveNumberRule,
    MaxYearRule,
    MediaFileExistsRule,
    ImageSpecRule
};
use App\Rules\Asset\DateOrderRule;
use App\Rules\Category\{MaxAssignedCategoriesRule, CategoryContentTypeMatchRule};

enum ValidatorRulesEnum: string
{
    case Required = RequiredRule::class;
    case String = StringRule::class;
    case Min = MinRule::class;
    case CaseStyle = CaseStyleRule::class;
    case Formatting = FormattingRule::class;
    case DateOrder = DateOrderRule::class;
    case ValueList = ValueListRule::class;
    case MaxStringLength = MaxStringLengthRule::class;
    case Enumeration = EnumerationRule::class;
    case FileNamePattern = FileNamePatternRule::class;
    case DuplicateCheck = DuplicateCheckRule::class;
    case MaxCount = MaxCountRule::class;
    case AtLeastOneCategory = AtLeastOneCategoryRule::class;
    case UniqueInCycle = UniqueInCycle::class;
    case PositiveNumber = PositiveNumberRule::class;
    case MaxYear = MaxYearRule::class;
    case MediaFileExists = MediaFileExistsRule::class;
    case ImageSpec = ImageSpecRule::class;
    case UniqueInSeason = UniqueInSeason::class;
    case UniqueInTVSeries = UniqueInTVSeries::class;
    case MaxAssignedCategories = MaxAssignedCategoriesRule::class;
    case CategoryContentTypeMatch = CategoryContentTypeMatchRule::class;

    public function label()
    {
        return match ($this) {
            ValidatorRulesEnum::Required => 'Required',
            ValidatorRulesEnum::String => 'String',
            ValidatorRulesEnum::Min => 'Min',
            ValidatorRulesEnum::CaseStyle => 'Case Style',
            ValidatorRulesEnum::Formatting => 'Formatting',
            ValidatorRulesEnum::DateOrder => 'Date Order',
            ValidatorRulesEnum::ValueList => 'Value List',
            ValidatorRulesEnum::MaxStringLength => 'Max String Length',
            ValidatorRulesEnum::Enumeration => 'Enumeration',
            ValidatorRulesEnum::FileNamePattern => 'File Name pattern',
            ValidatorRulesEnum::DuplicateCheck => 'Duplicate Check',
            ValidatorRulesEnum::MaxCount => 'Max Count',
            ValidatorRulesEnum::AtLeastOneCategory => 'At Least One Category',
            ValidatorRulesEnum::UniqueInCycle => 'Unique In Cycle',
            ValidatorRulesEnum::UniqueInSeason => 'Unique In Season',
            ValidatorRulesEnum::UniqueInTVSeries => 'Unique In TV Series',
            ValidatorRulesEnum::PositiveNumber => 'Positive Number',
            ValidatorRulesEnum::MaxYear => 'Max Year',
            ValidatorRulesEnum::MediaFileExists => 'Media File Exists',
            ValidatorRulesEnum::ImageSpec => 'Image Spec',
            ValidatorRulesEnum::MaxAssignedCategories => 'Max Assigned Categories',
            ValidatorRulesEnum::CategoryContentTypeMatch => 'Category Content Type Match',
        };
    }


    public static function getLabel(string $value)
    {
        $case = self::tryFrom($value);
        return $case?->label();
    }


    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
