<?php

namespace App\Models\Enums;

enum AssetStatus: string
{
    case Expiring = 'Expiring';
    case Holdover = 'Holdover';
    case New = 'New';
    case Recalled = 'Recalled';

    public function label()
    {
        return match ($this) {
            AssetStatus::Expiring => 'Expiring',
            AssetStatus::Holdover => 'Holdover',
            AssetStatus::New => 'New',
            AssetStatus::Recalled => 'Recalled',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
