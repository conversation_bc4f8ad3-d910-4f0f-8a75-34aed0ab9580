<?php

namespace App\Models\Enums;

enum AssetTypeEnum: string
{
    case Movie = 'movie';
    case Series = 'series';
    case Music = 'music';
    case Audiobook = 'audiobook';
    case Podcast = 'podcast';
    // subtypes
    case Episode = 'episode';
    case Track = 'track';

    public function label()
    {
        return match ($this) {
            AssetTypeEnum::Movie => 'Movie',
            AssetTypeEnum::Series => 'TV Series',
            AssetTypeEnum::Audiobook => 'Audiobook',
            AssetTypeEnum::Podcast => 'Podcast',
            AssetTypeEnum::Music => 'Music',
            AssetTypeEnum::Track => 'Track',
            AssetTypeEnum::Episode => 'Episode',
        };
    }

    public function pluralLabel()
    {
        return match ($this) {
            AssetTypeEnum::Movie => 'Movies',
            AssetTypeEnum::Series => 'TV Series',
            AssetTypeEnum::Podcast => 'Podcasts',
            AssetTypeEnum::Audiobook => 'Audiobooks',
            AssetTypeEnum::Music => 'Music',
            AssetTypeEnum::Track => 'Tracks',
            AssetTypeEnum::Episode => 'Episodes',
        };
    }

    public function fileType()
    {
        return match ($this) {
            AssetTypeEnum::Movie => 'video',
            AssetTypeEnum::Series => 'video',
            AssetTypeEnum::Audiobook => 'audio',
            AssetTypeEnum::Podcast => 'audio',
            AssetTypeEnum::Music => 'audio',
            AssetTypeEnum::Track => 'audio',
            AssetTypeEnum::Episode => 'video',
        };
    }

    public function routeValue()
    {
        return match ($this) {
            AssetTypeEnum::Movie => 'movies',
            AssetTypeEnum::Series => 'series',
            AssetTypeEnum::Audiobook => 'audiobooks',
            AssetTypeEnum::Podcast => 'podcasts',
            AssetTypeEnum::Music => 'music',
            AssetTypeEnum::Track => 'tracks',
            AssetTypeEnum::Episode => 'episodes',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }

    public function isTopLevel(): bool
    {
        return match ($this) {
            AssetTypeEnum::Movie,
            AssetTypeEnum::Series,
            AssetTypeEnum::Audiobook,
            AssetTypeEnum::Podcast,
            AssetTypeEnum::Music => true,
            default => false,
        };
    }

    public function isSecondLevel(): bool
    {
        return match ($this) {
            AssetTypeEnum::Track,
            AssetTypeEnum::Episode => true,
            default => false,
        };
    }
    
    public function getParentType(): AssetTypeEnum|null
    {
        return match ($this) {
            AssetTypeEnum::Track => AssetTypeEnum::Music,
            AssetTypeEnum::Episode => AssetTypeEnum::Series,
            default => null,
        };
    }

    public function isParent(): bool
    {
        return match ($this) {
            AssetTypeEnum::Series,
            AssetTypeEnum::Music => true,
            default => false,
        };
    }
}
