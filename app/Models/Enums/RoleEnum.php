<?php

namespace App\Models\Enums;

enum RoleEnum: string 
{
    case SuperAdmin = 'super_admin';
    case ViasatContentOps = 'viasat_content_ops';
    case CspEdit = 'csp_edit';
    case CspReadOnly = 'csp_read_only';
    case AirlineReadOnly = 'airline_read_only';

    const DEFAULT = self::CspReadOnly;

    public function label(): string
    {
        return match($this) {
            self::SuperAdmin => 'Super Admin',
            self::ViasatContentOps => 'Viasat Content Ops',
            self::CspEdit => 'CSP - edit access',
            self::CspReadOnly => 'CSP - read only',
            self::AirlineReadOnly => 'Airline - read only',
        };
    }

    public static function contains($val): self
    {
        return self::tryFrom($val) ?? self::DEFAULT;
    }
}
