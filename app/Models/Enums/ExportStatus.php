<?php

namespace App\Models\Enums;

enum ExportStatus: string
{
    case Pending = 'pending';
    case Processing = 'processing';
    case Ready = 'ready';
    case Failed = 'failed';

    public function label()
    {
        return match ($this) {
            ExportStatus::Pending => 'Pending',
            ExportStatus::Processing => 'Processing',
            ExportStatus::Failed => 'Failed',
            ExportStatus::Ready => 'Ready',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
