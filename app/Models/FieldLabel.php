<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

#[\AllowDynamicProperties]
class FieldLabel extends Model
{
    /** @use HasFactory<\Database\Factories\FieldFactory> */
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['field_id', 'language_id', 'value'];

    protected $casts = [];

    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }
}
