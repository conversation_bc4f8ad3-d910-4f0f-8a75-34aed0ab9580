<?php

namespace App\Models;

use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

#[\AllowDynamicProperties]
class Collection extends Model
{
    /** @use HasFactory<\Database\Factories\CollectionFactory> */
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    protected $fillable = ['name', 'is_localizable', 'schema_id', 'viasat_only', 'output_key'];

    protected $casts = [
        'is_localizable' => 'boolean',
        'viasat_only' => 'boolean',
    ];

    public function collectionValues(): HasMany
    {
        return $this->hasMany(CollectionValue::class);
    }

    public function schema(): BelongsTo
    {
        return $this->belongsTo(Schema::class);
    }

    public function delete(): ?bool
    {
        // Soft delete the collection and its associated values
        $this->collectionValues()->delete();
        return parent::delete();
    }

    public function getUserPermissions(User $user): array
    {
        return ['can_edit_items' => $user->has_unrestricted_edit_role || !$this->viasat_only];
    }

    public function withUserPermissions(User $user): static
    {
        $this->user_permissions = $this->getUserPermissions($user);

        return $this;
    }
}
