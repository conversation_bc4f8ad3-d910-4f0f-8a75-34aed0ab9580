<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

#[\AllowDynamicProperties]
class Collection extends Model
{
    /** @use HasFactory<\Database\Factories\CollectionFactory> */
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['name', 'is_localizable', 'schema_id'];

    protected $casts = [
        'is_localizable' => 'boolean',
    ];

    public function collectionValues(): HasMany
    {
        return $this->hasMany(CollectionValue::class);
    }

    public function delete(): ?bool
    {
        // Soft delete the collection and its associated values
        $this->collectionValues()->delete();
        return parent::delete();
    }
}
