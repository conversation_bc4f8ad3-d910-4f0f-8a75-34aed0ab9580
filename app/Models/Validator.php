<?php

namespace App\Models;

use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[\AllowDynamicProperties]
class Validator extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = ['name', 'description', 'parameters', 'group'];

    public function validatorField(): HasMany
    {
        return $this->hasMany(ValidatorField::class);
    }

    public function validatorFieldType(): HasMany
    {
        return $this->hasMany(ValidatorFieldType::class);
    }
}
