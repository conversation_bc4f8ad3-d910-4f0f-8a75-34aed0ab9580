<?php

namespace App\Models;

use App\Console\Commands\GetSnapshot;
use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

#[\AllowDynamicProperties]
class Snapshot extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'cycle_id',
        'is_completed',
        'is_failed',
        'is_running',
        'progress',
        'filename',
        'generated_with_errors',
        'user_id'
    ];

    protected $appends = [
        'signed_url'
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'is_failed' => 'boolean',
        'is_running' => 'boolean',
    ];

    public function cycle(): BelongsTo
    {
        return $this->belongsTo(Cycle::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function setProgress(float $progress): void
    {
        $progress = min(($progress * 100), 100);
        $this->progress = $progress;
        $this->save();
    }

    public function completed(): void
    {
        $this->is_running = false;
        $this->is_completed = true;
        $this->progress = 100;
        $this->save();
    }

    public function getSignedUrlAttribute(): ?string
    {
        if ($this->filename) {
            return Storage::temporaryUrl(GetSnapshot::SNAPSHOT_ROOT . $this->filename, now()->addHours(3));
        }
        return null;
    }

}
