<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Enums\AssetStatus;

/**
 * @property mixed $schema
 */

#[\AllowDynamicProperties]
class Cycle extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'start_date',
        'end_date',
        'is_locked',
        'airline_id',
        'schema_id',
    ];

    protected $casts = [
        'is_locked' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime'
    ];

    protected $attributes = [
        'is_locked' => false,
    ];

    // Let's only include values that don't query other tables.
    protected $appends = [
        'description'
    ];

    public function categories(): HasMany
    {
        return $this->hasMany(Category::class);
    }

    public function schema(): BelongsTo
    {
        return $this->belongsTo(Schema::class);
    }

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    public function assets(): BelongsToMany
    {
        return $this->belongsToMany(Asset::class);
    }

    public function getDescriptionAttribute(): string
    {
        // format like MAY-25
        return strtoupper($this->start_date->format('M-y'));
    }

    public function getAirlineNameAttribute(): string
    {
        return $this->airline->name ?? 'N/A';
    }

    public function getStatusCountsAttribute(): array
    {
        return [
            'new' => $this->assets()->where('status', AssetStatus::New)->count(),
            'holdover' => $this->assets()->where('status', AssetStatus::Holdover)->count(),
            'expiring' => $this->assets()->where('status', AssetStatus::Expiring)->count(),
            'recalled' => $this->assets()->where('status', AssetStatus::Recalled)->count(),
        ];
    }

    public function getAllCategoryItems()
    {
        $categoryIDs = $this->categories()->pluck('id')->toArray();
        $assets = CategoryItem::whereIn('category_id', $categoryIDs)
            ->orderBy('order')
            ->get();

        $categories = CategoryClosure::whereIn('ancestor_id', $categoryIDs)
            ->orderBy('order')
            ->get();

        $formattedItems = [];
        foreach ($assets as $asset) {
            $formattedItems[] = [
                'category_id' => $asset->category_id,
                'item_id' => $asset->asset_id,
                'order' => $asset->order,
                'type' => 'asset'
            ];
        }

        foreach ($categories as $category) {
            $formattedItems[] = [
                'category_id' => $category->ancestor_id,
                'item_id' => $category->descendant_id,
                'order' => $category->order,
                'type' => 'category'
            ];
        }

        return collect($formattedItems);
    }

    public function clearAllCategoryItems()
    {
        $categoryIDs = $this->categories()->pluck('categories.id')->toArray();

        if (!empty($categoryIDs)) {
            CategoryItem::whereIn('category_id', $categoryIDs)->delete();
            CategoryClosure::whereIn('ancestor_id', $categoryIDs)
                ->whereIn('descendant_id', $categoryIDs)
                ->delete();

        }
    }

    public function scopeForUser(Builder $query, ?User $user): void
    {
        if ($user !== null && $user->has_unrestricted_edit_role) {
            return;
        }

        $query->whereIn('airline_id', $user->getAirlineIdsForOrganization());
    }

}
