<?php

namespace App\Models;

use App\Jobs\ActivityLogJob;
use Illuminate\Support\Arr;
use Spatie\Activitylog\Models\Activity;

class ActivityLog extends Activity
{
    protected $appends = ['activity_description', 'subject_model_name', 'subject_title', 'subject_route'];

    public function getSubjectModelNameAttribute()
    {
        $exploded = explode('\\', $this->subject_type);
        return end($exploded);
    }

    public function getSubjectTable()
    {
        if ($this->subject) {
            return $this->subject->getTable();
        }
    }

    public function getSubjectRouteAttribute()
    {
        // It can't be routed if the model is deleted.
        if ($this->description === "deleted") {
            return null;
        }

        // Otherwise let's get the route URL based on the model's table...
        $table = $this->getSubjectTable();
        if ($table === "airlines") {
            return "/settings/airlines/{$this->subject->id}/details";
        }
        if ($table === "cycles") {
            return "/airlines/{$this->subject->airline_id}/cycles/{$this->subject->id}/details";
        }
        if ($table === "users") {
            return "/settings/users/{$this->subject->id}/details";
        }
        if ($table === "organizations") {
            return "/settings/organizations/{$this->subject->id}";
        }
        if ($table === "assets") {
            $cycle = $this->subject->getLatestCycle();
            return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/metadata/{$this->subject->route_value}/{$this->subject->id}";
        }
        if ($table === "categories") {
            $cycle = $this->subject->cycle;
            return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/categories/{$this->subject->id}";
        }
        if ($table === "field_values") {
            $asset = $this->subject->asset;
            $cycle = $asset->getLatestCycle();
            return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/metadata/{$asset->route_value}/{$asset->id}";
        }
        if ($table === "issues") {
            $asset = $this->subject->asset;
            $cycle = $this->subject->cycle;
            if ($asset && $cycle) {
                return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/metadata/{$asset->route_value}/{$asset->id}";
            }
            if ($cycle) {
                return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/issues"; 
            }
        }
        if ($table === "snapshots") {
            $cycle = $this->subject->cycle;
            return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/dashboard";
        }
        if ($table === "asset_images") {
            $cycle = $this->subject->cycle;
            return "/airlines/{$cycle->airline_id}/cycles/{$cycle->id}/images/all";
        }
        if ($table === "fields") {
            $schema = $this->subject->schema;
            return "/settings/schemas/{$schema->id}/fields/{$this->subject->id}";
        }
        if ($table === "collections") {
            $schema = $this->subject->schema;
            return "/settings/schemas/{$schema->id}/lists/{$this->subject->id}";
        }
        if ($table === "collection_values") {
            $schema = $this->subject->collection->schema;
            return "/settings/schemas/{$schema->id}/lists/{$this->subject->collection->id}";
        }
    }

    public function getSubjectTitleAttribute()
    {
        $table = $this->getSubjectTable();
        if ($table === "airlines") {
            return $this->subject->name;
        }
        if ($table === "cycles") {
            return $this->subject->airline->name . ': ' . $this->subject->description;
        }
        if ($table === "users") {
            return $this->subject->name . ' (' . $this->subject->email . ')';
        }
        if ($table === "organizations") {
            return $this->subject->name;
        }
        if ($table === "assets") {
            return $this->subject->plural_label . ': ' . $this->subject->title;
        }
        if ($table === "categories") {
            return $this->subject->getDefaultTitle();
        }
        if ($table === "field_values") {
            $asset = $this->subject->asset;
            return $asset->title . ': ' . $this->subject->field->name;
        }
        if ($table === "issues") {
            $asset = $this->subject->asset;
            if ($asset) {
                return $asset->plural_label . ': ' . $asset->title;
            }
            $cycle = $this->subject->cycle;
            return $cycle->description . ': ' . $this->subject->description;
        }
        if ($table === "snapshots") {
            return $this->subject->created_at->toDateTimeString();
        }
        if ($table === "asset_images") {
            return $this->subject->file_name;
        }
        if ($table === "fields") {
            return $this->subject->name;
        }
        if ($table === "collections") {
            return $this->subject->name;
        }
        if ($table === "collection_values") {
            return $this->subject->collection->name . ': ' . $this->subject->label;
        }
    }

    public function getActivityDescriptionAttribute()
    {
        $description = $this->description;
        $table = $this->getSubjectTable();
        if ($description === "updated") {
            $newKeys = array_keys($this->properties["attributes"] ?? []);
            $oldKeys = array_keys($this->properties["old"] ?? []);
            if (count($oldKeys) === 1 && count($newKeys) === 1) {
                $attribute = $newKeys[0];
                $oldValue = json_encode($this->properties["old"][$attribute] ?? 'N/A');
                $newValue = json_encode($this->properties["attributes"][$attribute] ?? 'N/A');
                return "Updated {$attribute} from {$oldValue} to {$newValue}";
            }
        }
        if ($table === "issues") {
            return ucfirst("{$description} issue: {$this->subject->description}");
        }
        $title = $this->getSubjectTitleAttribute();
        return ucfirst("{$description} {$title}");
        return $description;
    }

    public function save(array $options = [])
    {
        $payload = [
            'log_name' => $this->log_name,
            'description' => $this->description,
            'subject_type' => $this->subject_type,
            'subject_id' => $this->subject_id,
            'causer_type' => $this->causer_type,
            'causer_id' => $this->causer_id,
            'properties' => json_decode($this->properties, true)
        ];

        $only = ['http_host', 'request_uri', 'http_referer', 'http_user_agent']; // add any you want.
        $payload['properties']['request'] = Arr::only(array_change_key_case(request()->server()), $only);

        $ip = request()->ip();
        $payload['properties']['request']['remote_ip'] = $ip;

        $oldKeys = array_change_key_case($payload['properties']['old'] ?? []);
        $newKeys = array_change_key_case($payload['properties']['attributes'] ?? []);

        // obfuscate any sensitive data keys here before they go into the job queue & db
        $oldKeys = $this->obfuscateKeys($oldKeys);
        $newKeys = $this->obfuscateKeys($newKeys);

        $payload['properties']['old'] = $oldKeys; // old values
        $payload['properties']['attributes'] = $newKeys; // new values

        ActivityLogJob::dispatch($payload);
    }
    
    private function obfuscateKeys(array $keys): array
    {
        $obfuscateKeys = ['password', 'password_confirm'];
        foreach ($keys as $key => $value) {
            if (in_array($key, $obfuscateKeys)) {
                $keys[$key] = "**REMOVED**";
            }
        }
        return $keys;
    }
}