<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use App\Models\Traits\AssetMetadataFileRelations;
use App\Models\Traits\LogsActivity;

class AssetAudio extends Model
{
    use SoftDeletes, AssetMetadataFileRelations, LogsActivity;

    protected $fillable = [
        'cycle_id',
        'airline_id',
        'asset_id',
        'field_id',
        'mime',
        'extension',
        'size',
        'file_name',
        'duration',
        'viasat_id'
    ];

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function setValue(string $value): void
    {
        $this->file_name = $value;
    }

    public function getValue(): string
    {
        return $this->file_name;
    }
}
