<?php

namespace App\Models;

use App\Models\Enums\FieldEnum;
use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

#[\AllowDynamicProperties]
class ValidatorFieldType extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    protected $fillable = ['validator_id', 'field_type', 'enabled'];

    protected $casts = [
        'enabled' => 'boolean',
        'field_type' => FieldEnum::class
    ];

    public function validator(): BelongsTo
    {
        return $this->belongsTo(Validator::class);
    }

    public function setFieldTypeAttribute($value): void
    {
        $this->attributes['field_type'] = FieldEnum::from($value);
    }
}
