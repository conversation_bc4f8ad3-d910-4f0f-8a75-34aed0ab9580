<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CategoryValue extends Model
{
    /** @use HasFactory<\Database\Factories\CategoryValueFactory> */
    use HasFactory;

    protected $fillable = ['category_id', 'value', 'language_id'];

    protected $hidden = ['id', 'category_id', 'created_at', 'updated_at'];

    public function category(): HasOne
    {
        return $this->hasOne(Category::class, 'id', 'category_id');
    }

    public function language(): HasOne
    {
        return $this->hasOne(Language::class, 'id', 'language_id');
    }
}
