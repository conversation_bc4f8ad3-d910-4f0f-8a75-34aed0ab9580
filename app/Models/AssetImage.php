<?php

namespace App\Models;

use App\Jobs\MatchImageToFieldValue;
use App\Services\AwsBucketService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use App\Models\Traits\AssetMetadataFileRelations;
use App\Models\Traits\LogsActivity;

/**
 * When LaraStan/php<PERSON> complains a lot about dynamic/undefined props, you can declare them here.
 * @property mixed $airline
 * @property mixed $field
 * @property mixed $cycle
 * @property mixed $signed_url
 */


#[\AllowDynamicProperties]
class AssetImage extends Model
{
    /** @use HasFactory<\Database\Factories\AssetImageFactory> */
    use HasFactory, SoftDeletes, AssetMetadataFileRelations, LogsActivity;

    protected $fillable = [
        'airline_id',
        'cycle_id',
        'field_id',
        'asset_id',
        'mime',
        'file_name',
        'extension',
        'size',
        'path',
        'width',
        'height',
        'status'
    ];

    protected $attributes = [
        'field_id' => null,
        'asset_id' => null,
        'status' => 'pending'
    ];

    public static function booted(): void
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
            $model->generatePath();
        });

        // If an image is gone from "uploading" to "uploaded", and there is a file_name, we can attempt to match it to a field value.
        static::updated(function ($model) {
            if (
                isset($model->file_name)
                && $model->status === 'uploaded'
                && $model->getOriginal('status') === 'uploading'
            ) {
                $model->attemptAssetMatching();
            }
        });
    }

    public function cycle(): BelongsTo
    {
        return $this->belongsTo(Cycle::class);
    }

    private function generatePath(): void
    {
        $extension = pathinfo($this->file_name, PATHINFO_EXTENSION);
        $this->path = sprintf(
            "aero-metadata/airlines/%s/cycles/%s/%s.%s",
            $this->airline->id,
            $this->cycle->description,
            $this->uuid,
            $extension
        );
    }

    /**
     * Generate a signed upload URL for the asset image.
     *
     * @return string
     */
    public function getSignedUploadURL(): string
    {
        $signedUriData = (new AwsBucketService())->generateSignedUriPut($this->path, $this->mime);
        return $signedUriData['url'];
    }

    public function withSignedDownloadURL(): static
    {
        $this->signed_url = $this->getSignedDownloadURL();
        return $this;
    }

    public function getSignedDownloadURL()
    {
        $signedUriData = (new AwsBucketService())->generateSignedUriGet($this->path);
        return $signedUriData['url'];
    }

    public function attemptAssetMatching(): void
    {
        if (isset($this->asset_id) && isset($this->field_id)) {
            return;
        }
        MatchImageToFieldValue::dispatch($this->getRawOriginal('id'));
    }

    public function attachToFieldValue($fieldValue): void
    {
        $this->field_id = $fieldValue->field_id;
        $this->asset_id = $fieldValue->asset_id;
        $this->status = "matched";
        $this->save();
    }

    public function getValue()
    {
        return $this->path;
    }
}
