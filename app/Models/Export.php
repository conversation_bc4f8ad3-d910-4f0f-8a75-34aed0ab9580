<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

#[\AllowDynamicProperties]
class Export extends Model
{
    /** @use HasFactory<\Database\Factories\ExportFactory> */
    use HasFactory;
    protected $fillable = [
        'filename', 'path', 'status'
    ];

    protected $casts = [
        'processed_at' => 'datetime',
    ];
}
