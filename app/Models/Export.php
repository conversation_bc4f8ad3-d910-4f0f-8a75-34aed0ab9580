<?php

namespace App\Models;

use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

#[\AllowDynamicProperties]
class Export extends Model
{
    /** @use HasFactory<\Database\Factories\ExportFactory> */
    use HasFactory, LogsActivity;

    protected $fillable = [
        'filename', 'path', 'status'
    ];

    protected $casts = [
        'processed_at' => 'datetime',
    ];
}
