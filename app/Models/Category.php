<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

#[\AllowDynamicProperties]
class Category extends Model
{
    /** @use HasFactory<\Database\Factories\CategoryFactory> */
    use HasFactory;
    protected $fillable = ['type', 'viasat_id', 'airline_id', 'cycle_id', 'is_root', 'asset_types', 'origin_id', 'start_date', 'end_date'];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'asset_types' => 'array',
    ];

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function parentCategories()
    {
        return $this->hasManyThrough(Category::class, CategoryClosure::class, 'descendant_id', 'id', 'id', 'ancestor_id')->orderBy('order');
    }

    public function childCategories()
    {
        return $this->hasManyThrough(Category::class, CategoryClosure::class, 'ancestor_id', 'id', 'id', 'descendant_id')
            ->orderBy('order')
            ->with('categoryValues');
    }

    public function addChildCategories($ids)
    {
        $order = $this->childCategories()->max('order') ?? 0;
        $children = [];

        foreach ($ids as $id) {
            $children[] = [
                'ancestor_id' => $this->id,
                'descendant_id' => $id,
                'order' => ++$order
            ];
        }

        CategoryClosure::factory()->createMany($children);
    }

    public function addAssets($ids)
    {
        $order = $this->categoryItems()->max('order') ?? 0;
        $items = [];

        foreach ($ids as $id) {
            $items[] = [
                'category_id' => $this->id,
                'asset_id' => $id,
                'order' => ++$order
            ];
        }

        CategoryItem::factory()->createMany($items);
    }

    public function updateItemsOrder($ids, $type): bool
    {
        // get existing ids order
        $allItemIDs = ($type === 'assets')
            ? $this->categoryItems()->pluck('asset_id')->toArray()
            : $this->childCategories()->pluck('descendant_id')->toArray();

        if (count($allItemIDs) !== count($ids) || array_diff($allItemIDs, $ids) || array_diff($ids, $allItemIDs)) {
            return false;
        }

        // compare new ids to existing ids
        $changedOrder = array_diff_uassoc($ids, $allItemIDs, function ($key1, $key2) {
            if ($key1 == $key2) {
                return 0;
            }
            if ($key1 > $key2) {
                return 1;
            } else {
                return -1;
            }
        });

        // update only changed
        ($type === 'assets')
            ? $this->updateAssetsOrder($changedOrder)
            : $this->updateChildCategoriesOrder($changedOrder);

        return true;
    }

    private function updateAssetsOrder($ids)
    {
        foreach ($ids as $index => $assetID) {
            CategoryItem::where('category_id', $this->id)
                ->where('asset_id', $assetID)
                ->update(['order' => $index]);
        }
    }

    private function updateChildCategoriesOrder($ids)
    {
        foreach ($ids as $index => $categoryId) {
            CategoryClosure::where('ancestor_id', $this->id)
                ->where('descendant_id', $categoryId)
                ->update(['order' => $index]);
        }
    }

    public function removeChildCategories($ids)
    {
        CategoryClosure::where('ancestor_id', $this->id)
            ->whereIn('descendant_id', $ids)
            ->delete();
    }

    public function removeAssets($ids)
    {
        CategoryItem::where('category_id', $this->id)
            ->whereIn('asset_id', $ids)
            ->delete();
    }


    public function categoryItems(): HasManyThrough
    {
        return $this->hasManyThrough(Asset::class, CategoryItem::class, 'category_id', 'id', 'id', 'asset_id')->orderBy('order');
    }

    // categories that contains same asset type as in this category
    public function getAddableCategories()
    {
        if (!$this->asset_types) {
            return [];
        };

        return Category::where('is_root', false)
            ->whereNotIn('id', $this->childCategories()->pluck('descendant_id'))
            ->where('cycle_id', $this->cycle_id)
            ->whereJsonLength('asset_types', '>', 0) // only where asset category has asset types selected
            ->where(function ($q) {     // any match from current category asset types
                collect($this->asset_types)->map(function ($type) use ($q) {
                    $q->orWhereJsonContains('asset_types', $type);
                });
            })
            ->with('categoryValues')
            ->get();
    }

    public function getAddableAssets()
    {
        if (!$this->asset_types) {
            return [];
        };
        return Asset::whereIn('asset_type', $this->asset_types)
            ->whereHas('cycles', fn ($q) => $q->where('cycles.id', $this->cycle_id))
            ->whereNotIn('id', $this->categoryItems()->pluck('asset_id'))->get();
    }

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    public function getAirlineNameAttribute(): string
    {
        return $this->airline->name ?? 'N/A';
    }

    public function categoryValues(): HasMany
    {
        return $this->hasMany(CategoryValue::class, 'category_id', 'id');
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class, 'category_id', 'id');
    }
}
