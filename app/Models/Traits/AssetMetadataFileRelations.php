<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Field;
use App\Models\Asset;
use App\Models\Airline;
use App\Models\Cycle;
use App\Models\Issue;

trait AssetMetadataFileRelations
{
    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    public function cycle(): BelongsTo
    {
        return $this->belongsTo(Cycle::class);
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class);
    }

    // used in field value service 
    public function getValueAttribute(): string
    {
        return $this->file_name;
    }
}
