<?php

namespace App\Models;

use App\Models\Enums\FieldEnum;
use App\Models\Enums\AssetStatus;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\ValidatorGroupEnum;
use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
Represents a media entity (e.g., movie, TV show, music) with associated metadata.

- `id`: Unique identifier for the asset.
- `cycle_id`: (implicit) Links the asset to a content cycle.
- `values`: (implicit) Collection of metadata field values.
 *
 * @property mixed $schema
 * @property mixed $cycle
 */

#[\AllowDynamicProperties]
class Asset extends Model
{
    /** @use HasFactory<\Database\Factories\AssetFactory> */
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    protected $fillable = ['title', 'start_date', 'end_date', 'asset_type', 'parent_asset_id', 'origin_asset_id', 'version_number', 'origin_cycle_id'];
    protected $hidden = ['pivot'];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    protected const STANDARD_FIELDS = [
        // 'Title',
        'Year of Release',
        'Length',
        'Lang 1',
        'Lang 2',
        'Lang 3',
        'Lang 4',
        'Lang 5',
        'Lang 6',
        'Sub Embed 1',
        'Sub Embed 2',
        'Sub Dynamic 1',
        'Sub Dynamic 2',
        'Closed Caps',
        'Feature File Name (.mp4)',
        'Trailer File Name (.mp4)',
        'Poster Image File Name (.jpg)'
    ];

    // for non "Standard fields", but we want their info included in the standard field section
    protected const STANDARD_FIELD_EXTRAS = [
        'Poster',
    ];

    protected $appends = ['plural_label', 'route_value', 'parent_asset_type', 'available_categories', 'asset_issues'];

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function delete()
    {
        $this->childAssets()->delete();
        return parent::delete();
    }

    public function getParentAssetTypeAttribute(): ?AssetTypeEnum
    {
        return AssetTypeEnum::contains($this->asset_type)->getParentType();
    }

    public function getPluralLabelAttribute(): string
    {
        return AssetTypeEnum::tryFrom($this->asset_type)->pluralLabel();
    }

    public function getRouteValueAttribute(): string
    {
        return AssetTypeEnum::tryFrom($this->asset_type)->routeValue();
    }

    // TODO: if asset category can be used in multiple root categories we need to distinguish relations in category_items table
    public function getAvailableCategoriesAttribute()
    {
        if (!$this->cycle) {
            return [];
        }

        return Category::with(['childCategories' => fn($q) => $q->whereJsonContains('asset_types', $this->asset_type), 'categoryValues'])
            ->where('airline_id', $this->cycle->airline_id)
            ->where('cycle_id', $this->cycle->id)
            ->where('is_root', true)
            ->whereJsonContains('asset_types', $this->asset_type)
            ->get();
    }

    public function getAssetIssuesAttribute()
    {
        if (!$this->cycle) {
            return [];
        }

        return Issue::select('uuid', 'description', 'validator_group')
            ->where('asset_id', $this->id)
            ->whereIn('validator_group', ValidatorGroupEnum::cases()) // we need only issues with group
            ->where('cycle_id', $this->cycle->id)
            ->groupBy('validator_group', 'id')
            ->get();
    }

    public function cycles(): BelongsToMany
    {
        return $this->belongsToMany(Cycle::class);
    }

    public function fieldValues(): HasMany
    {
        return $this->hasMany(FieldValue::class);
    }

    public function videos(): HasMany
    {
        return $this->hasMany(AssetVideo::class);
    }

    public function images(): HasMany
    {
        return $this->hasMany(AssetImage::class);
    }

    public function audio(): HasMany
    {
        return $this->hasMany(AssetAudio::class);
    }


    public function categories(): HasManyThrough
    {
        return $this->hasManyThrough(Category::class, CategoryItem::class, 'asset_id', 'id', 'id', 'category_id');
    }

    public function categoryItems(): HasMany
    {
        return $this->hasMany(CategoryItem::class);
    }

    public function getLatestCycle()
    {
        return $this->cycles()->orderBy('start_date', 'desc')->first();
    }

    public function getCompletionPercentageAttribute(): float|int
    {
        $total = $this->getFieldsCount();
        $filled = $this->fieldValues()->count() + $this->videos()->count() + $this->images()->count() + $this->audio()->count();
        return $filled / $total * 100;
    }

    public function withDefaultTitle($cycle, $languageID)
    {
        // TODO: Maybe don't hardcode Title here, if this won't be what it always is?...
        $titleField = $cycle->schema->fields()->where('name', 'Title')->first();
        $titleFieldValue = $titleField ? $titleField->getValue($this->id, $languageID) : null;
        $this->default_title = $titleFieldValue ? $titleFieldValue->value : $this->title;
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class);
    }

    public function getFields($cycleID)
    {
        $cycle = Cycle::find($cycleID);
        return $cycle->schema->fields->filter(function ($field) {
            return empty($field->asset_types) || in_array($this->asset_type, $field->asset_types);
        })->values();
    }

    public function getFieldsCount()
    {

        $languageCount = $this->cycle->airline->languages()->count();

        $fieldsQuery = $this->cycle->schema->fields()->where(function ($query) {
            $query->whereNull('asset_types')->orWhere('asset_types', 'like', '%' . $this->asset_type . '%');
        });

        // Since DROPDOWNS cannot be really localized, we need to handle them differently...
        $localizedFieldsCount = (clone $fieldsQuery)->where('is_localizable', true)->where('field_type', '!=', FieldEnum::Dropdown)->count();
        $nonLocalizedFieldsCount = (clone $fieldsQuery)->where('is_localizable', false)->orWhere('field_type', FieldEnum::Dropdown)->count();

        return $localizedFieldsCount * $languageCount + $nonLocalizedFieldsCount;
    }

    public function getFieldsWithValues($cycleID, $standard = false)
    {
        // Grab the cycle from the cycle ID.
        $cycle = Cycle::find($cycleID);
        $languageID = request()->languageID;
        // To get field values, we first grab the fields from the cycle's schema.
        // Then filter out any fields that are not associated with the asset type.
        // Then map over the fields and return the field's value.

        $isStandard = $standard || request()->isStandard;

        return $cycle->schema->fields()
            ->where('is_localizable', $languageID ? true : false)
            ->when($isStandard, function ($query) {
                return $query->whereIn('name', array_merge(self::STANDARD_FIELDS, self::STANDARD_FIELD_EXTRAS));
            }, function ($query) {
                return $query->whereNotIn('name', self::STANDARD_FIELDS);
            })
            ->get()
            ->filter(function ($field) {
                return empty($field->asset_types) || in_array($this->asset_type, $field->asset_types);
            })->map(function ($field) use ($languageID) {
                return $field->withValue($this->id, $languageID);
            })->values();
    }

    public function withCompletionPercentage($cycle): static
    {
        $this->cycle = $cycle;
        $this->completion_percentage = $this->getCompletionPercentageAttribute();
        return $this;
    }

    public function childAssets(): HasMany
    {
        return $this->hasMany(Asset::class, 'parent_asset_id');
    }

    public function parentAsset(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'parent_asset_id');
    }

    public function getUserPermissions(User $user, Cycle $cycle): array
    {
        $disabledFields = [];

        $cycleAsset = $cycle->assets()->withPivot('status')->find($this->id);

        $assetStatus = $cycleAsset->pivot->status;
        if (
            $cycleAsset !== null
            && !$user->has_unrestricted_edit_role
            && in_array($assetStatus, [AssetStatus::Holdover->value, AssetStatus::Expiring->value])
        ) {
            $disabledFields[] = 'start_date';
        }

        return ['disabled_fields' => $disabledFields];
    }

    public function withUserPermissions(User $user, Cycle $cycle): static
    {
        $this->user_permissions = $this->getUserPermissions($user, $cycle);

        return $this;
    }

    public function scopeForAirline(Builder $query, int $airlineId): void
    {
        $query->whereHas(
            'cycles',
            fn(Builder $query) => $query->where('airline_id', $airlineId)
        );
    }

    public function getSize()
    {
        $videosSize = $this->videos->pluck('size')->sum();
        $audioSize = $this->audio->pluck('size')->sum();
        $imageSize = $this->images->pluck('size')->sum();

        return $videosSize + $audioSize + $imageSize;
    }
}
