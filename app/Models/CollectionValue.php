<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

/**
 * @property mixed $collectionValues
 */

#[\AllowDynamicProperties]
class CollectionValue extends Model
{
    /** @use HasFactory<\Database\Factories\CollectionValueFactory> */
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['collection_id', 'label', 'language_id', 'collection_value_id'];

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class);
    }

    // This will exist for localized values. Their collection_value_id will be the identifier collection value.
    public function collectionValue(): BelongsTo
    {
        return $this->belongsTo(CollectionValue::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    public function delete(): ?bool
    {
        // If this is a parent collection value, delete children as well.
        if ($this->collectionValues) {
            foreach ($this->collectionValues as $child) {
                $child->delete();
            }
        }

        return parent::delete();
    }
}
