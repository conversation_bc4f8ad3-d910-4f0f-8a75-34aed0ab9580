<?php

namespace App\Models;

use App\Models\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

#[\AllowDynamicProperties]
class Schema extends Model
{
    /** @use HasFactory<\Database\Factories\SchemaFactory> */
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'is_root',
        'name',
        'version_number',
        'airline_id'
    ];

    protected $attributes = [
        'is_root' => false,
        'version_number' => 'integer'
    ];

    // protected $with = [
    //     'airline',
    // ];

    public function fields(): HasMany
    {
        return $this->hasMany(Field::class);
    }

    public function collections(): HasMany
    {
        return $this->hasMany(Collection::class);
    }

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    public function cloneSchema(): Schema
    {
        $newSchema = Schema::create([
            'name' => 'New Schema from ' . now()->format('Y-m-d H:i:s'),
            'version_number' => $this->version_number + 1
        ]);

        // Loop through the old schema's fields and copy them over!
        foreach ($this->fields as $field) {
            /** @var Field $newField */
            $newField = $field->replicate();
            $newField->schema_id = $newSchema->id;
            $newField->save();
        }
        return $newSchema;
    }
}
