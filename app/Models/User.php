<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Sanctum\HasApiTokens;
use App\Models\Enums\RoleEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use App\Models\Traits\LogsActivity;


#[\AllowDynamicProperties]
class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes, HasApiTokens, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'organization_id',
        'role_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var list<string>
     */
    protected $appends = [
        'has_unrestricted_edit_role',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function hasUnrestrictedEditRole(): Attribute
    {
        return new Attribute(
            get: fn () => $this->role !== null && in_array(
                $this->role->name,
                [ RoleEnum::SuperAdmin->value, RoleEnum::ViasatContentOps->value ],
                true
            ),
        );
    }

    public function isSuperAdmin(): bool
    {
        return $this->role !== null && $this->role->name === RoleEnum::SuperAdmin->value;
    }

    public function getAirlineIdsForOrganization(): array
    {
        $organization = Organization::find($this->organization_id);
        return $organization->airlines->modelKeys();
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }
}
