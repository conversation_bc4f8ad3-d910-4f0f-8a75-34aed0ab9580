<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CategoryClosure extends Model
{
    /** @use HasFactory<\Database\Factories\CategoryClosureFactory> */
    use HasFactory;

    protected $fillable = ['ancestor_id', 'descendant_id', 'order'];

    public function descendant()
    {
        return $this->belongsTo(Category::class, 'descendant_id');
    }

    public function ancestor()
    {
        return $this->belongsTo(Category::class, 'ancestor_id');
    }
}
