<?php

namespace App\Models;

use App\Models\Enums\IssueEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

#[\AllowDynamicProperties]
class Issue extends Model
{
    /** @use HasFactory<\Database\Factories\IssueFactory> */
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'cycle_id',
        'description',
        'field_value_id',
        'asset_id',
        'validator_field_id',
        'asset_image_id',
        'type',
        'is_custom',
        'validator_group',
        'category_id',
        'asset_video_id',
        'asset_audio_id'
    ];

    public function setTypeAttribute($value)
    {
        $this->attributes['type'] = IssueEnum::contains($value);
    }

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    public function fieldValue(): BelongsTo
    {
        return $this->belongsTo(FieldValue::class);
    }

    public function validatorField(): BelongsTo
    {
        return $this->belongsTo(ValidatorField::class);
    }
}
