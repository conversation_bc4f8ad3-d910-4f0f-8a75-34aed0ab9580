<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

#[\AllowDynamicProperties]
class ValidatorField extends Model
{
    protected $fillable = ['field_id', 'validator_id', 'parameters', 'severity', 'enabled'];

    protected $casts = [
        'enabled' => 'boolean'
    ];

    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    public function validator(): BelongsTo
    {
        return $this->belongsTo(Validator::class);
    }
}
