<?php

namespace App\Models;

use App\Models\Traits\AssetMetadataFileRelations;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class AssetVideo extends Model
{
    use SoftDeletes;
    use AssetMetadataFileRelations;

    protected $fillable = [
        'cycle_id',
        'airline_id',
        'asset_id',
        'field_id',
        'mime',
        'extension',
        'size',
        'file_name',
        'resolution',
        'duration',
    ];

    public static function booted()
    {
        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function setValue(string $value): void
    {
        $this->file_name = $value;
    }
}
