<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

#[\AllowDynamicProperties]
class Import extends Model
{
    use HasFactory;

    protected $fillable = [
        'file_name',
        'file_path',
        'processing_at',
        'status',
        'errors',
    ];

    protected $casts = [
        'processing_at' => 'datetime',
        'errors' => 'array',
    ];
}
