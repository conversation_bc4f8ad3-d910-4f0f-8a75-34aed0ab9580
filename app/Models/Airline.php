<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\{BelongsTo, BelongsToMany};
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Enums\CycleFrequency;
use App\Models\Traits\LogsActivity;

#[\AllowDynamicProperties]
class Airline extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    protected $fillable = [
        'name',
        'logo_url',
        'group_code',
        'max_total_snapshot_size',
        'max_delta_snapshot_size',
        'max_for_eis_size',
        'cycle_frequency',
        'asset_types',
        'organization_id',
        'recall_period',
        'default_language_id',
    ];

    protected $casts = [
        'cycle_frequency' => CycleFrequency::class,
        'asset_types' => 'array',
        'recall_period' => 'integer',
    ];

    public static function booted()
    {
        static::created(function ($model) {
            $model->languages()->sync(Language::getDefaultLanguageId());
        });
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function languages(): BelongsToMany
    {
        return $this->belongsToMany(Language::class);
    }

    public function defaultLanguage(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    /**
     * Returns the airline's default language id, or the system default if one isn't specified.
     *
     * To be used in cases where a language id is needed, but not necessarily in all cases
     * if you need to check if a language id has been set or not.
     */
    public function getDefaultOrSystemLanguageId(): int
    {
        return $this->default_language_id ?? Language::getDefaultLanguageId();
    }

    public function cycles()
    {
        return $this->hasMany(Cycle::class);
    }

    public function getLogoUrlAttribute($value): string
    {
        return $value ? Storage::temporaryUrl($value, now()->addMinutes(5)) : '';
    }

    public function scopeForUser(Builder $query, User $user): void
    {
        if ($user->has_unrestricted_edit_role) {
            return;
        }

        $query->whereIn('id', $user->getAirlineIdsForOrganization());
    }
}
