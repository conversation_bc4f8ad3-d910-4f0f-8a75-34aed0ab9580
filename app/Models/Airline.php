<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\{BelongsTo, BelongsToMany};
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Enums\CycleFrequency;

#[\AllowDynamicProperties]
class Airline extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'logo_url',
        'icao_code',
        'group_code',
        'max_total_snapshot_size',
        'max_delta_snapshot_size',
        'max_for_eis_size',
        'cycle_frequency',
        'asset_types',
        'organization_id',
        'recall_period'
    ];

    protected $casts = [
        'cycle_frequency' => CycleFrequency::class,
        'asset_types' => 'array',
        'recall_period' => 'integer',
    ];

    public static function booted()
    {
        static::created(function ($model) {
            $model->languages()->sync(Language::getDefaultLanguageId());
        });
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function languages(): BelongsToMany
    {
        return $this->belongsToMany(Language::class);
    }

    public function getLogoUrlAttribute($value): string
    {
        return $value ? Storage::temporaryUrl($value, now()->addMinutes(5)) : '';
    }

    public function getDefaultLanguageID()
    {
        return $this->languages()->first()?->id ?? Language::getDefaultLanguageId();
    }

    public function scopeForUser(Builder $query, User $user): void
    {
        if ($user->has_unrestricted_edit_role) {
            return;
        }

        $query->whereIn('id', $user->getAirlineIdsForOrganization());
    }
}
