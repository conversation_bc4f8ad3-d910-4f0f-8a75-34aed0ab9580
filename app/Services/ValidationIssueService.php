<?php

namespace App\Services;

use App\Models\Enums\IssueEnum;
use App\Models\{AssetImage, Issue, FieldValue, AssetVideo, AssetAudio};

class ValidationIssueService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    // report issue for asset field value
    public function report(FieldValue|AssetVideo|AssetAudio $fieldValue, int $validatorID, string $error, $cycleId, bool $isCustom = false)
    {
        $data = [
            'cycle_id' => $cycleId,
            'asset_id' => $fieldValue->asset_id,
            'validator_field_id' => $validatorID,
            'description' => $error,
            'is_custom' => $isCustom,
            'type' => IssueEnum::Metadata->value
        ];

        // assign corresponding foreign key id
        if ($fieldValue instanceof FieldValue) {
            $data['field_value_id'] = $fieldValue->id;
        } else if ($fieldValue instanceof AssetVideo) {
            $data['asset_video_id'] = $fieldValue->id;
        } else if ($fieldValue instanceof AssetAudio) {
            $data['asset_audio_id'] = $fieldValue->id;
        }

        Issue::create($data);
    }

    public function reportImageIssue(AssetImage $image, int $validatorID, string $error, bool $isCustom = false)
    {
        $data = [
            'asset_image_id' => $image->id,
            'asset_image_id' => $image->id,
            'asset_id' => $image->asset_id,
            'validator_field_id' => $validatorID,
            'description' => $error,
            'is_custom' => $isCustom,
            'type' => IssueEnum::Images->value
        ];

        Issue::create($data);
    }

    public function reportAssetIssue(int $assetID, string $error, int $cycleID)
    {
        $data = [
            'cycle_id' => $cycleID,
            'asset_id' => $assetID,
            'description' => $error,
            'type' => IssueEnum::Content->value
        ];

        Issue::create($data);
    }

    public function reportValidatorIssue(string $error, ?int $assetID, int $cycleID, string $validatorGroup = '', $categoryID = null)
    {
        $data = [
            'description' => $error,
            'type' => IssueEnum::Metadata->value,
            'is_custom' => false,
            'asset_id' => $assetID,
            'cycle_id' => $cycleID,
            'validator_group' => $validatorGroup,
            'category_id' => $categoryID
        ];
        Issue::create($data);
    }

    // fix issue by asset,field value,validator
    public function fix(FieldValue|AssetVideo|AssetAudio $fieldValue, int $validatorID)
    {
        Issue::where('asset_id', $fieldValue->asset_id)
            ->where('field_value_id', $fieldValue->id)
            ->where('validator_field_id', $validatorID)
            ->delete();
    }

    public function fixImageIssue(AssetImage $image, int $validatorID)
    {
        Issue::where('asset_id', $image->asset_id)
            ->where('asset_image_id', $image->id)
            ->where('validator_field_id', $validatorID)
            ->delete();
    }

    public function fixValidatorIssue($assetID, $cycleID, $validatorGroup = null, $categoryID = null)
    {
        Issue::when($validatorGroup, fn($q) => $q->where('validator_group', $validatorGroup))
            ->where('asset_id', $assetID)
            ->where('cycle_id', $cycleID)
            ->where('category_id', $categoryID)
            ->delete();
    }
}
