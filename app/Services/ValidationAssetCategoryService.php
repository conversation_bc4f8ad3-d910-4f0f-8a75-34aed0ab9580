<?php

namespace App\Services;

use App\Models\Asset;
use App\Models\Category;
use App\Rules\AtLeastOneCategoryRule;
use App\Rules\Category\MaxAssignedCategoriesRule;
use App\Rules\Category\CategoryContentTypeMatchRule;


/**
 * This service suppose to run validators for assets categories
 */
class ValidationAssetCategoryService
{
    protected ValidationIssueService $validationIssueService;

    // validation error mesage
    private ?string $error = null;

    /**
     * Create a new class instance.
     */
    public function __construct(ValidationIssueService $validationIssueService)
    {
        $this->validationIssueService = $validationIssueService;
    }

    /**
     * @param  Category $category active category, triggered validation
     * @param  ?array $ids assets ids for category type asset
     */
    public function runValidation(Category $category, array $ids = []): void
    {
        // check category context type 
        $rule = new CategoryContentTypeMatchRule($category);
        $rule->validate('Categories', $ids, fn($message) => $this->error = $message);
        $this->runValidationIssueService(null, $category->cycle_id, $rule->group, $category->id);

        if (empty($ids)) {
            return; // no need to validate related assets
        }

        foreach ($ids as $assetID) {
            $this->error = null;

            // AtLeastOneCategory Check - [Movie, TV Series]
            // At least one of the category fields (Categories, Discover, Kids, Lifestyle, Paramount+) requires a value
            $rule = new AtLeastOneCategoryRule($category->cycle_id);
            $rule->validate('Categories', $assetID, fn($message) => $this->error = $message);
            $this->runValidationIssueService($assetID, $category->cycle_id, $rule->group);

            $asset = Asset::where('id', $assetID)->with('categories')->firstOrFail();

            $rule = new MaxAssignedCategoriesRule($asset, $category->airline_id, $category->cycle_id);
            $rule->validate('Categories', $assetID, fn($message) => $this->error = $message);
            $this->runValidationIssueService($assetID, $category->cycle_id, $rule->group);
        }
    }

    public function runValidationIssueService($assetID, $cycleID, $group, $categoryID = null)
    {
        if ($this->error) {
            $this->validationIssueService->reportValidatorIssue($this->error, $assetID, $cycleID, $group, $categoryID);
        } else {
            $this->validationIssueService->fixValidatorIssue($assetID, $cycleID, $group, $categoryID);
        }
    }
}
