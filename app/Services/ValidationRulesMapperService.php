<?php

namespace App\Services;
 
use App\Models\ValidatorField; 

class ValidationRulesMapperService
{
    public function __construct(){}


    public function mapValidationFieldsToRules(int $field_id): array
    {
        $enabledRules = ValidatorField::with(['validator'])
            ->where('field_id', $field_id)
            ->where('enabled', true)
            ->get();

        return $enabledRules->toArray();
    }
}
