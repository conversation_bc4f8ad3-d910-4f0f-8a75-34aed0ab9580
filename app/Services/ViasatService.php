<?php

namespace App\Services;

use App\Console\Commands\GetSnapshot;
use App\Models\Snapshot;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\AssetAudio;
use App\Models\AssetImage;
use App\Models\AssetVideo;
use App\Models\Cycle;
use App\Models\Enums\AssetStatus;
use Illuminate\Support\Facades\Storage;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Asset;
use App\Models\Field;
use App\Models\FieldValue;
use App\Models\Language;
use App\Utilities\CycleAssetManager;
use Illuminate\Support\Arr;

/**
 * Service to make requests to the Viasat API.
 *
 * Viasat endpoints:
 * - Authentication `/v1/dev/cms/auth`. You request a token with this curl and use that in subsequent requests.
 * - Snapshots `/v1/dev/cms/snapshots`. POST to snapshots with the json payload designed between Cinesend and Viasat
 * - Files metadata - `/v1/dev/cms/files/metadata`. For receiving information about file metadata for a list of files.
 * - Files upload - `/v1/dev/cms/files/upload`. Self explanatory file upload endpoint
 * - Updates - `/v1/dev/cms/updates`. You can poll this endpoint for a response containing the state of snapshot creation and any errors with validation.
 */
class ViasatService
{
    private $signature;
    private $authenticateURL;
    private $mediaInfoURL;
    private ValidationIssueService $validationIssueService;
    private ValidationFieldService $validationFieldService;
    private CycleAssetManager $cycleAssetManager;

    // items in a data chunk for batch inserts
    private const BATCH_SIZE = 500;

    private array $previousResponseByMediaType = [];

    /**
     * Create a new class instance.
     */
    public function __construct(ValidationIssueService $validationIssueService, ValidationFieldService $validationFieldService, CycleAssetManager $cycleAssetManager)
    {
        $this->validationIssueService = $validationIssueService;
        $this->validationFieldService = $validationFieldService;
        $this->cycleAssetManager = $cycleAssetManager;
        $domain = config('viasat.url');
        $this->authenticateURL = $domain . '/auth';
        $this->mediaInfoURL = $domain . '/files/metadata/vstest';
        $this->snapshotUrl = $domain . '/snapshots/v2';
    }

    public function authenticate()
    {
        // If no signature set, then get one and store it.
        if (!isset($this->signature)) {
            $this->signature = $this->getSignature();
        }
    }

    public function getSignature()
    {
        $response = Http::asForm()->post($this->authenticateURL, [
            'expires' => Carbon::now('UTC')->addDay()->format('Y-m-d\TH:i:s') . ".000000Z",
            'username' => config('viasat.username'),
            'password' => config('viasat.password'),
        ]);

        if ($response->successful()) {
            return $response->json('signature');
        }

        Log::error('Failed to authenticate with Viasat API: ' . $response->body());
        abort(500, 'Failed to authenticate with Viasat API: ' . $response->body());
    }

    public function sendSnapshot(Snapshot $snapshot)
    {
        // get the file contents from snapshot
        $this->authenticate();

        $fileContents = json_decode(Storage::get(GetSnapshot::SNAPSHOT_ROOT . $snapshot->filename));
        $response = Http::withHeaders([
            'x-request-signature' => $this->signature,
        ])->asForm()->post($this->snapshotUrl . "?action=create&previewDate=" . now()->toIso8601String(), $fileContents);

        if ($response->successful()) {
            $snapshot->viasat_snapshot_id = $response->body();
            $snapshot->viasat_status = 'created';
            $snapshot->save();
        }
        else {
            $snapshot->viasat_status = 'error: ' . $response->body();
            $snapshot->is_failed = true;
            $snapshot->save();
        }
    }

    public function getMediaInfoFiles(string $mediaType)
    {
        if (array_key_exists($mediaType, $this->previousResponseByMediaType)) {
            return $this->previousResponseByMediaType[$mediaType];
        }

        $this->authenticate();

        $response = Http::withHeaders([
            'x-request-signature' => $this->signature,
        ])->asForm()->get("{$this->mediaInfoURL}?fileType={$mediaType}");

        if ($response->successful()) {
            $files = $response->json()['files'] ?? [];

            $responseData = array_map(function ($file) {
                return [
                    'file_name' => $file['uploadedFileName'] ?? '',
                    'file_type' => $file['fileType'] ?? '',
                    'viasat_id' => $file['viasatId'] ?? '',
                    'extension' =>  array_key_exists('uploadedFileName', $file) ? pathinfo($file['uploadedFileName'], PATHINFO_EXTENSION) : '',
                    'size' => $file['fileSize'] ?? null,
                    'file' => [
                        ...$file,
                        // add fake/dummy mappings to unblock api based field updates
                        // frontend can swap to real fields (spread above from `$file`) once they are populated by the api
                        'fake_supplier' => 'Fake Video Lab Inc.',
                        'fake_durationMs' => 2 * 60 * 1000, // 2 mins to ms
                        'fake_projectedFileSize' => 2 * 1024 * 1024, // 2GB (base is in kb?)
                        'fake_videos' => [
                            [
                                'displayAspectRatio' => '16:9',
                                'contentAspectRatio' => '16:9',
                            ],
                        ],
                        'fake_audio' => [
                            ['language' => 'eng'],
                            ['language' => 'ita'],
                            ['language' => 'deu'],
                            ['language' => 'fra'],
                        ],
                        'fake_textTracks' => [
                            ['language' => 'eng'],
                            ['language' => 'ita'],
                            ['language' => 'deu'],
                            ['language' => 'fra'],
                        ]
                    ]
                ];
            }, $files);

            $this->previousResponseByMediaType[$mediaType] = $responseData;

            return $responseData;
        }

        Log::error('Failed to get media info files: ' . $response->body());
        abort(500, 'Failed to get media info files: ' . $response->body());
    }

    public function getFileInfo($fileType, $fileName)
    {
        $files = $this->getMediaInfoFiles($fileType);
        $matchingFiles = array_values(array_filter($files, function ($file) use ($fileName) {
            return $file['file_name'] === $fileName;
        }));

        if (empty($matchingFiles)) {
            Log::error("File not found: {$fileName}");
            return;
        }

        // TODO: confirm what to do when multiple files match. Viasat indicated using latest created, however there is no date yet in the response.
        // if (count($matchingFiles) > 1) {
        //     Log::error("Multiple files found with the same name: {$fileName}");
        //     return;
        // }

        return $matchingFiles[0];
    }

    /**
     * Pull files data from the Viasat API. and autofill the cycle size.
     *
     * @param Cycle $cycle
     */
    public function autoFill(Cycle $cycle): void
    {
        $this->authenticate();

        $totalVideoSize = $this->getAssetFilesSize($cycle, 'video', AssetVideo::class);
        $totalAudioSize = $this->getAssetFilesSize($cycle, 'audio', AssetAudio::class);
        $totalImageSize = $this->getAssetFilesSize($cycle, 'image', AssetImage::class);

        $cycle->total_snapshot_size = $totalVideoSize + $totalAudioSize + $totalImageSize;

        $recalledVideoSize = $this->getAssetFilesSize($cycle, 'video', AssetVideo::class, true);
        $recalledAudioSize = $this->getAssetFilesSize($cycle, 'audio', AssetAudio::class, true);
        $recalledImageSize = $this->getAssetFilesSize($cycle, 'image', AssetImage::class, true);

        $cycle->recalled_snapshot_size = $recalledVideoSize + $recalledAudioSize + $recalledImageSize;
        $cycle->save();

        $this->updateAssetContent($cycle, 'video');
        $this->updateAssetContent($cycle, 'audio');
        $this->updateAssetContent($cycle, 'image');
    }

    private function updateAssetContent(Cycle $cycle, string $mediaType)
    {
        $assetTypesForMediaType = match ($mediaType) {
            'video' => [AssetTypeEnum::Movie, AssettypeEnum::Series, AssetTypeEnum::Episode],
            'audio' => [AssetTypeEnum::Music, AssetTypeEnum::Audiobook, AssetTypeEnum::Podcast, AssetTypeEnum::Track],
            'image' => [],
            default => null,
        };

        if (empty($assetTypesForMediaType)) {
            return;
        }

        $files = collect($this->getMediaInfoFiles($mediaType));

        $assets = $cycle->assets()
            ->whereIn('asset_type', $assetTypesForMediaType)
            ->get();

        // get "set from api" fields for current schema
        $apiMappedFields = Field::query()
            ->where('schema_id', $cycle->schema_id)
            ->where('is_external_api_value_source', true)
            ->get()
            ->toArray();

        /**
         * For all assets, match a "key field" to a file from the API,
         * then extract values for fields based on mappings stored in `is_external_api_value_source` marked fields.
         *
         * @var Asset $asset */
        foreach ($assets as $asset) {
            $keyField = $cycle->schema->fields()
                ->where('is_external_api_key_field', true)
                ->where(
                    fn ($query) =>
                        $query->where('asset_types', 'like', "%{$asset->asset_type}%")
                            // since empty asset_types means "all asset types"
                            ->orWhere('asset_types', '[]')
                            ->orwhere('asset_types', null)
                )
                ->get()
                ->first();

            if ($keyField === null) {
                continue;
            }

            $keyField->withValue($asset->id);
            $keyFieldValue = $keyField->value?->getValue();

            if ($keyFieldValue === null) {
                continue;
            }

            $file = $files->first(fn ($file) => $file['file_name'] === $keyFieldValue);

            if (empty($file)) {
                continue;
            }

            // get api fields for asset type, create update payloads based on field mappings
            // if `asset_types` is empty it means "all asset types"
            $fieldsForType = array_filter($apiMappedFields, fn ($field) => empty($field['asset_types']) || in_array($asset->asset_type, $field['asset_types'] ?? []));

            foreach ($fieldsForType as $field) {
                $input_key = $field['input_key'];
                if (empty($input_key)) {
                    continue;
                }

                $newValue = Arr::get($file, "file.{$input_key}");

                if ($field['use_languages']) {
                    // match on all language codes since its not clear which is being used
                    $newValue = Language::query()
                        ->where('iso_639_2_t_code', $newValue)
                        ->orWhere('bcp_47_code', $newValue)
                        ->orWhere('ife_code', $newValue)
                        ->first('id')
                        ?->id;
                }

                [$fieldValue, $error] = FieldValue::createOrUpdate($field['id'], $asset->id, null, $cycle->id, $newValue, $this->validationFieldService, $this->cycleAssetManager);
            }
        }
    }

    /**
     * Get sum of all media files in cycle by type. Size is read from the Viasat API and stored in the database for existing file records.
     *
     * @param Cycle $cycle
     * @param string $fileType
     * @param string $modelClass
     * @return int
     */
    private function getAssetFilesSize(Cycle $cycle, string $fileType, string $modelClass, ?bool $recalled = null): int
    {
        $apiFiles = collect($this->getMediaInfoFiles($fileType));
        $assetFiles = $modelClass::whereHas('asset.cycles', function ($q) use ($cycle, $recalled) {
            $q->where('cycles.id', $cycle->id);
            if ($recalled) {
                $q->where('status', AssetStatus::Recalled->value);
            }
        })
        ->where('airline_id', $cycle->airline_id)
        ->whereNot('asset_id', null)
        ->get();
        $dataToUpsert = [];
        $totalSize = 0;
        foreach ($assetFiles as $assetFile) {
            $file = $apiFiles->first(fn ($file) => $file['file_name'] === $assetFile->file_name);
            if ($file) {
                $dataToUpsert[] = [
                    'uuid' => $assetFile->uuid,
                    'cycle_id' => $cycle->id,
                    'asset_id' => $assetFile->asset_id,
                    'airline_id' => $cycle->airline_id,
                    'field_id' => $assetFile->field_id,
                    'file_name' => $file['file_name'],
                    'size' => $file['size']
                ];
                $totalSize +=  $file['size'];
            } else if ($assetFile->file_name) {
                // report an issue if file data not found in Viasat API
                $this->validationIssueService->reportAssetIssue($assetFile->asset_id, "Viasat API does not have file: {$assetFile->file_name}", $cycle->id);
            }
        }
        if (!empty($dataToUpsert)) {
            $chunks = collect($dataToUpsert)->chunk(self::BATCH_SIZE);
            foreach ($chunks as $chunk) {
                $modelClass::upsert($chunk->toArray(), ['uuid'], ['size', 'extension']);
            }
        }
        return $totalSize;
    }
}
