<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Jobs\ValidateCycleSize;
use App\Models\AssetAudio;
use App\Models\AssetImage;
use App\Models\AssetVideo;
use App\Models\Cycle;
use PDO;

/**
 * Service to make requests to the Viasat API.
 * 
 * Viasat endpoints:
 * - Authentication `/v1/dev/cms/auth`. You request a token with this curl and use that in subsequent requests.
 * - Snapshots `/v1/dev/cms/snapshots`. POST to snapshots with the json payload designed between Cinesend and Viasat
 * - Files metadata - `/v1/dev/cms/files/metadata`. For receiving information about file metadata for a list of files.
 * - Files upload - `/v1/dev/cms/files/upload`. Self explanatory file upload endpoint
 * - Updates - `/v1/dev/cms/updates`. You can poll this endpoint for a response containing the state of snapshot creation and any errors with validation.
 */
class ViasatService
{
    private $signature;
    private $authenticateURL;
    private $mediaInfoURL;

    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $domain = config('viasat.url');
        $this->authenticateURL = $domain . '/auth';
        $this->mediaInfoURL = $domain . '/files/metadata/vstest';
    }

    public function authenticate()
    {
        // If no signature set, then get one and store it.
        if (!isset($this->signature)) {
            $this->signature = $this->getSignature();
        }
    }

    public function getSignature()
    {
        $response = Http::asForm()->post($this->authenticateURL, [
            'expires' => Carbon::now('UTC')->addDay()->format('Y-m-d\TH:i:s') . ".000000Z",
            'username' => config('viasat.username'),
            'password' => config('viasat.password'),
        ]);

        if ($response->successful()) {
            return $response->json('signature');
        }

        Log::error('Failed to authenticate with Viasat API: ' . $response->body());
        abort(500, 'Failed to authenticate with Viasat API: ' . $response->body());
    }

    public function getMediaInfoFiles(string $mediaType)
    {
        $this->authenticate();

        $response = Http::withHeaders([
            'x-request-signature' => $this->signature,
        ])->asForm()->get("{$this->mediaInfoURL}?fileType={$mediaType}");

        if ($response->successful()) {
            $files = $response->json()['files'] ?? [];
            return array_map(function ($file) {
                return [
                    'file_name' => $file['uploadedFileName'] ?? '',
                    'file_type' => $file['fileType'] ?? '',
                    'content_id' => $file['contentId'] ?? '',
                    'extension' => $file['extension'] ?? '',
                ];
            }, $files);
        }

        Log::error('Failed to get media info files: ' . $response->body());
        abort(500, 'Failed to get media info files: ' . $response->body());
    }

    public function getFileInfo($fileType, $fileName)
    {
        $files = $this->getMediaInfoFiles($fileType);
        $matchingFiles = array_values(array_filter($files, function ($file) use ($fileName) {
            return $file['file_name'] === $fileName;
        }));

        if (empty($matchingFiles)) {
            Log::error("File not found: {$fileName}");
            return;
        }

        // TODO: confirm what to do when multiple files match. Viasat indicated using latest created, however there is no date yet in the response.
        // if (count($matchingFiles) > 1) {
        //     Log::error("Multiple files found with the same name: {$fileName}");
        //     return;
        // }

        return $matchingFiles[0];
    }

    /**
     * Pull files data from the Viasat API. and autofill the cycle size.
     * 
     * @param Cycle $cycle
     */
    public function autoFill(Cycle $cycle): void
    {
        $this->authenticate();

        $cycle_video_size = $this->getAssetFilesSize($cycle, 'video', AssetVideo::class);
        $cycle_audio_size = $this->getAssetFilesSize($cycle, 'audio', AssetAudio::class);
        $cycle_image_size = $this->getAssetFilesSize($cycle, 'image', AssetImage::class);

        $cycle->total_snapshot_size = $cycle_video_size + $cycle_audio_size + $cycle_image_size;
        $cycle->recalled_snapshot_size = 80; // TODO: fix once api is ready
        $cycle->save();
    }


    /**
     * Get sum of all media files in cycle by type. Size is read from the Viasat API and stored in the database for existing file records.
     * 
     * @param Cycle $cycle
     * @param string $fileType
     * @param string $modelClass
     * @return int  
     */
    private function getAssetFilesSize(Cycle $cycle, string $fileType, string $modelClass): int
    {
        $apiFiles = collect($this->getMediaInfoFiles($fileType));
        $assetFiles = $modelClass::where('airline_id', $cycle->airline_id)->where('cycle_id', $cycle->id)->get();
        $dataToUpsert = [];
        $totalSize = 0;
        foreach ($assetFiles as $assetFile) {
            $file = $apiFiles->first(fn($file) => $file['file_name'] === $assetFile->file_name);
            if ($file) {
                $size = $file['size'] ?? rand(1, 1024 * 1024 * 1024); // TODO: fix once api is ready
                $dataToUpsert[] = [
                    'uuid' => $assetFile->uuid,
                    'cycle_id' => $cycle->id,
                    'asset_id' => $assetFile->asset_id,
                    'airline_id' => $cycle->airline_id,
                    'field_id' => $assetFile->field_id,
                    'file_name' => $file['file_name'],
                    'extension' => $file['extension'],
                    'size' => $size
                ];
                $totalSize += $size;
            } else if ($assetFile->file_name) {
                // TODO: trigger issue
            }
        }
        if (!empty($dataToUpsert)) {
            $modelClass::upsert($dataToUpsert, ['uuid'], ['size', 'extension']);
        }
        return $totalSize;
    }
}
