<?php

namespace App\Services;

use App\Models\Asset;
use App\Models\Cycle;
use App\Models\Enums\AssetTypeEnum;
use App\Rules\Asset\DateOrderRule;

//use App\Services\ValidationFieldService;

class ValidationAssetService
{
    protected ValidationFieldService $validationFieldService;

    protected ValidationIssueService $validationIssueService;

    // validation error mesage
    private ?string $error = null;

    /**
     * Create a new class instance.
     */
    public function __construct(
        ValidationFieldService $validationFieldService,
        ValidationIssueService $validationIssueService
    ) {
        $this->validationFieldService = $validationFieldService;
        $this->validationIssueService = $validationIssueService;
    }

    /**
     * Validate asset fields.
     *
     * @param Asset $asset
     * @return bool validation success state
     */
    public function validateAssetFields(Asset $asset, Cycle $cycle): void
    {
        $this->runAssetValidation($asset, $cycle);

        // get field values list from the asset
        $fieldValues = $asset->fieldValues()->get();

        // Validate Asset field values
        foreach ($fieldValues as $fieldValue) {
            $this->validationFieldService->runValidation($fieldValue, $cycle->id);
            // TODO: block asset if severity is over threshold
            //
        }
    }

    private function runAssetValidation(Asset $asset, Cycle $cycle)
    {
        // Validate Asset data
        if (in_array($asset->asset_type, [AssetTypeEnum::Series->value, AssetTypeEnum::Movie->value])) {
            // DateOrder
            $rule = new DateOrderRule($asset);
            $rule->validate('End Date', $asset->end_date, fn($error) => $this->error = $error);

            if ($this->error) {
                //reportValidatorIssue(string $error, int $assetID, int $cycleID, string $validatorGroup = '')
                $this->validationIssueService->reportValidatorIssue($this->error, $asset->id, $cycle->id, $rule->group);
            } else {
                $this->validationIssueService->fixValidatorIssue($asset->id, $cycle->id, $rule->group);
            }
        }
    }
}
