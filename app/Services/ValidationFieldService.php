<?php

namespace App\Services;

use App\Models\AssetAudio;
use App\Models\FieldValue;
use App\Models\ValidatorField;
use App\Models\AssetImage;
use App\Models\AssetVideo;
use App\Services\ValidationRulesMapperService;
use App\Models\Enums\ValidatorRulesEnum;
use Illuminate\Support\Facades\Log;

class ValidationFieldService
{
    protected ValidationRulesMapperService $validationRulesMapperService;
    protected ValidationIssueService $validationIssueService;

    // validation error mesage
    private ?string $error = null;

    public function __construct(
        ValidationRulesMapperService $validationRulesMapperService,
        ValidationIssueService $validationIssueService
    ) {
        $this->validationRulesMapperService = $validationRulesMapperService;
        $this->validationIssueService = $validationIssueService;
    }


    // Should returm message for manual field change and record an issue, if any, when processing an asset
    public function runValidation(FieldValue|AssetVideo|AssetAudio $fieldValue, $cycleId, bool $returnMessage = false)
    {
        $errors = [];
        // get field validators
        $fieldValidators = $this->getFieldValidators($fieldValue->field_id);

        // run validators on the field value 
        foreach ($fieldValidators as $fieldValidator) {
            $this->error = null;
            // check if validation rule exists
            $validatorEnum = ValidatorRulesEnum::contains($fieldValidator->validator->name);
            if (!isset($validatorEnum)) {
                Log::error("Invalid Validator Name: {$fieldValidator->validator->name} in field value: {$fieldValue->id}");
                continue;
            }

            // add asset info 
            $parameters = json_decode($fieldValidator->parameters, true) ?? [];
            $parameters = array_merge($parameters, [
                'cycleID' => $cycleId,
                'assetType' => $fieldValue->asset->asset_type,
                'fieldID' => $fieldValue->field_id,
                'fieldValueID' => $fieldValue->id
            ]);

            // validate
            $ruleInstance = $this->createRuleInstance($validatorEnum->value, $parameters);
            $ruleInstance->validate($fieldValue->field->name, $fieldValue->value, fn($message) => $this->error = $message);

            if (is_null($this->error)) {
                $this->validationIssueService->fix($fieldValue, $fieldValidator->id);
                continue;
            }

            // report issue
            $this->validationIssueService->report($fieldValue, $fieldValidator->id, $this->error, $cycleId);
            $errors[] = $this->error;
        }
        // After processing all validators, return first error message
        return $returnMessage ? ($errors[0] ?? null) : null;
    }


    public function runValidationImage(AssetImage $assetImage, bool $returnMessage = false)
    {
        $errors = [];
        // get field validators
        $fieldValidators = $this->getFieldValidators($assetImage->field_id);
        // run validators on the assetImage value 
        foreach ($fieldValidators as $fieldValidator) {
            $this->error = null;
            // check if validation rule exists
            $validatorEnum = ValidatorRulesEnum::contains($fieldValidator->validator->name);
            if (!isset($validatorEnum)) {
                Log::error("Invalid Validator Name: {$fieldValidator->validator->name} in image field: {$assetImage->field_id}");
                continue;
            }

            // validate
            $ruleInstance = $this->createRuleInstance($validatorEnum->value, $fieldValidator->parameters);
            $ruleInstance->validate($assetImage->field->name, $assetImage, fn($message) => $this->error = $message);

            if (is_null($this->error)) {
                $this->validationIssueService->fixImageIssue($assetImage, $fieldValidator->id);
                continue;
            }

            // report image issue
            $this->validationIssueService->reportImageIssue($assetImage, $fieldValidator->id, $this->error);
            $errors[] = $this->error;
        }
        // After processing all validators, return first error message
        return $returnMessage ? ($errors[0] ?? null) : null;
    }


    private function createRuleInstance(string $ruleClass, ?array $parameters)
    {
        if (!is_null($parameters)) {
            // pass params as an object
            return new $ruleClass((object)$parameters);
        }

        return new $ruleClass();
    }


    private function getFieldValidators(int $fieldID)
    {
        return ValidatorField::where('field_id', $fieldID)
            ->where('enabled', true)
            ->get();
    }
}
