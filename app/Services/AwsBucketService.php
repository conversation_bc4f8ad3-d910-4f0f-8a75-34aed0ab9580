<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Aws\Exception\AwsException;

class AwsBucketService
{
    private $disk;
    private $bucket;
    private $expiryMinutes = '+15 minutes'; 

    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $this->disk = Storage::disk('s3');
        $this->bucket = env('AWS_BUCKET'); 
    }

    /**
     * $s3Key: The key for the object in S3.
     * $mimeType: The MIME type of the object.
     */
    public function generateSignedUriPut($s3Key, $mimeType)
    {
        $error = null;
        $signedUrl = null;
 
        try {  
            $client = $this->disk->getClient();

            $command = $client->getCommand('PutObject', [
                'Bucket' => $this->bucket,
                'Key' => $s3Key,
                'ContentType' => $mimeType
            ]);
            
            $presignedRequest = $client->createPresignedRequest($command, $this->expiryMinutes);
 
            $signedUrl = (string) $presignedRequest->getUri(); 
        }
        catch(AwsException $e) {
            $error = "Generating a signed URL for PutObject in AWS failed: " . $e->getMessage();
            Log::error($error);
        }
        catch(\Exception $e) {
            $error = "Generating a signed URL for PutObject failed: " . $e->getMessage();
            Log::error($error);
        }

        return [
            'success' => is_null($error),
            'url' => $signedUrl,
            'error' => $error,
        ];
    }


    public function generateSignedUriGet($path) 
    {  
        $signedUrl = '';
        $error = null;
        
        try { 
            $client = $this->disk->getClient();

            $command = $client->getCommand('GetObject', [
                'Bucket' => $this->bucket,
                'Key' => $path, 
            ]);
            
            $presignedRequest = $client->createPresignedRequest($command, $this->expiryMinutes);
            $signedUrl = (string) $presignedRequest->getUri(); 
        }
        catch(AwsException $e) {
            $error = "Generating a signed URL for GetObject in AWS failed: " . $e->getMessage();
            Log::error($error);
        }
        catch(\Exception $e) {
            $error = "Generating a signed URL for GetObject failed: " . $e->getMessage();
            Log::error($error);
        }

        return [
            'success' => is_null($error),
            'url' => $signedUrl, 
            'error' => $error,
        ];
    }

    public function deleteObject(String $path)
    {
        try { 
            $client = $this->disk->getClient(); 
            
            $command = $client->getCommand('DeleteObject', [
                'Bucket' => $this->bucket,
                'Key' => $path,  
            ]);
            
            return true;
        }
        catch(AwsException $e) {
            Log::error("S3 Pre-signed URL generation failed: " . $e->getMessage()); 
        }
        catch(\Exception $e) {
            Log::error("Error generating Pre-signed URL: " . $e->getMessage()); 
        } 

        return false;
    } 
}
