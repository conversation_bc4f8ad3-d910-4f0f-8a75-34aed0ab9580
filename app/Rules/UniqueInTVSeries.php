<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Asset;


class UniqueInTVSeries implements ValidationRule
{
    protected int $fieldID;

    protected int $fieldValueID;

    protected int $cycleID;

    protected string $assetType;

    public function __construct(mixed $params)
    {
        $this->cycleID = $params->cycleID;
        $this->assetType = $params->assetType;
        $this->fieldID = $params->fieldID;
        $this->fieldValueID = $params->fieldValueID;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // if value is empty let other validators, like required, handle it
        if (empty($value)) {
            return;
        }

        // TV Episode must be unique within TV Series.
        if ($this->assetType == AssetTypeEnum::Episode->value && $this->existsInTVSeries($value)) {
            $fail("The {$attribute} must be unique within TV Series.");
        }
    }

    /**
     * It is not unique if another record exists with the same value in the same cycle within the same TV Series
     */
    private function existsInTVSeries(string $value): bool
    {
        $currentEpisode = Asset::whereHas('fieldValues', fn($q) => $q->where('id', $this->fieldValueID))
            ->where('asset_type', AssetTypeEnum::Episode->value)
            ->first();

        if (! $currentEpisode->parent_asset_id) {
            return false; // episode does not belong to a TV Series, no need to check
        }

        $exists = Asset::whereHas('cycles', fn($q) => $q->where('cycles.id', $this->cycleID))
            ->whereHas(
                'fieldValues',
                fn($q) => $q->where('field_id', $this->fieldID)
                    ->where('value', $value)
                    ->whereNot('id', $this->fieldValueID)
            )
            ->where('parent_asset_id', $currentEpisode->parent_asset_id)
            ->where('asset_type', $this->assetType)
            ->exists();

        return $exists;
    }
}
