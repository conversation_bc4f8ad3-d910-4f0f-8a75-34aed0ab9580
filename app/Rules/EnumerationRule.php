<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class EnumerationRule implements ValidationRule
{
    protected array $enum;

    public function __construct(mixed $params)
    {
        $this->enum = $params->enum;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!in_array($value, $this->enum)) {
            $fail("The {$attribute} is not a valid enumeration value.");
        }
    }
}
