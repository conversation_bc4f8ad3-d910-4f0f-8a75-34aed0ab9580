<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Imagick;

class ImageSpecRule implements ValidationRule
{
    protected int $width;
    protected int $height;

    public function __construct(mixed $params)
    {
        $this->width = $params->width;
        $this->height = $params->height;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $this->log("Starting validation for file: $value");

        try {
            $imagick = new Imagick($value);
            $this->log("Successfully loaded image: $value");
        } catch (\Exception $e) {
            $this->log("Failed to load image: $value. Error: " . $e->getMessage());
            $fail("The image is not a valid file.");
            return;
        }

        $version = \Imagick::getVersion();
        $this->log("Imagick version: " . ($version['versionString'] ?? json_encode($version)));

        $actualWidth = $imagick->getImageWidth();
        $actualHeight = $imagick->getImageHeight();
        $this->log("Image dimensions: {$actualWidth}x{$actualHeight}, expected: {$this->width}x{$this->height}");

        if ($actualWidth !== $this->width || $actualHeight !== $this->height) {
            $this->log("Invalid dimensions. Got {$actualWidth}x{$actualHeight}, expected {$this->width}x{$this->height}.");
            $fail("The image dimensions must be {$this->width}x{$this->height}.");
        }

        $extension = strtolower(pathinfo($value, PATHINFO_EXTENSION));
        $this->log("File extension: .$extension");
        if ($extension !== 'jpg') {
            $this->log("Invalid file extension: .$extension. Expected .jpg");
            $fail("The image must be a JPG file.");
        }

        $format = $imagick->getImageFormat();
        $this->log("Image format: $format");

        $embeddedProfiles = $imagick->getImageProfiles("icc", true);
        $this->log("Embedded ICC profiles: " . implode(', ', array_keys($embeddedProfiles)));
        if (!empty($embeddedProfiles)) {
            $this->log("Found embedded ICC profile(s): " . implode(', ', array_keys($embeddedProfiles)));
            $fail("The image must not have an embedded color profile.");
        }

        $colorSpace = $imagick->getImageColorspace();
        $this->log("Image colorspace (int): $colorSpace");
        $this->log("Imagick::COLORSPACE_SRGB: " . (defined('Imagick::COLORSPACE_SRGB') ? Imagick::COLORSPACE_SRGB : 'undefined'));
        $this->log("Imagick::COLORSPACE_RGB: " . (defined('Imagick::COLORSPACE_RGB') ? Imagick::COLORSPACE_RGB : 'undefined'));
        $this->log("Imagick::COLORSPACE_TRANSPARENT: " . (defined('Imagick::COLORSPACE_TRANSPARENT') ? Imagick::COLORSPACE_TRANSPARENT : 'undefined'));

        $validSRGB = [
            Imagick::COLORSPACE_SRGB,
            Imagick::COLORSPACE_RGB,
        ];

        $this->log("Acceptable colorspace values: " . implode('/', $validSRGB));
        if (!in_array($colorSpace, $validSRGB, true)) {
            $this->log("FAILED - Expected one of " . implode('/', $validSRGB) . ", got: {$colorSpace}");
            $fail("The image must be in sRGB or RGB color space.");
        }

        $quality = $imagick->getImageCompressionQuality();
        $this->log("Compression quality: $quality");

        $isMozJPEG = ($quality === 0);
        $this->log("isMozJPEGEncoded: " . ($isMozJPEG ? 'true' : 'false'));

        $threshold = $isMozJPEG ? 0 : 90;
        $this->log("Quality threshold: $threshold");

        /*
         * We can't check MozJPEG compression. Part of its algorithm rips the
         * attribute out of the file. We can only assume that if quality is 0, it's
         * using mozjpeg (cjpeg) to be compressed optimally.
         */

        if ($quality < $threshold) {
            $this->log("Low compression quality. Got $quality, expected at least $threshold.");
            $fail("The image quality must be at least $threshold" . ($isMozJPEG ? "% for MozJPEG." : "."));
        }

        $imagick->clear();

        $this->log("Validation complete for file: $value");
    }

    private function log(string $message)
    {
        logger()->debug("[ImageSpecRule] $message");
    }
}
