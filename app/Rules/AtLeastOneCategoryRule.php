<?php

namespace App\Rules;

use App\Models\CategoryItem;
use App\Models\Enums\ValidatorGroupEnum;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class AtLeastOneCategoryRule implements ValidationRule
{
    protected int $cycleID;

    public $group = ValidatorGroupEnum::Category->value;

    public function __construct($cycleID)
    {
        //        $this->asset = $asset;
        $this->cycleID = $cycleID;
    }

    /**
     * Run the validation rule.
     *
     * @param  string $attribute Attribute name
     * @param  mixed  $value Asset ID
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $categories = CategoryItem::where('asset_id', $value)
            ->whereHas('category', fn($q) => $q->where('cycle_id', $this->cycleID))
            ->count();

        // get parent categories for asset type
        if ($categories === 0) {
            $fail("At least one of the {$attribute} fields requires a value");
        }
    }
}
