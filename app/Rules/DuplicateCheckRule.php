<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class DuplicateCheckRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $duplicates = array_count_values($value);
        $duplicates = array_filter($duplicates, function ($count) {
            return $count > 1;
        });
        if (!empty($duplicates)) {
            $fail("The {$attribute} contains duplicate values");
        }
    }
}
