<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Carbon\Carbon;
use Exception;

class MaxYearRule implements ValidationRule
{
    protected int $years;
    

    public function __construct(mixed $params)
    {
        $this->years = $params->years;
    }
    
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        try {
            $inputDate = Carbon::parse($value);
            $maxAllowedDate = Carbon::now()->addYears($this->years);
            if ($inputDate->gt($maxAllowedDate)) {
                $fail("The {$attribute} cannot be more than {$this->years} year(s) in the future.");
            }
        } 
        catch (Exception $e) {
            $fail("The {$attribute} must be a valid date.");
        }
    }
}
