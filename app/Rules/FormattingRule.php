<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FormattingRule implements ValidationRule
{    
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strpos($value, '  ') !== false) {
            $fail("The {$attribute} field contains double spaces.");
            return;
        }
    
        // Check for trailing/leading spaces
        if (trim($value) !== $value) {
            $fail("The {$attribute} field contains leading or trailing spaces.");
            return;
        }

    
        // Check first character capitalization
        if (!ctype_upper($value[0])) {
            $fail("The {$attribute} field must start with a capital letter.");
            return;
        }
    
        // Check for proper sentence endings
        $sentences = preg_split('/(?<=[.!?])\s+/', $value, -1, PREG_SPLIT_NO_EMPTY);
        foreach ($sentences as $sentence) {
            // Each sentence should end with proper punctuation
            if (!preg_match('/[.!?]$/', $sentence)) {
                $fail("All sentences in the {$attribute} field must end with proper punctuation.");
                return;
            }
    
            // Each sentence should start with capital letter
            if (!empty($sentence) && !ctype_upper($sentence[0])) {
                $fail("All sentences in the {$attribute} field must start with a capital letter.");
                return;
            }
        }
    }
}
