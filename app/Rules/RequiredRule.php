<?php

namespace App\Rules;

use App\Models\AssetImage;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class RequiredRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if(!$value || empty(trim($value)) || $value === "[]" || ($value instanceof AssetImage && $value->path === null)){
            $fail("The {$attribute} field is required.");
        }
    }
}
