<?php

namespace App\Rules\Asset;

use Closure;
use DateTime;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Enums\ValidatorGroupEnum;

class DateOrderRule implements ValidationRule
{
    private mixed $asset;

    public $group = ValidatorGroupEnum::DateOrder->value;

    public function __construct(mixed $asset)
    {
        $this->asset = $asset;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $date = new DateTime($value);
        $startDate = new DateTime($this->asset->start_date);

        if ($date < $startDate) {
            $fail("The {$attribute} must be greater than or equal to " . $this->asset->start_date);
        }
    }
}
