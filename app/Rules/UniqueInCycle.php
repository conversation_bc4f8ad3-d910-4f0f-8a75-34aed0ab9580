<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Asset;

class UniqueInCycle implements ValidationRule
{
    protected int $fieldID;

    protected int $fieldValueID;

    protected int $cycleID;

    protected string $assetType;

    public function __construct(mixed $params)
    {
        $this->cycleID = $params->cycleID;
        $this->assetType = $params->assetType;
        $this->fieldID = $params->fieldID;
        $this->fieldValueID = $params->fieldValueID;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // if value is empty let other validators, like required, handle it
        if (empty($value)) {
            return;
        }

        // Movies Cast, Synopsis, Title must be unique within the cycle.
        // TV Series Title, Synopsis must be unique within the cycle.
        if (in_array($this->assetType, [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value]) && $this->existsInCycle($value)) {
            $fail("The {$attribute} must be unique within the cycle.");
        }
    }


    /**
     * It is not unique if another record exists with the same value in the same cycle
     */
    private function existsInCycle(string $value): bool
    {
        $exists = Asset::whereHas('cycles', fn($q) => $q->where('cycles.id', $this->cycleID))
            ->whereHas(
                'fieldValues',
                fn($q) => $q
                    ->where('field_id', $this->fieldID)
                    ->where('value', $value)
                    ->whereNot('id', $this->fieldValueID)  // do not compare to itself
            )
            ->where('asset_type', $this->assetType)
            ->exists();

        return $exists;
    }
}
