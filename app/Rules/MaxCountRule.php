<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Log;

class MaxCountRule implements ValidationRule
{
    protected int $max;

    public function __construct(mixed $params)
    {
        $this->max = $params->value;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (is_string($value)) { //e.g. "[1,3,5,9]"
            $value = json_decode($value);
        }
        if (is_countable($value) && count($value) > $this->max) {
            $fail("The $attribute must not have more than {$this->max} items.");
        }
    }
}
