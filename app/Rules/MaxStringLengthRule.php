<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Log;

class MaxStringLengthRule implements ValidationRule
{
    protected int $max;

    public function __construct(mixed $params)
    {
        $this->max = $params->value;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (is_string($value) && strlen($value) > $this->max) {
            $fail("The {$attribute} field must be less than {$this->max} characters long.");
        }
    }
}
