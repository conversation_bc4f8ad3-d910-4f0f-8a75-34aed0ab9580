<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NoTags implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Remove HTML and PHP tags
        $cleared_value = preg_replace('/<[^>]*>|<\?php|\?>/', '', $value);
        
        if($cleared_value !== $value){
            $fail('Tags are not allowed in :attribute.');
        }
    }
}
