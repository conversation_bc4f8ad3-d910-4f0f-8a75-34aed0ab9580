<?php

namespace App\Rules;

use App\Models\CollectionValue;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CaseStyleRule implements ValidationRule
{
    protected string $case; // title or sentence

    protected int $fieldID;

    // params are expected as a json object
    public function __construct(mixed $params)
    {
        $this->case = $params->value;
        $this->fieldID = $params->fieldID;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty(trim($value))) {
            return;
        }

        // if field value is collection
        if (gettype(json_decode($value)) === 'array') {
            $items = json_decode($value);

            $collectionValues = CollectionValue::select('label')->whereIn('id', $items)->get();
            foreach ($collectionValues as $item) {
                if (!$this->isTitleCase($item->label)) {
                    $fail("The {$attribute} field must be in title case.");
                }
            }
        }

        if ($this->case === 'title' && !$this->isTitleCase($value)) {
            $fail("The {$attribute} field must be in title case.");
        }

        if ($this->case === 'sentence' && !$this->isSentenceCase($value)) {
            $fail("The {$attribute} field must be in sentence case.");
        }
    }

    // Title case - capitalizes the first letter of every important word
    private function isTitleCase($value)
    {
        $words = explode(' ', $value);
        $result = [];

        foreach ($words as $word) {
            if (empty($word)) { // possible when multiple spaces in a row
                $result[] = '';

                continue;
            }

            $result[] = mb_ucfirst(mb_strtolower($word));
        }

        return $value == implode(' ', $result);
    }

    // Sentence case - only capitalizes the first word of a sentence or heading
    private function isSentenceCase($value)
    {
        $sentences = explode('. ', $value);
        $result = [];

        foreach ($sentences as $sentence) {
            $result[] = mb_ucfirst(mb_strtolower($sentence));
        }

        return $value == implode('. ', $result);
    }
}
