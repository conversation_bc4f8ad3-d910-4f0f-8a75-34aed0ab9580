<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Jobs\FetchExternalApiData;

class MediaFileExistsRule implements ValidationRule
{
   protected mixed $cycle;

    public function __construct(mixed $params)
    {
        $this->cycle = $params->cycle;
    }
    
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
       // This rule should check if the media file name came from viasat api
       //    $test = FetchExternalApiData::dispatchSync($this->cycle);

       // TODO: Validate against the actual API response
        

    }
}
