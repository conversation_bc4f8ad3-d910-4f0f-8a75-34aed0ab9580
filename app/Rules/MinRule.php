<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class MinRule implements ValidationRule
{
    protected int $min;
    
    // params are expecgted as a json object
    public function __construct(mixed $params)
    {
        $this->min = $params->value;
    }
    
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if(is_string($value) && strlen($value) < $this->min) {
            $fail("The {$attribute} field must be at least {$this->min} characters long.");
        }
        else if(is_numeric($value) && $value < $this->min) {
            $fail("The {$attribute} field must be greater or equal to {$this->min}");
        }
        else if(!is_string($value) && !is_numeric($value)) { // int or string only
            $fail("The {$attribute} field format is invalid."); 
        }
    }
}
