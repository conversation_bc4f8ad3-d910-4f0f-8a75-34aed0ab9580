<?php

namespace App\Rules\Category;

use App\Models\{Asset, Category};
use App\Models\Enums\ValidatorGroupEnum;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CategoryContentTypeMatchRule implements ValidationRule
{
    private Category $category;

    public $group = ValidatorGroupEnum::Category->value;

    public function __construct(Category $category)
    {
        $this->category = $category;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->category->is_root) {
            $this->childCategoriesContentTypesMatch($this->category, $fail);
        } else {
            $this->assetsHaveAllowedTypes($this->category, $fail);
        }
    }

    // Checks if allowed asset types in child categories are present in the parent category
    private function childCategoriesContentTypesMatch(Category $category, Closure $fail): void
    {
        $category->childCategories->each(function ($childCategory) use ($fail) {
            $diff = array_diff($childCategory->asset_types,  $this->category->asset_types);
            if ($diff) {
                $fail("Category {$this->category->categoryValues->first()->value} has unsupported asset types assigned to child categories");
            }
            $this->assetsHaveAllowedTypes($childCategory, $fail);
        });
    }

    // Check if cateory items (assets) have supported asset types
    private function assetsHaveAllowedTypes(Category $category, Closure $fail): void
    {
        $totalAssetsWithUnallowedType = Asset::whereHas(
            'categories',
            fn($q) => $q->where('category_id', $category->id)
                ->whereNotIn('asset_type', $category->asset_types)
        )->count();

        if ($totalAssetsWithUnallowedType > 0) {
            $fail("Category {$category->categoryValues->first()->value} have assets with unallowed asset types");
        }
    }
}
