<?php

namespace App\Rules\Category;

use App\Models\Enums\ValidatorGroupEnum;
use App\Models\Enums\ValidatorRulesEnum;
use App\Models\ValidatorAirlineParam;
use App\Models\Cycle;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * - If category does not have dates specified we consider it takes all cycle.
 */
class MaxAssignedCategoriesRule implements ValidationRule
{
    private const DEFAULT_MAX_ASSIGNED_CATEGORIES = 2;

    private mixed $asset;

    private int $airlineID;

    private int $cycleID;

    public $group = ValidatorGroupEnum::Category->value;

    public function __construct(mixed $asset, int $airlineID, int $cycleID)
    {
        $this->asset = $asset;
        $this->airlineID = $airlineID;
        $this->cycleID = $cycleID;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // no need to check if only 1 category assigned
        if ($this->asset->categories->count() <= 1) {
            return;
        }

        $cycle = Cycle::where('id', $this->cycleID)->firstOrFail();

        // Check overlapping by category dates 
        // Sweep-Line Algorithm 
        $sorted = $this->asset->categories->sortBy('start_date');

        $events = collect([]);
        foreach ($sorted as $category) {
            $start_date = Carbon::parse($category->start_date ?? $cycle->start_date);
            $end_date = Carbon::parse($category->end_date ?? $cycle->end_date);
            // Event type: +1 for start, -1 for end
            $events->push([$start_date, 1]);
            $events->push([$end_date, -1]);
        }

        $sortedEvents = $events->sort(function ($a, $b) {
            if ($a[0]->lt($b[0])) return -1;
            if ($a[0]->gt($b[0])) return 1;
            return $a[1] <=> $b[1];
        })->values();

        $max_overlap = 0;
        $overlap = 0;

        foreach ($sortedEvents as $event) {
            $overlap += $event[1];
            $max_overlap = max($max_overlap, $overlap);
        }

        // Trigger an error if overlaping is greater than max
        if ($max_overlap > $this->getMaxAssignedCategories()) {
            $fail("Exceeded maximum allowed simultaneously active categories for this {$this->asset->asset_type}. Found {$max_overlap} overlapping");
        }
    }

    /**
     * Get Airline specific parameters for max assigned categories validation rule
     */
    private function getMaxAssignedCategories(): int
    {
        $validatorAirlineParam = ValidatorAirlineParam::where('airline_id', $this->airlineID)
            ->whereHas('validator', fn($q) => $q->where('name', ValidatorRulesEnum::MaxAssignedCategories->value))
            ->first();

        return $validatorAirlineParam
            ? $validatorAirlineParam->parameters['value']
            : self::DEFAULT_MAX_ASSIGNED_CATEGORIES;
    }
}
