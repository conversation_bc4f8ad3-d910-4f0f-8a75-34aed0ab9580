<?php

namespace App\Rules;

use App\Models\CollectionValue;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Contracts\Validation\ValidationRule;

class ValueListRule implements ValidationRule
{
    protected int $fieldID;

    protected int $fieldValueID;

    protected int $cycleID;

    protected string $assetType;

    // params are expecgted as a json object
    public function __construct(mixed $params)
    {
        $this->cycleID = $params->cycleID;
        $this->assetType = $params->assetType;
        $this->fieldID = $params->fieldID;
        $this->fieldValueID = $params->fieldValueID;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $ids = $this->parseIds($value);

        // if value is empty let other validators, like required, handle it
        if (empty($ids)) {
            return;
        }

        if ($this->fieldValueID) {
            $query = CollectionValue::where(
                'collection_id',
                function (Builder $query) {
                    $query->select('collection_id')
                        ->from('fields')
                        ->where('id', $this->fieldID)
                        ->limit(1);
                }
            )
            ->whereIn('id', $ids)
            ->get();

            if ($query->count() !== count($ids)) {
                $fail("The {$attribute} is not in the list.");
            }
        }
        else {
            $fail("Unknown field. The {$attribute} is not in the list.");
        }
    }

    private function parseIds($value)
    {
        switch (gettype($value)) {
            case 'integer':
                return [$value];
            case 'string':
                // run again for case when string contains single id: '1' !== '[1]'
                return $this->parseIds(json_decode($value));
            default:
                return $value;
        }
    }
}
