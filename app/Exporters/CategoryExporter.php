<?php

namespace App\Exporters;

use App\Models\{Cycle, Category, Asset};
use Illuminate\Support\Facades\Storage;

class CategoryExporter
{
    protected const DESTINATION = '/exports/CSV/';
    protected const CATEGORY_IDENTIFIER = 'Category Identifier';
    protected const ITEM_IDENTIFIER = 'Item Identifier';

    protected $cycleID;
    protected $isTemplate;
    protected $langID;

    public function __construct($cycleID, $isTemplate = false, $langID)
    {
        $this->cycleID = $cycleID;
        $this->isTemplate = $isTemplate;
        $this->langID = $langID;
    }

    public function export(array $filters = [])
    {
        $headers = $this->buildHeaders();
        // If isTemplate is true, we only need to export the headers
        $data = $this->isTemplate ? [] : $this->buildData();

        return $this->generateCSV($headers, $data);
    }

    private function buildHeaders()
    {
        $headers = [
            self::CATEGORY_IDENTIFIER,
            self::ITEM_IDENTIFIER,
            'Type'
        ];
        return $headers;
    }

    private function buildData()
    {
        $cycle = Cycle::find($this->cycleID);
        $categoryItems = $cycle->getAllCategoryItems();
        $parentCategoryIDs = $categoryItems->pluck('category_id')->unique()->toArray();
        $childCategoryIDs = collect($categoryItems)
            ->filter(function ($item) {
                return $item['type'] === 'category';
            })
            ->pluck('item_id')
            ->unique()
            ->toArray();

        $assetIDs = collect($categoryItems)
            ->filter(function ($item) {
                return $item['type'] === 'asset';
            })
            ->pluck('item_id')
            ->unique()
            ->toArray();

        $parentCategories = Category::with('categoryValues')->whereIn('id', $parentCategoryIDs)->get()->keyBy('id');
        $childCategories = Category::with('categoryValues')->whereIn('id', $childCategoryIDs)->get()->keyBy('id');
        $assets = Asset::whereIn('id', $assetIDs)->get()->keyBy('id');

        $data = [];

        foreach ($categoryItems as $item) {
            $row = [];
            $parentCategory = $parentCategories->get($item['category_id']);
            $row[self::CATEGORY_IDENTIFIER] = $parentCategory ?
                $parentCategory->categoryValues->firstWhere('language_id', $this->langID)?->value : null;

            if ($item['type'] === 'category') {
                $childCategory = $childCategories->get($item['item_id']);
                if ($childCategory) {
                    $row[self::ITEM_IDENTIFIER] = $childCategory->categoryValues->firstWhere('language_id', $this->langID)?->value;
                } else {
                    $row[self::ITEM_IDENTIFIER] = null;
                }
            } else {
                $asset = $assets->get($item['item_id']);
                $row[self::ITEM_IDENTIFIER] = $asset ? $asset->title : null;
            }

            $row['Type'] = $item['type'];

            $data[] = $row;
        }

        return $data;
    }

    private function generateCSV($headers, $data)
    {
        $tempFilePath = tempnam(sys_get_temp_dir(), 'exports');
        $handle = fopen($tempFilePath, 'w');

        fputcsv($handle, $headers);

        foreach ($data as $row) {
            fputcsv($handle, $row);
        }

        fclose($handle);

        $filename = date('Y-m-d-His') . '-cycle-' . $this->cycleID . '-category' . '-export.csv';
        $path = self::DESTINATION . $filename;
        Storage::put($path, file_get_contents($tempFilePath));

        return $path;
    }
}
