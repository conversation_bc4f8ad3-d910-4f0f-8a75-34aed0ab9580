<?php

namespace App\Exporters;

use App\Models\Cycle;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Field;
use App\Models\Enums\FieldEnum;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Builder;

class AssetExporter
{
    protected const DESTINATION = '/exports/CSV/';
    protected const ASSET_IDENTIFIER = 'Asset Identifier';

    protected $cycleID;
    protected $assetType;
    protected $isTemplate;

    public function __construct($cycleID, $assetType, $isTemplate = false)
    {
        $this->cycleID = $cycleID;
        $this->assetType = $assetType;
        $this->isTemplate = $isTemplate;
    }

    public function export(array $filters = [])
    {
        $cycle = Cycle::find($this->cycleID);
        $assets = $this->getAssets($cycle);
        $headers = $this->buildHeaders($cycle);
        // If isTemplate is true, we only need to export the headers
        $data = $this->isTemplate ? [] : $this->buildData($assets, $headers);
        return $this->generateCSV($headers, $data);
    }

    private function getAssets(Cycle $cycle)
    {
        $assets = $cycle->assets()
            ->withPivot('status')
            ->when($this->assetType, function ($query) {
                return $query->where('asset_type', $this->assetType);
            })
            ->get();

        // For series exports, include episodes
        if ($this->assetType === 'series') {
            $assets = $assets->merge($cycle->assets()
                ->withPivot('status')
                ->where('asset_type', 'episode')
                ->whereNotNull('parent_asset_id')
                ->get());
        }

        return $assets;
    }

    private function buildHeaders(Cycle $cycle)
    {
        $defaultHeaders = [self::ASSET_IDENTIFIER, 'Start Date', 'End Date'];
        return $this->getFieldNames($defaultHeaders, $cycle);
    }

    private function getFieldNames(array $defaultHeaders, Cycle $cycle)
    {
        $fields = Field::query()
            ->where('schema_id', $cycle->schema_id)
            ->where(function (Builder $query) {
                $query->whereJsonContains('asset_types', $this->assetType)
                    ->orWhereNull('asset_types');
            })
            ->get(['name'])
            ->pluck('name')
            ->all();

        $fields = array_merge($defaultHeaders, $fields);

        if ($this->assetType !== AssetTypeEnum::Series->value) {
            return $fields;
        }

        $seriesFields = [];
        foreach ($fields as $field) {
            $seriesFields[] = 'SERIES:' . $field;
        }

        return array_merge($seriesFields, $fields);
    }

    private function buildData($assets, $headers)
    {
        return $this->assetType === 'series'
            ? $this->buildSeriesData($assets, $headers)
            : $this->buildStandardData($assets, $headers);
    }

    private function buildSeriesData($assets, $headers)
    {
        $seriesAssets = $assets->where('asset_type', 'series')->keyBy('id');
        $episodeAssets = $assets->where('asset_type', 'episode');
        $data = [];

        foreach ($episodeAssets as $episode) {
            $row = array_fill_keys($headers, null);
            $row[self::ASSET_IDENTIFIER] = $episode->title;
            $row['Start Date'] = $episode->start_date ? $episode->start_date->format('Y-m-d') : null;
            $row['End Date'] = $episode->end_date ? $episode->end_date->format('Y-m-d') : null;

            $parentSeriesID = $episode->parent_asset_id;
            $parentSeries = $parentSeriesID ? $seriesAssets->get($parentSeriesID) : null;

            if ($parentSeries) {
                $row['SERIES:' . self::ASSET_IDENTIFIER] = $parentSeries->title;
                $row['SERIES:Start Date'] = $parentSeries->start_date ? $parentSeries->start_date->format('Y-m-d') : null;
                $row['SERIES:End Date'] = $parentSeries->end_date ? $parentSeries->end_date->format('Y-m-d') : null;
                $row = $this->setFieldValues($row, $parentSeries, true);
            }

            $data[] = $this->setFieldValues($row, $episode, false);
        }

        return $data;
    }

    private function buildStandardData($assets, $headers)
    {
        $data = [];

        foreach ($assets as $asset) {
            $row = array_fill_keys($headers, null);
            $row[self::ASSET_IDENTIFIER] = $asset->title;
            $row['Start Date'] = $asset->start_date ? $asset->start_date->format('Y-m-d') : null;
            $row['End Date'] = $asset->end_date ? $asset->end_date->format('Y-m-d') : null;
            $data[] = $this->setFieldValues($row, $asset, false);
        }

        return $data;
    }

    private function setFieldValues($row, $asset, bool $isSeries)
    {
        $notStandardValues = $asset->getFieldsWithValues($this->cycleID, false);
        $standardValues = $asset->getFieldsWithValues($this->cycleID, true);
        $fieldsWithValues = $notStandardValues->merge($standardValues);

        $updatedRow = $row;

        foreach ($fieldsWithValues as $field) {
            if (!empty($field->name) && !empty($field->value)) {
                $fieldName = $isSeries ? 'SERIES:' . $field->name : $field->name;

                if ($field->field_type === FieldEnum::Dropdown) {
                    $updatedRow[$fieldName] = $field->value->getFormattedDropdownValue();
                } else {
                    $updatedRow[$fieldName] = $field->value->getValue();
                }
            }
        }

        return $updatedRow;
    }

    private function generateCSV($headers, $data)
    {
        $tempFilePath = tempnam(sys_get_temp_dir(), 'exports');
        $handle = fopen($tempFilePath, 'w');

        fputcsv($handle, $headers);

        foreach ($data as $row) {
            $rowData = [];
            foreach ($headers as $header) {
                $rowData[] = $row[$header] ?? '';
            }
            fputcsv($handle, $rowData);
        }

        fclose($handle);

        $filename = date('Y-m-d-His') . '-cycle-' . $this->cycleID . '-asset-' . $this->assetType . '-export.csv';
        $path = self::DESTINATION . $filename;
        Storage::put($path, file_get_contents($tempFilePath));
        unlink($tempFilePath);

        return $path;
    }
}
