<?php

namespace App\Exporters;

use App\Models\Cycle;
use Illuminate\Support\Facades\Storage;
use App\Models\Enums\FieldEnum;

class AssetExporter
{
    protected const DESTINATION = '/exports/CSV/';
    protected const ASSET_IDENTIFIER = 'Asset Identifier';

    protected $cycleID;
    protected $assetType;
    protected $isTemplate;

    public function __construct($cycleID, $assetType, $isTemplate = false)
    {
        $this->cycleID = $cycleID;
        $this->assetType = $assetType;
        $this->isTemplate = $isTemplate;
    }

    public function export(array $filters = [])
    {
        $cycle = Cycle::find($this->cycleID);
        $assets = $this->getAssets($cycle);
        $headers = $this->buildHeaders($assets);
        // If isTemplate is true, we only need to export the headers
        $data = $this->isTemplate ? [] : $this->buildData($assets, $headers);
        return $this->generateCSV($headers, $data);
    }

    private function getAssets(Cycle $cycle)
    {
        $assets = $cycle->assets()
            ->withPivot('status')
            ->when($this->assetType, function ($query) {
                return $query->where('asset_type', $this->assetType);
            })
            ->get();

        // For series exports, include episodes
        if ($this->assetType === 'series') {
            $assets = $assets->merge($cycle->assets()
                ->withPivot('status')
                ->where('asset_type', 'episode')
                ->whereNotNull('parent_asset_id')
                ->get());
        }

        return $assets;
    }

    private function buildHeaders($assets)
    {
        $fields = $this->getFieldNames($assets);
        $headers = [self::ASSET_IDENTIFIER];

        if ($this->assetType === 'series') {
            // We'll prepend SERIES: to the header so we can distinguish on the import
            $headers[] = 'SERIES:' . self::ASSET_IDENTIFIER;
        }

        return array_merge($headers, $fields);
    }

    private function getFieldNames($assets)
    {
        return $assets->flatMap(function ($asset) {
            $fieldNames = collect($asset->getFields($this->cycleID))->pluck('name');
            if ($asset->asset_type === 'series') {
                $fieldNames = $fieldNames->map(function ($fieldName) {
                    return 'SERIES:' . $fieldName;
                });
            }
            return $fieldNames->all();
        })->unique()->values()->all();
    }

    private function buildData($assets, $headers)
    {
        return $this->assetType === 'series' 
            ? $this->buildSeriesData($assets, $headers) 
            : $this->buildStandardData($assets, $headers);
    }

    private function buildSeriesData($assets, $headers)
    {
        $seriesAssets = $assets->where('asset_type', 'series')->keyBy('id');
        $episodeAssets = $assets->where('asset_type', 'episode');
        $data = [];

        foreach ($episodeAssets as $episode) {
            $row = array_fill_keys($headers, null);
            $row[self::ASSET_IDENTIFIER] = $episode->title;

            $parentSeriesID = $episode->parent_asset_id;
            $parentSeries = $parentSeriesID ? $seriesAssets->get($parentSeriesID) : null;

            if ($parentSeries) {
                $row['SERIES:' . self::ASSET_IDENTIFIER] = $parentSeries->title;
                $row = $this->setFieldValues($row, $parentSeries, true);
            }

            $data[] = $this->setFieldValues($row, $episode, false);
        }

        return $data;
    }

    private function buildStandardData($assets, $headers)
    {
        $data = [];

        foreach ($assets as $asset) {
            $row = array_fill_keys($headers, null);
            $row[self::ASSET_IDENTIFIER] = $asset->title;
            $data[] = $this->setFieldValues($row, $asset, false);
        }

        return $data;
    }

    private function setFieldValues($row, $asset, bool $isSeries)
    {
        $notStandardValues = $asset->getFieldsWithValues($this->cycleID, false);
        $standardValues = $asset->getFieldsWithValues($this->cycleID, true);
        $fieldsWithValues = $notStandardValues->merge($standardValues);

        $updatedRow = $row;

        foreach ($fieldsWithValues as $field) {
            if (!empty($field->name) && !empty($field->value)) {
                $fieldName = $isSeries ? 'SERIES:' . $field->name : $field->name;
                
                if ($field->field_type === FieldEnum::Dropdown) {
                    $updatedRow[$fieldName] = $field->value->getFormattedDropdownValue();
                } else {
                    $updatedRow[$fieldName] = $field->value->getValue();
                }
            }
        }

        return $updatedRow;
    }

    private function generateCSV($headers, $data)
    {
        $tempFilePath = tempnam(sys_get_temp_dir(), 'exports');
        $handle = fopen($tempFilePath, 'w');

        fputcsv($handle, $headers);

        foreach ($data as $row) {
            $rowData = [];
            foreach ($headers as $header) {
                $rowData[] = $row[$header] ?? '';
            }
            fputcsv($handle, $rowData);
        }

        fclose($handle);

        $filename = date('Y-m-d-His') . '-cycle-' . $this->cycleID . '-asset-' . $this->assetType . '-export.csv';
        $path = self::DESTINATION . $filename;
        Storage::put($path, file_get_contents($tempFilePath));
        unlink($tempFilePath);

        return $path;
    }
}