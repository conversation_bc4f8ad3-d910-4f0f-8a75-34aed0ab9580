id: 70398
name: aero-metadata-api
environments:
  staging:
    network: Aero
    memory: 1024
    cli-memory: 512
    runtime: 'php-8.4:al2'
    database: aero-metadata-staging
    storage: aero-metadata-uploads-staging
    queue-concurrency: 50
    domain: staging-metadata-api.cinesend.aero
    build:
      - 'composer install --no-dev'
      - 'php artisan event:cache'
    deploy:
      - 'php artisan migrate --force'
  production:
    network: Aero
    memory: 1024
    cli-memory: 512
    runtime: 'php-8.4:al2'
    database: aero-metadata-production
    storage: aero-metadata-uploads-production
    domain: metadata-api.cinesend.aero
    build:
      - 'composer install --no-dev'
      - 'php artisan event:cache'
    deploy:
      - 'php artisan migrate --force'
