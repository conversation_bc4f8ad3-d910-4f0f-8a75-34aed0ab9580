# aero-metadata-api

[![Tests](https://github.com/BitCine/aero-metadata-api/actions/workflows/laravel.yml/badge.svg)](https://github.com/BitCine/aero-metadata-api/actions/workflows/laravel.yml)

![Code Coverage](https://public-assets-dev.dev.cinesend.com/github-badges/aero-metadata-api-badge.svg)

## About

-   Represents the backend for aero-metadata-client
-   Built on Laravel 12+
-   MySQL 8.0+
-   This is in full-API mode (no blades, no front end routes save for the default 404's etc.)
-   To get up and running:


## Run composer to get all our dependencies installed:

```
composer install
```

### Create the relevant MySQL database

- Create a database called `aero_metadata_api` on `127.0.0.1:3306` (default)
`mysql -u root`
`create database aero_metadata_api;`
`quit`

### Create your .env file

- Copy the `.env.example` file into a new file `.env`
- Run `php artisan key:generate`

### Add sensitive .env information

- AWS creds can be found in 1Password under `aero-metadata-api`
- Find the `VIASAT_API` credentials in 1Password and copy them to your `.env` and `.env.testing` files

### Migrate and seed the database

- Run `php artisan migrate:fresh --seed`

### Create a Valet link for the site so that it can be served on HTTPS

```
valet secure
valet link
valet open
```

### Et voila! A nice little API only.

Once up and running the default backend should be `https://aero-metadata-api.test`.

If all set, you should see "API is running!"

You can hit `https://aero-metadata-api.test/up` to confirm the application is running.

### Log-in defaults

Since you have run `--seed` on your migration, there should be a default organization and a single user to sign in.
The credentials for the default user are: `<EMAIL>` with password: `password`

### Run the test suite.

The testing suite will use the base `.env` file with required overrides specified in `phpunit.xml`. Run using:

`php artisan test` 


### Extra dependencies

You will need to install Imagick, if it hasn't been installed on your system already.

You can tell if it has been installed by running:

`php -m | grep -i imagick`

If you get no results, install running: 

```shell
brew install imagemagick
pecl install imagick
```

### Code Coverage

Code overage is available when testing via Xdebug. You will need to install the Xdebug driver if you do not have it.

1. You need to install Xdebug if you don't have it. `pecl install xdebug` (via brew, pecl, this should work for you)
2. Edit your php.ini to enable coverage (find it with `php -i | grep php.ini`)

```
[xdebug]
zend_extension="xdebug.so"
xdebug.mode=develop,debug,coverage
xdebug.start_with_request = yes
```

3. Run `artisan test` and coverage will be output to ./reports/
4. Use the utility of your choice to view the outputs (html, text, or clover.xml)

You can always run the tests without coverage. `artisan test --no-coverage`

### Code Formatting

Please use your IDE's plugin support for Laravel Pint to run this automatically.

If you must, please use `./vendor/bin/pint --dirty` to format your changed code.

Please do not Use `./vendor/bin/pint` to run formatter on all code.

### Code Analysis

Please use `./vendor/bin/phpstan` or your IDE plugin to analyze code.
 
The current config is set to level '5' which is fairly balanced and will report missing return types, 
method types, and general caveats found with usage of framework tools.

Models can be tagged with `#[AllowDynamicProperties]`, most everywhere else that has undefined property 
errors should be declared properly in the class(es)
