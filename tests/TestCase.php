<?php

namespace Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\{AssetAudio, AssetVideo};
use App\Models\{Asset, Airline, Cycle, Field, FieldValue, Organization, Schema, User, Issue, Validator, ValidatorFieldType, ValidatorField, AssetImage, Category, Collection, CollectionValue, FieldLabel, Role, ValidatorAirlineParam, Language};
use App\Models\Enums\{AssetStatus, IssueEnum, FieldEnum, ValidatorRulesEnum, ValidatorSeverityEnum, RoleEnum};
use Database\Seeders\RoleSeeder;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    protected $user;
    protected $organization;
    protected $organization2;
    protected $airline;
    protected $cycle;
    protected $field;
    protected $stringField;
    protected $stringFieldValue;
    protected $dateField;
    protected $dateFieldValue;
    protected $seriesSynopsisField;
    protected $imageField;
    protected $imageFieldValue;
    protected $asset;
    protected $assetVideo;
    protected $assetAudio;
    protected $schema;
    protected $issue;
    protected $collection;
    protected $collectionItem;
    protected $cspEditableCollection;
    protected $cspEditableCollectionItem;
    protected $dropdownField;
    protected $category;
    protected $rootCategory;
    protected $validator;
    protected $validatorFieldType;
    protected $validatorField;
    protected $assetImage;
    protected $readOnlyUser;
    protected $cspEditorUser;
    protected $validatorAirlineParam;
    protected $fieldLabel;

    public function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();

        (new RoleSeeder())->run(); // create roles

        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'role_id' => Role::where('name', RoleEnum::SuperAdmin->value)->first()->id,
        ]);

        $this->readOnlyUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'role_id' => Role::where('name', RoleEnum::CspReadOnly->value)->first()->id,
        ]);

        $this->cspEditorUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'role_id' => Role::where('name', RoleEnum::CspEdit->value)->first()->id,
        ]);

        $this->schema = Schema::create(['is_root' => true, 'name' => 'Root Schema', 'version_number' => 1]);
    }

    public function createAirline()
    {
        $this->airline = Airline::factory()->create([
            'organization_id' => $this->organization->id,
        ]);

        $this->organization->airlines()->attach($this->airline);

        return $this->airline;
    }

    public function createOrganization2()
    {
        return $this->organization2 = Organization::factory()->create(['name' => 'Organization 2']);
    }

    public function createCycle($startDate = null, $endDate = null)
    {
        return $this->cycle = Cycle::factory()->create([
            'airline_id' => $this->airline->id,
            'schema_id' => $this->schema->id,
            'start_date' => $startDate ?? now(),
            'end_date' => $endDate ?? now()->addMonth(),
        ]);
    }

    public function createCollection()
    {
        return $this->collection = Collection::create([
            'name' => 'Test Genres',
            'airline_id' => $this->airline->id,
            'organization_id' => $this->organization->id,
            'schema_id' => $this->schema->id,
            'viasat_only' => true,
        ]);
    }

    public function createCollectionItem(string $label = 'Action')
    {
        return $this->collectionItem = CollectionValue::create([
            'collection_id' => $this->collection->id,
            'label' => $label,
        ]);
    }

    public function createCSPEditableCollection()
    {
        return $this->cspEditableCollection = Collection::create([
            'name' => 'Test Genres',
            'airline_id' => $this->airline->id,
            'organization_id' => $this->organization->id,
            'schema_id' => $this->schema->id,
            'viasat_only' => false,
        ]);
    }

    public function createCSPEditableCollectionItem(string $label = 'Action')
    {
        return $this->cspEditableCollectionItem = CollectionValue::create([
            'collection_id' => $this->cspEditableCollection->id,
            'label' => $label,
        ]);
    }

    public function createDropdownField()
    {
        return $this->dropdownField = Field::create([
            'name' => 'Genre',
            'field_type' => FieldEnum::Dropdown->value,
            'schema_id' => $this->schema->id,
            'collection_id' => $this->collection->id,
        ]);
    }

    public function createStringField(bool $isLocalizable = false)
    {
        return $this->stringField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Title',
            'field_type' => 'string',
            'is_localizable' => $isLocalizable,
        ]);
    }

    public function createImageField()
    {
        return $this->imageField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Poster Image',
            'field_type' => FieldEnum::Image->value,
        ]);
    }

    public function createImageFieldValue($fileName = 'poster_image_for_a_specific_movie.jpg')
    {
        return $this->imageFieldValue = FieldValue::create([
            'field_id' => $this->imageField->id,
            'asset_id' => $this->asset->id,
            'value' => $fileName
        ]);
    }

    public function createStringFieldValue(?int $languageId = null)
    {
        $this->stringFieldValue = FieldValue::create([
            'field_id' => $this->stringField->id,
            'asset_id' => $this->asset->id,
            'value' => 'Test Value',
            'language_id' => $languageId,
        ]);
    }

    public function createDateField()
    {
        return $this->dateField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Filming Date',
            'field_type' => 'datetime'
        ]);
    }

    public function createDateFieldValue()
    {
        return $this->dateFieldValue = FieldValue::create([
            'field_id' => $this->dateField->id,
            'asset_id' => $this->asset->id,
            'value' => now()->addDays(2)
        ]);
    }

    public function createAsset($startDate = null, $endDate = null, $assetType = 'movie', AssetStatus $status = null)
    {
        $this->asset = Asset::create([
            'asset_type' => 'movies',
            'start_date' => $startDate ?? now(),
            'end_date' => $endDate ?? now()->addMonth(),
            'asset_type' => $assetType,
            'title' => 'some-unique-title-or-filename'
        ]);
        if ($this->cycle) {
            $this->cycle->assets()->attach($this->asset->id, [
                'status' => $status !== null ? $status : AssetStatus::New,
            ]);
        }
        return $this->asset;
    }

    public function createSeriesField()
    {
        return $this->seriesSynopsisField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Series Synopsis',
            'field_type' => 'string',
            'asset_types' => ['series']
        ]);
    }

    public function createIssue()
    {
        return $this->issue = Issue::create([
            'description' => 'Test Description',
            'cycle_id' => $this->cycle->id,
            'asset_id' => $this->asset->id,
            'asset_image_id' => null,
            'field_value_id' => $this->stringFieldValue->id,
            'validator_field_id' => $this->validatorField->id,
            'type' => IssueEnum::Metadata->value
        ]);
    }


    public function createAssetImage($fileName = null, $status = 'pending')
    {
        $rand = Str::random(10);
        if (!$fileName) {
            $fileName = "test-image-{$rand}.jpg";
        }
        $file = UploadedFile::fake()->image($fileName, 100, 100)->size(120);
        $path = Storage::put('/airline/1/assets/1/fields/1', $file);

        return $this->assetImage = AssetImage::create([
            'airline_id' => $this->airline->id,
            'cycle_id' => $this->cycle->id,
            'status' => $status,
            // 'asset_id' => $this->asset->id,
            // 'field_id' => $this->imageField->id,
            'file_name' => $file->getClientOriginalName(),
            'path' => $path,
            'mime' => $file->getMimeType(),
            'size' => $file->getSize(),
            'extension' => $file->getClientOriginalExtension()
        ]);
    }

    public function createValidator()
    {
        return $this->validator = Validator::create([
            'name' => ValidatorRulesEnum::Required->value,
            'description' => 'The field is required.',
        ]);
    }

    public function createValidatorFieldType()
    {
        return $this->validatorFieldType = ValidatorFieldType::create([
            'validator_id' => $this->validator->id,
            'field_type' => FieldEnum::String->value,
            'enabled' => true
        ]);
    }

    public function createValidatorField($parameters = null)
    {
        return $this->validatorField = ValidatorField::create([
            'field_id' => $this->stringField->id,
            'validator_id' => $this->validator->id,
            'severity' => ValidatorSeverityEnum::Warning->value,
            'enabled' => true,
            'parameters' => $parameters
        ]);
    }

    public function createRootCategory()
    {
        return $this->rootCategory = Category::create([
            'type' => 'categories',
            'is_root' => true,
            'airline_id' => $this->airline->id,
            'cycle_id' => $this->cycle->id,
            'asset_types' => ["movie"]
        ]);
    }

    public function createCategory()
    {
        return $this->category = Category::create([
            'type' => 'assets',
            'airline_id' => $this->airline->id,
            'cycle_id' => $this->cycle->id,
            'asset_types' => ['movie'],
        ]);
    }

    public function createField(string $type = FieldEnum::String->value)
    {
        return $this->field = Field::create([
            'name' => 'Field_' . Str::random(5),
            'field_type' => $type,
            'schema_id' => $this->schema->id,
        ]);
    }

    public function createAssetVideo()
    {
        return $this->assetVideo = AssetVideo::create([
            'cycle_id' => $this->cycle->id,
            'airline_id' => $this->airline->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
            'mime' => 'video/mp4',
            'extension' => 'mp4',
            'file_name' => 'test-video.mp4',
            'size' => 12345
        ]);
    }

    public function createAssetAudio()
    {
        return $this->assetAudio = AssetAudio::create([
            'cycle_id' => $this->cycle->id,
            'airline_id' => $this->airline->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
            'mime' => 'audio/mp3',
            'extension' => 'mp3',
            'file_name' => 'test-audio.mp3',
            'size' => 889
        ]);
    }

    public function createValidatorAirlineParam($parameters = [])
    {
        return $this->validatorAirlineParam = ValidatorAirlineParam::create([
            'validator_id' => $this->validator->id,
            'airline_id' => $this->airline->id,
            'parameters' => $parameters
        ]);
    }

    public function createFieldLabel()
    {
        return $this->fieldLabel = FieldLabel::create([
            "field_id" => $this->stringField->id,
            "language_id" => Language::getDefaultLanguageId(),
            "value" => "initial label",
        ]);
    }
}
