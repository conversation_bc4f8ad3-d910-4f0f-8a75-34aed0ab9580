<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Enums\AssetTypeEnum;
use PHPUnit\Framework\Attributes\DataProvider;

class CategoryTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCycle();
        $this->createRootCategory();
        $this->createCategory();
    }

    #[DataProvider('privileged_user_category_provider')]
    public function test_category_can_be_created(string $userType, string $categoryType)
    {
        $user = $userType === 'admin' ? $this->user : $this->readOnlyUser;
        $is_root = $categoryType === 'root' ? 1 : 0;

        $createData = [
            'type' => 'assets',
            'airline_id' => $this->airline->id,
            'cycle_id' => $this->cycle->id,
            'viasat_id' => uniqid('viasat'),
            'is_root' => $is_root,
        ];
        $response = $this->actingAs($user)->postJson('/categories', $createData);

        $response->assertStatus(201);
        $this->assertDatabaseHas('categories', $createData);
    }

    public function test_root_category_cannot_be_created_by_unprivileged_user()
    {
        $createData = [
            'type' => 'assets',
            'airline_id' => $this->airline->id,
            'cycle_id' => $this->cycle->id,
            'viasat_id' => uniqid('viasat'),
            'is_root' => 1,
        ];
        $response = $this->actingAs($this->readOnlyUser)->postJson('/categories', $createData);

        $response->assertForbidden();
        $this->assertDatabaseMissing('categories', $createData);
    }

    public static function privileged_user_category_provider()
    {
        return [
            ['admin', 'root'],
            ['admin', 'asset'],
            ['readonly', 'asset'],
        ];
    }

    #[DataProvider('privileged_user_category_provider')]
    public function test_category_can_be_updated(string $userType, string $categoryType)
    {
        $user = $userType === 'admin' ? $this->user : $this->readOnlyUser;
        $category = $categoryType === 'root' ? $this->rootCategory : $this->category;

        $response = $this->actingAs($user)->putJson("/categories/{$category->id}", [
            'asset_types' => [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value],
        ]);

        $response->assertStatus(202);

        $this->assertDatabaseHas('categories', [
            'id' => $category->id,
            'asset_types' => json_encode([AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value]),
        ]);
    }

    public function test_root_category_cannot_be_updated_by_unprivileged_user()
    {
        $category = $this->rootCategory;

        $response = $this->actingAs($this->readOnlyUser)->putJson("/categories/{$category->id}", [
            'asset_types' => [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value],
        ]);

        $response->assertForbidden();

        $this->assertDatabaseMissing('categories', [
            'id' => $category->id,
            'asset_types' => json_encode([AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value]),
        ]);
    }

    #[DataProvider('privileged_user_category_provider')]
    public function test_category_can_be_deleted(string $userType, string $categoryType)
    {
        $user = $userType === 'admin' ? $this->user : $this->readOnlyUser;
        $category = $categoryType === 'root' ? $this->rootCategory : $this->category;

        $this->actingAs($user)->deleteJson("/categories/{$category->id}");

        $this->assertDatabaseMissing('categories', [
            'id' => $category->id,
        ]);
    }

    public function test_root_category_cannot_be_deleted_by_unprivileged_user()
    {
        $category = $this->rootCategory;

        $response = $this->actingAs($this->readOnlyUser)->deleteJson("/categories/{$category->id}");

        $response->assertForbidden();
        $this->assertDatabaseHas('categories', [
            'id' => $category->id,
        ]);
    }

    public function test_category_asset_types_format()
    {
        $category = $this->category;
        $category->asset_types = [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value];
        $category->save();

        $response = $this->actingAs($this->user)->get("/categories/{$category->id}/get-addable-items?type=assets");

        $response->assertStatus(200);
    }
}
