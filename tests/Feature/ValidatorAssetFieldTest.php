<?php

namespace Tests\Feature;

use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\FieldEnum;
use App\Models\Enums\ValidatorRulesEnum;
use Tests\TestCase;

class ValidatorAssetFieldTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createStringField();
        $this->createStringFieldValue();
        // assign validator
        $this->createValidator();
        $this->createValidatorFieldType();
        $this->createValidatorField();
    }


    public function test_field_value_is_unique_within_cycle(): void
    {
        // change validator rule
        $this->validator->name = ValidatorRulesEnum::UniqueInCycle->value;
        $this->validator->save();

        // first field value should pass
        $response = $this->actingAs($this->user)->postJson("/field-values", ['cycle_id' => $this->cycle->id, 'field_id' => $this->stringField->id, 'asset_id' => $this->asset->id, 'value' => 'Unique Value']);
        $response->assertCreated();

        // second field value should fail
        $this->createAsset();
        // same field, different asset
        $response = $this->actingAs($this->user)->postJson("/field-values", ['cycle_id' => $this->cycle->id, 'field_id' => $this->stringField->id, 'asset_id' => $this->asset->id, 'value' => 'Unique Value']);
        $response->assertUnprocessable();

        // third field value should pass
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'Another Unique Value']);
        $response->assertAccepted();
    }


    public function test_field_value_is_unique_within_tv_series(): void
    {
        // change validator rule
        $this->validator->name = ValidatorRulesEnum::UniqueInTVSeries->value;
        $this->validator->save();

        // parent asset - series
        $this->asset->asset_type = AssetTypeEnum::Series->value;
        $this->asset->save();
        $series_asset = $this->asset;

        // child asset - episode 1
        $this->createAsset(assetType: AssetTypeEnum::Episode->value);
        $this->asset->parent_asset_id = $series_asset->id;
        $this->asset->save();

        $this->createStringFieldValue();
        // first field value should pass
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'Unique Value']);
        $response->assertAccepted();

        // child asset - episode 2
        $this->createAsset(assetType: AssetTypeEnum::Episode->value);
        $this->asset->parent_asset_id = $series_asset->id;
        $this->asset->save();

        // second field value should fail
        $this->createStringFieldValue();
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'Unique Value']);
        $response->assertUnprocessable();

        // third field value should pass
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'Another Unique Value']);
        $response->assertAccepted();
    }


    public function test_movie_filename_pattern_validator(): void
    {
        // update validator rule
        $this->validator->name = ValidatorRulesEnum::FileNamePattern->value;
        $this->validator->save();

        $this->createAsset(assetType: AssetTypeEnum::Movie->value);
        $this->createStringFieldValue();

        // should fail
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'some-random-value-1234.mp4']);
        $response->assertUnprocessable();

        // should pass - `<IATA>YYMM_Movie_Feature_<Title>.mp4`
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'ABC2506_Movie_Feature_Starbucks.mp4']);
        $response->assertAccepted();
    }


    public function test_episode_filename_pattern_validator(): void
    {
        // update validator rule
        $this->validator->name = ValidatorRulesEnum::FileNamePattern->value;
        $this->validator->save();

        $this->createAsset(assetType: AssetTypeEnum::Episode->value);
        $this->createField(FieldEnum::Video->value);
        $this->createAssetVideo();

        // should fail
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->assetVideo->id}", ['cycle_id' => $this->cycle->id, 'value' => '4n0th3r-random-value-1234.mp4']);
        $response->assertUnprocessable();

        // should pass - `<IATA>YYMM_TV_Program_<Title>_SxxExx.mp4`
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->assetVideo->id}", ['cycle_id' => $this->cycle->id, 'value' => 'IOP2606_Movie_Feature_TV_Program_Holiday_Special_S01E01.mp4']);
        $response->assertAccepted();
    }


    public function test_string_value_min_length_validator(): void
    {
        // update validator rule
        $this->validator->name = ValidatorRulesEnum::Min->value;
        $this->validator->save();

        $this->validatorField->parameters = json_encode(['value' => 5]);
        $this->validatorField->save();

        // should fail
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'Once']);
        $response->assertUnprocessable();

        // should pass
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", ['cycle_id' => $this->cycle->id, 'value' => 'Once upon a time...']);
        $response->assertAccepted();
    }
}
