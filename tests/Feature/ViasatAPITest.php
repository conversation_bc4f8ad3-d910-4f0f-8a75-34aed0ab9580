<?php

namespace Tests\Feature;

use App\Models\Asset;
use App\Models\AssetAudio;
use App\Models\AssetVideo;
use App\Models\Enums\FieldEnum;
use App\Models\Field;
use App\Models\FieldValue;
use Tests\TestCase;
use App\Services\ViasatService;

class ViasatAPITest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createField();
    }

    public function test_it_gets_a_signature()
    {
        // Given
        $viasatService = new ViasatService();

        // When
        $signature = $viasatService->getSignature();

        // Then
        $this->assertNotEmpty($signature, 'Signature should not be empty after authorization');
    }

    public function test_it_gets_media_info_files_for_audio_from_web_route()
    {
        // When
        $response = $this->actingAs($this->user)->get('/media-info-files?fileType=audio');

        // Then
        $response->assertStatus(200);
        $response->assertJson([
            [
                'file_type' => 'audio',
            ]
        ]);
    }

    public function test_it_gets_file_info_for_an_audio_file_name()
    {
        // Given
        $viasatService = new ViasatService();
        $fileName = '170727-233150_Keith_Jars_-_03_-_Aquatic_Kiss_out.m4a';

        // When
        $response = $viasatService->getFileInfo('audio', $fileName);

        // Then
        $this->assertNotEmpty($response, 'Response should not be empty for file info');
        $this->assertContains($fileName, $response);
        $this->assertContains('audio', $response);
    }

    public function test_autofill_populates_cycle_size()
    {
        $total_snapshot_size = $this->cycle->total_snapshot_size;
        $recalled_snapshot_size = $this->cycle->recalled_snapshot_size;

        $this->createField(FieldEnum::Video->value);
        $fileName = "170724-144344_Trailer_InBruges.mp4";

        $data = [
            'value' => $fileName,
            'cycle_id' => $this->cycle->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
        ];
        $response = $this->actingAs($this->user)->postJson("/asset-videos", $data);
        $response->assertCreated();

        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/auto-fill");
        $response->assertOk();

        // TODO: do we need this?
        // check if the cycle size has been updated
        // possible it will not change
        $this->cycle->refresh();

        $this->assertNotEquals($total_snapshot_size, $this->cycle->total_snapshot_size);
        $this->assertNotEquals($recalled_snapshot_size, $this->cycle->recalled_snapshot_size);
    }


    public function test_field_value_can_be_stored_and_updated()
    {
        $this->createStringField();
        $this->createStringFieldValue();

        $data = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringFieldValue->field_id,
            'value' => 'Some string value'
        ];
        // store field value
        $response = $this->actingAs($this->user)->postJson("/field-values", $data);
        $response->assertCreated();

        // update field value
        $data = [
            'value' => 'updated value',
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringFieldValue->field_id,
        ];

        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", $data);
        $response->assertAccepted();

        // db has record in proper table
        $fieldValue = FieldValue::find($response->json('field.id'));
        $this->assertEquals('updated value', $fieldValue->value);
    }

    public function test_field_type_video_can_be_stored()
    {
        $this->createField(FieldEnum::Video->value);
        $fileName = "170724-144332_Trailer_ExMachina.mp4";

        $data = [
            'value' => $fileName,
            'cycle_id' => $this->cycle->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
        ];
        $response = $this->actingAs($this->user)->postJson("/asset-videos", $data);
        $response->assertCreated();

        // db has record in proper table
        $assetVideo = AssetVideo::find($response->json('field.id'));
        $this->assertEquals($fileName, $assetVideo->file_name);
    }

    public function test_field_type_video_can_be_updated()
    {
        $this->createField(FieldEnum::Video->value);
        $this->createAssetVideo();
        $fileName = "170724-144337_Trailer_Godzilla.mp4";

        $data = [
            'value' => $fileName,
            'cycle_id' => $this->cycle->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
        ];

        $response = $this->actingAs($this->user)->putJson("/asset-videos/{$this->assetVideo->id}", $data);
        $response->assertAccepted();

        // db has record
        $assetVideo = AssetVideo::find($response->json('field.id'));
        $this->assertEquals($fileName, $assetVideo->file_name);
    }


    public function test_field_type_audio_can_be_stored()
    {
        $this->createField(FieldEnum::Audio->value);
        $fileName = "file_example_MP3_700KB.mp3";

        $data = [
            'value' => $fileName,
            'cycle_id' => $this->cycle->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
        ];
        $response = $this->actingAs($this->user)->postJson("/asset-audios", $data);
        $response->assertCreated();

        // db has record in proper table
        $assetVideo = AssetAudio::find($response->json('field.id'));
        $this->assertEquals($fileName, $assetVideo->file_name);
    }

    public function test_field_type_audio_can_be_updated()
    {
        $this->createField(FieldEnum::Audio->value);
        $this->createAssetAudio();
        $fileName = "file_example_MP3_700KB.mp3";

        $data = [
            'value' => $fileName,
            'cycle_id' => $this->cycle->id,
            'asset_id' => $this->asset->id,
            'field_id' => $this->field->id,
        ];
        $response = $this->actingAs($this->user)->putJson("/asset-audios/{$this->assetAudio->id}", $data);
        $response->assertAccepted();

        // db has record
        $assetAudio = AssetAudio::find($response->json('field.id'));
        $this->assertEquals($fileName, $assetAudio->file_name);
    }
}
