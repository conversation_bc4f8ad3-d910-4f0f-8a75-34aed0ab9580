<?php

namespace Tests\Feature;

use App\Models\Enums\AssetTypeEnum;
use Tests\TestCase;

class StaticValuesControllerTest extends TestCase
{
    public function test_get_asset_types_returns_successful_response()
    {
        $response = $this->getJson('api/asset-types');

        $response->assertOk();
        $response->assertJsonStructure([
            '*' => [
                'value',
                'route_value',
                'plural_label',
                'label',
                'parent_type',
                'is_parent'
            ]
        ]);
    }

    public function test_get_asset_types_returns_all_asset_types()
    {
        $response = $this->getJson('api/asset-types');

        $data = $response->json();
        $expectedCount = count(AssetTypeEnum::cases());

        $this->assertCount($expectedCount, $data);
    }

    public function test_get_asset_types_route_values_are_correct()
    {
        $response = $this->getJson('api/asset-types');

        $data = $response->json();
        
        $expectedRouteValues = [
            'movie' => 'movies',
            'series' => 'series',
            'music' => 'music',
            'audiobook' => 'audiobooks',
            'podcast' => 'podcasts',
            'ad' => 'ads',
            'episode' => 'episodes',
            'track' => 'tracks'
        ];

        foreach ($expectedRouteValues as $assetType => $expectedRouteValue) {
            $item = collect($data)->firstWhere('value', $assetType);
            $this->assertNotNull($item, "Asset type '{$assetType}' not found in response");
            $this->assertEquals($expectedRouteValue, $item['route_value'], "Route value for '{$assetType}' should be '{$expectedRouteValue}'");
        }
    }
}
