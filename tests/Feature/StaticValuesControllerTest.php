<?php

namespace Tests\Feature;

use App\Models\Enums\AssetTypeEnum;
use Tests\TestCase;

class StaticValuesControllerTest extends TestCase
{
    public function test_get_asset_types_returns_successful_response()
    {
        $response = $this->getJson('api/asset-types');

        $response->assertOk();
        $response->assertJsonStructure([
            '*' => [
                'value',
                'route_value',
                'plural_label',
                'label',
                'parent_type',
                'is_parent'
            ]
        ]);
    }

    public function test_get_asset_types_returns_all_asset_types()
    {
        $response = $this->getJson('api/asset-types');

        $data = $response->json();
        $expectedCount = count(AssetTypeEnum::cases());

        $this->assertCount($expectedCount, $data);
    }

    // public function test_get_asset_types_contains_expected_asset_types()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $values = collect($data)->pluck('value')->toArray();

    //     // Test that all enum values are present
    //     foreach (AssetTypeEnum::cases() as $assetType) {
    //         $this->assertContains($assetType->value, $values);
    //     }
    // }

    // public function test_get_asset_types_has_correct_structure_for_movie()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $movie = collect($data)->firstWhere('value', 'movie');

    //     $this->assertNotNull($movie);
    //     $this->assertEquals([
    //         'value' => 'movie',
    //         'route_value' => 'movies',
    //         'plural_label' => 'Movies',
    //         'label' => 'Movie',
    //         'parent_type' => null,
    //         'is_parent' => false
    //     ], $movie);
    // }

    // public function test_get_asset_types_has_correct_structure_for_series()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $series = collect($data)->firstWhere('value', 'series');

    //     $this->assertNotNull($series);
    //     $this->assertEquals([
    //         'value' => 'series',
    //         'route_value' => 'series',
    //         'plural_label' => 'TV Series',
    //         'label' => 'TV Series',
    //         'parent_type' => null,
    //         'is_parent' => true
    //     ], $series);
    // }

    // public function test_get_asset_types_has_correct_structure_for_episode()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $episode = collect($data)->firstWhere('value', 'episode');

    //     $this->assertNotNull($episode);
    //     $this->assertEquals([
    //         'value' => 'episode',
    //         'route_value' => 'episodes',
    //         'plural_label' => 'Episodes',
    //         'label' => 'Episode',
    //         'parent_type' => AssetTypeEnum::Series,
    //         'is_parent' => false
    //     ], $episode);
    // }

    // public function test_get_asset_types_has_correct_structure_for_music()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $music = collect($data)->firstWhere('value', 'music');

    //     $this->assertNotNull($music);
    //     $this->assertEquals([
    //         'value' => 'music',
    //         'route_value' => 'music',
    //         'plural_label' => 'Music',
    //         'label' => 'Music',
    //         'parent_type' => null,
    //         'is_parent' => true
    //     ], $music);
    // }

    // public function test_get_asset_types_has_correct_structure_for_track()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $track = collect($data)->firstWhere('value', 'track');

    //     $this->assertNotNull($track);
    //     $this->assertEquals([
    //         'value' => 'track',
    //         'route_value' => 'tracks',
    //         'plural_label' => 'Tracks',
    //         'label' => 'Track',
    //         'parent_type' => AssetTypeEnum::Music,
    //         'is_parent' => false
    //     ], $track);
    // }

    // public function test_get_asset_types_parent_child_relationships()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
        
    //     // Test parent types
    //     $parentTypes = collect($data)->where('is_parent', true)->pluck('value')->toArray();
    //     $this->assertContains('series', $parentTypes);
    //     $this->assertContains('music', $parentTypes);

    //     // Test child types have correct parents
    //     $episode = collect($data)->firstWhere('value', 'episode');
    //     $this->assertEquals(AssetTypeEnum::Series, $episode['parent_type']);

    //     $track = collect($data)->firstWhere('value', 'track');
    //     $this->assertEquals(AssetTypeEnum::Music, $track['parent_type']);

    //     // Test non-parent types
    //     $movie = collect($data)->firstWhere('value', 'movie');
    //     $this->assertFalse($movie['is_parent']);
    //     $this->assertNull($movie['parent_type']);
    // }

    // public function test_get_asset_types_includes_all_expected_types()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
    //     $values = collect($data)->pluck('value')->toArray();

    //     $expectedTypes = [
    //         'movie',
    //         'series',
    //         'music',
    //         'audiobook',
    //         'podcast',
    //         'ad',
    //         'episode',
    //         'track'
    //     ];

    //     foreach ($expectedTypes as $expectedType) {
    //         $this->assertContains($expectedType, $values, "Asset type '{$expectedType}' should be included in the response");
    //     }
    // }

    public function test_get_asset_types_route_values_are_correct()
    {
        $response = $this->getJson('api/asset-types');

        $data = $response->json();
        
        $expectedRouteValues = [
            'movie' => 'movies',
            'series' => 'series',
            'music' => 'music',
            'audiobook' => 'audiobooks',
            'podcast' => 'podcasts',
            'ad' => 'ads',
            'episode' => 'episodes',
            'track' => 'tracks'
        ];

        foreach ($expectedRouteValues as $assetType => $expectedRouteValue) {
            $item = collect($data)->firstWhere('value', $assetType);
            $this->assertNotNull($item, "Asset type '{$assetType}' not found in response");
            $this->assertEquals($expectedRouteValue, $item['route_value'], "Route value for '{$assetType}' should be '{$expectedRouteValue}'");
        }
    }

    // public function test_get_asset_types_labels_are_correct()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
        
    //     $expectedLabels = [
    //         'movie' => 'Movie',
    //         'series' => 'TV Series',
    //         'music' => 'Music',
    //         'audiobook' => 'Audiobook',
    //         'podcast' => 'Podcast',
    //         'ad' => 'Ad',
    //         'episode' => 'Episode',
    //         'track' => 'Track'
    //     ];

    //     foreach ($expectedLabels as $assetType => $expectedLabel) {
    //         $item = collect($data)->firstWhere('value', $assetType);
    //         $this->assertNotNull($item, "Asset type '{$assetType}' not found in response");
    //         $this->assertEquals($expectedLabel, $item['label'], "Label for '{$assetType}' should be '{$expectedLabel}'");
    //     }
    // }

    // public function test_get_asset_types_plural_labels_are_correct()
    // {
    //     $response = $this->getJson('/asset-types');

    //     $data = $response->json();
        
    //     $expectedPluralLabels = [
    //         'movie' => 'Movies',
    //         'series' => 'TV Series',
    //         'music' => 'Music',
    //         'audiobook' => 'Audiobooks',
    //         'podcast' => 'Podcasts',
    //         'ad' => 'Ads',
    //         'episode' => 'Episodes',
    //         'track' => 'Tracks'
    //     ];

    //     foreach ($expectedPluralLabels as $assetType => $expectedPluralLabel) {
    //         $item = collect($data)->firstWhere('value', $assetType);
    //         $this->assertNotNull($item, "Asset type '{$assetType}' not found in response");
    //         $this->assertEquals($expectedPluralLabel, $item['plural_label'], "Plural label for '{$assetType}' should be '{$expectedPluralLabel}'");
    //     }
    // }
}
