<?php

namespace Tests\Feature;

use Tests\TestCase;

class CollectionTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCollection();
    }

    public function test_can_read_collections(): void
    {
        $response = $this->actingAs($this->user)->getJson('/collections');
        $response->assertOk();

        $response->assertJsonCount(1);

        $response = $this->actingAs($this->user)->getJson("/collections/{$this->collection->id}");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_collections(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson('/collections');
        $response->assertForbidden();

        $response = $this->actingAs($this->cspEditorUser)->getJson("/collections/{$this->collection->id}");
        $response->assertForbidden();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_collection(): void
    {
        $createData = [
            'name' => 'test collection',
            'airline_id' => $this->airline->id,
            'schema_id' => $this->schema->id,
        ];

        $response = $this->actingAs($this->user)->postJson('/collections', $createData);

        $response->assertCreated();
    }

    public function test_cannot_create_collection_by_unprivileged_user(): void
    {
        $createData = [
            'name' => 'test collection',
            'airline_id' => $this->airline->id,
            'schema_id' => $this->schema->id,
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/collections', $createData);

        $response->assertForbidden();
    }

    public function test_can_update_collection(): void
    {
        $updateData = [
            'name' => 'new name',
        ];

        $response = $this->actingAs($this->user)->putJson("/collections/{$this->collection->id}", $updateData);

        $response->assertOk();
    }

    public function test_cannot_update_collection_by_unprivileged_user(): void
    {
        $updateData = [
            'name' => 'new name',
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("/collections/{$this->collection->id}", $updateData);

        $response->assertForbidden();
    }

    public function test_can_delete_collection(): void
    {
        $response = $this->actingAs($this->user)->deleteJson("/collections/{$this->collection->id}");

        $response->assertOk();
    }

    public function test_cannot_delete_collection_by_unprivileged_user(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/collections/{$this->collection->id}");

        $response->assertForbidden();
    }
}
