<?php

namespace Tests\Feature;

use App\Models\Airline;
use Tests\TestCase;
use App\Models\Collection;
use App\Models\Cycle;
use App\Models\Organization;
use App\Models\Role;
use App\Models\Schema;
use App\Models\User;
use App\Models\Enums\RoleEnum;

class CollectionTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCycle();
        $this->createCollection();
    }

    public function test_can_read_collections(): void
    {
        $response = $this->actingAs($this->user)->getJson('/collections');
        $response->assertOk();

        $response->assertJsonCount(1);

        $response = $this->actingAs($this->user)->getJson("/collections/{$this->collection->id}");
        $response->assertOk();
    }

    public function test_limited_user_can_read_collection_for_accessible_schema(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson('/collections');
        $response->assertJsonCount(1);

        $response = $this->actingAs($this->cspEditorUser)->getJson("/collections/{$this->collection->id}");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_collection_from_unauthorized_schema(): void
    {
        $org = Organization::factory()->create();
        $airline = Airline::factory()->create([
            'organization_id' => $org->id,
        ]);
        $org->airlines()->attach($airline);
        $user = User::factory()->create([
            'organization_id' => $org->id,
            'role_id' => Role::where('name', RoleEnum::CspReadOnly->value)->first()->id,
        ]);
        $schema = Schema::create(['is_root' => true, 'name' => 'Root Schema', 'version_number' => 1]);
        Cycle::factory()->create([
            'airline_id' => $airline->id,
            'schema_id' => $schema->id
        ]);
        $collection = Collection::create([
            'name' => 'Collection in another org',
            'airline_id' => $airline->id,
            'organization_id' => $org->id,
            'schema_id' => $schema->id,
        ]);

        // should only find 1 from its own org (created above)
        $response = $this->actingAs($user)->getJson('/collections');
        $response->assertJsonCount(1);

        // can view from its own org
        $response = $this->actingAs($user)->getJson("/collections/{$collection->id}");
        $response->assertOk();

        // cannot view from another org
        $response = $this->actingAs($user)->getJson("/collections/{$this->collection->id}");
        $response->assertForbidden();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_collection(): void
    {
        $createData = [
            'name' => 'test collection',
            'airline_id' => $this->airline->id,
            'schema_id' => $this->schema->id,
        ];

        $response = $this->actingAs($this->user)->postJson('/collections', $createData);

        $response->assertCreated();
    }

    public function test_cannot_create_collection_by_unprivileged_user(): void
    {
        $createData = [
            'name' => 'test collection',
            'airline_id' => $this->airline->id,
            'schema_id' => $this->schema->id,
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/collections', $createData);

        $response->assertForbidden();
    }

    public function test_can_update_collection(): void
    {
        $updateData = [
            'name' => 'new name',
        ];

        $response = $this->actingAs($this->user)->putJson("/collections/{$this->collection->id}", $updateData);

        $response->assertOk();
    }

    public function test_cannot_update_collection_by_unprivileged_user(): void
    {
        $updateData = [
            'name' => 'new name',
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("/collections/{$this->collection->id}", $updateData);

        $response->assertForbidden();
    }

    public function test_can_delete_collection(): void
    {
        $response = $this->actingAs($this->user)->deleteJson("/collections/{$this->collection->id}");

        $response->assertOk();
    }

    public function test_cannot_delete_collection_by_unprivileged_user(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/collections/{$this->collection->id}");

        $response->assertForbidden();
    }
}
