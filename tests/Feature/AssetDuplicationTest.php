<?php

namespace Tests\Feature;

use App\Models\Asset;
use App\Models\Category;
use App\Models\FieldValue;
use Illuminate\Support\Carbon;
use Tests\TestCase;

class AssetDuplicationTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createStringField();
        $this->createStringFieldValue();
        $this->createRootCategory();
        $this->createCategory();
    }


    /**
     * When updating asset with status `New` it should not be duplicated 
     */
    public function test_new_asset_is_not_duplicated(): void
    {
        $data = [
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringField->id,
            'value' => 'Some Value'
        ];

        $result = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", $data);
        $result->assertAccepted();

        // db does not have asset duplicates
        $this->assertDatabaseMissing('assets', ['origin_asset_id' => $this->asset->id]);
    }

    /**
     * Asset shoud be duplicated only once within cycle, if asset status is not `New` and field value chaneged 
     */
    public function test_asset_is_duplicated_only_once_within_cycle_when_field_value_changed()
    {
        // updated asset end date so it can be migrated into new cycle
        $this->asset->end_date = Carbon::parse($this->asset->end_date)->addYears(10);
        $this->asset->save();

        // migrate assets into new cycle - will change asset status to `Holdover`
        $response = $this->actingAs($this->user)->postJson("/cycles", ['airline_id' => $this->airline->id]);
        $response->assertCreated();
        $cycle = $response->json('cycle'); // new cycle 

        // asset was migrated (same asset id, relation with new cycle)
        $migratedAsset = Asset::with(['cycles' => fn($q) => $q->where('cycle_id', $cycle['id'])])->first();
        $migratedFieldValue = FieldValue::where('asset_id', $migratedAsset->id)->where('field_id', $this->stringField->id)->first();

        // change field value for migrated asset to trigger duplication
        $data = [
            'cycle_id' => $cycle['id'],
            'field_id' => $this->stringField->id,
            'value' => 'New Value for the string field'
        ];
        $result = $this->actingAs($this->user)->putJson("/field-values/{$migratedFieldValue->id}", $data);
        $result->assertAccepted();

        // asset was duplicated
        $duplicatedAsset = Asset::whereHas('cycles', fn($q) => $q->where('cycle_id', $cycle['id']))->first();
        $this->assertNotEquals($migratedAsset->id, $duplicatedAsset->id);

        // second update will not trigger duplication
        $data = [
            'cycle_id' => $cycle['id'],
            'field_id' => $this->stringField->id,
            'value' => 'New Value #2'
        ];
        $result = $this->actingAs($this->user)->putJson("/field-values/{$migratedFieldValue->id}", $data);
        $result->assertAccepted();

        // asset was not duplicated 
        $asset = Asset::whereHas('cycles', fn($q) => $q->where('cycle_id', $cycle['id']))->first();
        $this->assertEquals($asset->id, $duplicatedAsset->id);
    }


    public function test_categories_are_duplicated_with_asset()
    {
        $this->rootCategory->addChildCategories([$this->category->id]);
        $this->category->addAssets([$this->asset->id]);
        $this->assertDatabaseHas('category_items', [
            'asset_id' => $this->asset->id,
            'category_id' => $this->category->id
        ]);

        // migrate assets into new cycle - will change asset status to `Holdover`
        $response = $this->actingAs($this->user)->postJson("/cycles", ['airline_id' => $this->airline->id]);
        $response->assertCreated();
        $newCycle = $response->json('cycle'); // new cycle 

        // asset was migrated (same asset and catgory, relation with new cycle)
        $migratedAsset = $this->asset; // It's the same asset record just with new relationsinp
        //Asset::with(['cycles' => fn($q) => $q->where('cycle_id', $cycle['id'])])->first();
        $migratedCategory = Category::where('cycle_id', $newCycle['id'])
            ->where('origin_id', $this->category->id)
            ->first();

        $this->assertNotNull($migratedCategory, "Category was not migrated into the new cycle");
        $this->assertDatabaseHas('category_items', [
            'asset_id' => $migratedAsset->id,
            'category_id' => $migratedCategory->id
        ]);

        // Trigger the asset duplication 
        $migratedFieldValue = FieldValue::where('asset_id', $migratedAsset->id)
            ->where('field_id', $this->stringField->id)
            ->firstOrFail();

        // Verify the duplicate asset and that it has the correct category relationship
        $data = [
            'cycle_id' => $newCycle['id'],
            'field_id' => $this->stringField->id,
            'value' => 'Value for the string field'
        ];

        $this->actingAs($this->user)
            ->putJson("/field-values/{$migratedFieldValue->id}", $data)
            ->assertAccepted();

        // asset was duplicated
        $duplicatedAsset = Asset::where('origin_asset_id', $migratedAsset->id)
            ->whereHas('cycles', fn($q) => $q->where('cycle_id', $newCycle['id']))
            ->first();
        $this->assertNotEquals($migratedAsset->id, $duplicatedAsset->id);
        $this->assertDatabaseMissing('category_items', [
            'asset_id' => $duplicatedAsset->id,
            'category_id' => $this->category->id // category id will be different
        ]);
        $this->assertDatabaseHas('category_items', [
            'asset_id' => $duplicatedAsset->id,
            'category_id' => $migratedCategory->id
        ]);
    }
}
