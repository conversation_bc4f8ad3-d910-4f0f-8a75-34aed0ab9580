<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Rules\ValueListRule;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;
use App\Models\Enums\FieldEnum;

class ValidatorValueListRuleTest extends TestCase
{
    private ValueListRule $rule;
    private array $testData;

    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createCollection();

        $this->collection->collectionValues()->createMany([
            ['label' => 'Action'],    // id: 1
            ['label' => 'Adventure'], // id: 2
            ['label' => 'Comedy'],    // id: 3
            ['label' => 'Drama']      // id: 4
        ]);

        $data = [
            'collection_id' => $this->collection->id,
            'name' => 'Test Field',
            'field_type' => FieldEnum::Dropdown->value,
            'is_localizable' => false,
            'use_languages' => false,
            'is_external_api_value_source' => false,
            'is_size_field' => false,
            'schema_id' => $this->schema->id,
            'is_multi_select' => false,
        ];

        $this->field = Field::factory()->create($data);

        // crate a validation rule
        $this->rule = $this->makeRule($this->field->id, 1);

        $this->testData = ['cycle' => $this->cycle->id];
    }

    private function makeRule(int $fieldID, int $fieldValueID = 1, int $cycleID = 0, string $assetType = ''): ValueListRule
    {
        $params = (object) [
            'cycleID' => $cycleID,
            'assetType' => $assetType,
            'fieldID' => $fieldID,
            'fieldValueID' => $fieldValueID, // This acts as a gate in the current rule
        ];
        return new ValueListRule($params);
    }

    public function test_empty_value_passes_validation_as_per_rule_logic()
    {
        $this->testData['value'] = ''; // empty id

        // single value
        $validator = Validator::make($this->testData, ['value' => $this->rule]);
        $this->assertTrue($validator->passes());
    }


    public function test_empty_array_or_json_array_value_passes()
    {
        $this->testData['value'] = []; // empty array
        $validator = Validator::make($this->testData, ['value' => $this->rule]);
        $this->assertTrue($validator->passes());

        $this->testData['value'] = "[]"; // empty json
        $validator = Validator::make($this->testData, ['value' => $this->rule]);
        $this->assertTrue($validator->passes());
    }


    public function test_validation_fails_when_fieldvalueid_is_zero_and_value_is_not_empty_due_to_rule_error()
    {
        $rule = $this->makeRule($this->field->id, 0); // fieldValueID = 0, triggers bug

        $this->testData['value'] = 1;
        $validator = Validator::make($this->testData, ['value' => $rule]);

        $this->assertTrue($validator->fails());
        // Check that 'value' is marked as a failed attribute
        $this->assertArrayHasKey('value', $validator->failed());

        // Validation Failed because no fieid value id provided
        $this->assertNotEmpty($validator->errors()->get('value'));
    }


    public function test_valid_single_id_as_integer_passes()
    {
        $this->testData['value'] = 1;
        $validator = Validator::make($this->testData, ['value' => $this->rule]);
        $this->assertTrue($validator->passes());
    }


    public function test_valid_single_id_as_string_integer_passes()
    {
        $this->testData['value'] = '1';
        $validator = Validator::make($this->testData, ['value' => $this->rule]);
        $this->assertTrue($validator->passes());
    }


    public function test_validation_fails_if_values_belong_to_a_different_collection()
    {
        $this->createCollection(); // another collection
        $this->collection->collectionValues()->createMany([
            ['label' => 'Romance'],    // id: 5
            ['label' => 'Thriller'],   // id: 6
            ['label' => 'Mystery'],    // id: 7
        ]);

        $this->testData['value'] = [5, 6];
        $validator = Validator::make($this->testData, ['value' => $this->rule]);
        $this->assertTrue($validator->fails());
        $this->assertEquals('The value is not in the list.', $validator->errors()->first('value'));
    }
}
