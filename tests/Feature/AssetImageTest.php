<?php

namespace Tests\Feature;

use Mockery;
use <PERSON><PERSON>y\MockInterface;
use Tests\TestCase;

class AssetImageTest extends TestCase
{ 
    protected MockInterface $assetImageMock;

    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createImageField();
    }


    protected function tearDown(): void
    {
        parent::tearDown();
    }
    
    public function test_asset_images_listing(): void
    {
        $this->createAssetImage();
        $response = $this->actingAs($this->user)->getJson('/asset-images');
        $response->assertOk();
    }

    public function test_asset_image_can_be_created(): void
    { 
        $createData = [
            'airline_id' => $this->airline->id,
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->imageField->id,
            'file_name' => 'test-image.jpg',
            'size' => 1024,
            'mime' => 'image/jpeg'
        ];
        
        // Act 
        $response = $this->actingAs($this->user)->postJson('/asset-images', $createData); 
        
        // Assert
        $response->assertCreated();
    }
    
    public function test_asset_image_can_be_viewed(): void
    {
        $this->createAssetImage();

        $response = $this->actingAs($this->user)->getJson("/asset-images/{$this->assetImage->id}"); 

        $response->assertOk();
    }

    public function test_asset_image_upload_url_can_be_generated(): void
    {
        $this->createAssetImage();

        $mockResponseURL = 'https://' . env('AWS_BUCKET') . '.s3.amazonaws.com/' . $this->assetImage->path;

        /** @var \App\Models\AssetImage|\Mockery\MockInterface $mock */
        $mock = Mockery::mock($this->assetImage)->makePartial();
        $mock->shouldReceive('getSignedUploadURL')
            ->once()
            ->andReturn($mockResponseURL);
    
        $generatedUploadURL = $mock->getSignedUploadURL();
    
        $this->assertStringStartsWith($mockResponseURL, $generatedUploadURL);
    }

    public function test_asset_image_download_url_can_be_generated(): void
    {
        $this->createAssetImage();

        $mockResponseURL = 'https://' . env('AWS_BUCKET') . '.s3.amazonaws.com/' . $this->assetImage->path;

        /** @var \App\Models\AssetImage|\Mockery\MockInterface $mock */
        $mock = Mockery::mock($this->assetImage)->makePartial();
        $mock->shouldReceive('getSignedDownloadURL')
            ->once()
            ->andReturn($mockResponseURL);

        $generatedDownloadURL = $mock->getSignedDownloadURL();

        $this->assertStringStartsWith($mockResponseURL, $generatedDownloadURL);
    }

    public function test_asset_image_can_be_trashed(): void
    {
        $this->createAssetImage();
        $response = $this->actingAs($this->user)->deleteJson("/asset-images/{$this->assetImage->id}");
        
        $response->assertOk();
        $response->assertJsonFragment(['message' => 'Asset image deleted successfully.']);
    }
}
