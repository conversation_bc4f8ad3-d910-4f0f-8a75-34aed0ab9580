<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\{
    Airline,
    ActivityLog,
    Issue
};
use Illuminate\Foundation\Testing\RefreshDatabase;

class ActivityLogTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createStringField();
        $this->createStringFieldValue();
        $this->createValidator();
        $this->createValidatorFieldType();
        $this->createValidatorField();
    }

    public function test_it_creates_activity_log_on_model_create()
    {
        $airline = Airline::factory()->create();
        $activityLog = ActivityLog::where('description', 'created')
            ->where('subject_type', Airline::class)
            ->where('subject_id', $airline->id)
            ->first();

        $this->assertEquals($airline->id, $activityLog->subject_id);
        $this->assertEquals('created', $activityLog->description);
    }

    public function test_it_creates_activity_log_on_model_update()
    {
        $airline = Airline::factory()->create(['name' => 'Old Name']);
        $airline->update(['name' => 'New Name']);
        $activityLog = ActivityLog::where('description', 'updated')->first();
        
        $this->assertEquals($airline->id, $activityLog->subject_id);
        $this->assertEquals('updated', $activityLog->description);
        $this->assertEquals('Old Name', $activityLog->properties['old']['name']);
        $this->assertEquals('New Name', $activityLog->properties['attributes']['name']);
    }

    public function test_it_creates_activity_log_on_model_delete()
    {
        $airline = Airline::factory()->create();
        $airlineID = $airline->id;
        $airline->delete();
        $activityLog = ActivityLog::where('description', 'deleted')->first();

        $this->assertEquals($airlineID, $activityLog->subject_id);
        $this->assertEquals('deleted', $activityLog->description);
    }

    public function it_gets_activity_log_issue_properties()
    {
        $issue = $this->createIssue();
        $activityLog = ActivityLog::where('subject_id', $issue->id)
            ->where('subject_type', Issue::class)
            ->first();

        $this->assertEquals('Movies: some-unique-title-or-filename', $activityLog->subject_title);
        $this->assertEquals('/airlines/1/cycles/1/metadata/movies/1', $activityLog->subject_route);
        $this->assertEquals('Created issue: Test Description', $activityLog->activity_description);
    }
}