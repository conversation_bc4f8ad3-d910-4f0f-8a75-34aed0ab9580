<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Airline;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;

class AirlineControllerTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
    }

    public function test_index_returns_all_airlines()
    {
        $this->actingAs($this->user);
        $response = $this->getJson('/airlines');
        $response->assertOk();
    }

    public function test_index_is_unauthorized()
    {
        $response = $this->getJson('/airlines');
        $response->assertUnauthorized();
    }

    public function test_index_can_search_airlines()
    {
        $this->actingAs($this->user);

        Airline::factory()->create(['name' => 'Test Airline']);
        Airline::factory()->create(['name' => 'Another Airline']);

        $response = $this->getJson('/airlines?search=Test');

        $response->assertOk()
            ->assertJsonCount(1)
            ->assertJsonFragment(['name' => 'Test Airline']);
    }

    public function test_index_unrestricted_user_can_search_all_airlines()
    {
        $this->createOrganization2();
        $this->actingAs($this->user);

        $airline = Airline::factory()->create(['name' => 'Another Org\'s Airline']);
        $this->organization2->airlines()->attach($airline);

        $response = $this->getJson('/airlines');

        $response->assertOk()
            ->assertJsonCount(2); // 2 airlines = default airline + the one created in this test
    }

    public function test_index_limited_user_can_only_search_airlines_associated_with_org()
    {
        $this->createOrganization2();
        $this->actingAs($this->cspEditorUser);

        $airline1 = Airline::factory()->create(['name' => 'Test Airline 1']);
        $airline2 = Airline::factory()->create(['name' => 'Test Airline 2']);
        $airline3 = Airline::factory()->create(['name' => 'Another Org\'s Airline']);

        $this->organization->airlines()->attach($airline1);
        $this->organization->airlines()->attach($airline2);
        $this->organization2->airlines()->attach($airline3);

        // only associated airlines are returned
        $response = $this->getJson('/airlines');

        $response->assertOk()
            ->assertJsonCount(3); // 3 airlines = default airline + the 2 created in this test

        // can't search for airline in another org
        $response = $this->getJson('/airlines?search=Another');

        $response->assertOk()
            ->assertJsonCount(0);
    }

    public function test_unrestricted_user_can_request_airline_in_another_org()
    {
        $this->createOrganization2();
        $this->actingAs($this->user);

        $airline = Airline::factory()->create(['name' => 'Another Org\'s Airline']);
        $this->organization2->airlines()->attach($airline);

        $response = $this->getJson("/airlines/{$airline->id}");

        $response->assertOk();
    }

    public function test_limited_user_cannot_request_airline_in_another_org()
    {
        $this->createOrganization2();
        $this->actingAs($this->cspEditorUser);

        $airline = Airline::factory()->create(['name' => 'Another Org\'s Airline']);
        $this->organization2->airlines()->attach($airline);

        $response = $this->getJson("/airlines/{$airline->id}");

        $response->assertForbidden();
    }

    public function test_limited_user_can_request_airline_in_same_org()
    {
        $this->actingAs($this->cspEditorUser);

        $response = $this->getJson("/airlines/{$this->airline->id}");

        $response->assertOk();
    }

    public function test_store_creates_new_airline()
    {
        $this->actingAs($this->user);

        $airlineData = Airline::factory()->make()->toArray();

        $response = $this->postJson('/airlines', $airlineData);

        $response->assertStatus(Response::HTTP_CREATED)
            ->assertJson([
                'success' => true,
                'message' => 'Airline created successfully',
            ]);
        $response->assertJson(fn ($json) => $json->has('airline')->etc());
    }

    public function test_store_cannot_create_new_airline_for_unprivileged_user()
    {
        $this->actingAs($this->cspEditorUser);

        $airlineData = Airline::factory()->make()->toArray();

        $response = $this->postJson('/airlines', $airlineData);

        $response->assertForbidden();
    }

    public function test_show_returns_airline()
    {
        $this->actingAs($this->user);

        $airline = Airline::factory()->create();

        $response = $this->getJson("/airlines/{$airline->id}");

        $response->assertOk()
            ->assertJson($airline->toArray());
    }

    public function test_update_modifies_airline()
    {
        $this->actingAs($this->user);

        $airline = Airline::factory()->create();
        $updateData = ['name' => 'Updated Name'];

        $response = $this->putJson("/airlines/{$airline->id}", $updateData);

        $response->assertOk()
            ->assertJson([
                'success' => true,
                'message' => 'Airline updated successfully',
            ]);
        $this->assertDatabaseHas('airlines', $updateData);
    }

    public function test_update_cannot_modify_airline_for_unprivileged_user()
    {
        $this->actingAs($this->cspEditorUser);

        $airline = Airline::factory()->create();
        $updateData = ['name' => 'Updated Name'];

        $response = $this->putJson("/airlines/{$airline->id}", $updateData);

        $response->assertForbidden();
        $this->assertDatabaseMissing('airlines', $updateData);
    }

    public function test_update_modifies_airline_languages_by_string()
    {

        $this->actingAs($this->user);
        $airline = Airline::factory()->create();

        $updateData = ['languages' => 'en,fr'];

        $response = $this->putJson(route('airlines.update', $airline), $updateData);
        $response->assertOk()
            ->assertJson([
                'success' => true,
                'message' => 'Airline updated successfully',
            ]);
    }

    public function test_update_modifies_airline_languages_by_ids()
    {

        $this->actingAs($this->user);
        $airline = Airline::factory()->create();

        $updateData = ['languages' => [1, 2]];

        $response = $this->putJson(route('airlines.update', $airline), $updateData);
        $response->assertOk()
            ->assertJson([
                'success' => true,
                'message' => 'Airline updated successfully',
            ]);
    }

    public function test_update_when_default_language_removed_from_list_default_is_cleared()
    {

        $this->actingAs($this->user);
        $airline = Airline::factory()->create();

        $updateData = ['languages' => [1, 2], 'default_language_id' => 1];

        $response = $this->putJson(route('airlines.update', $airline), $updateData);
        $response->assertOk()
            ->assertJson([
                'success' => true,
                'message' => 'Airline updated successfully',
            ]);

        $response = $this->getJson(route('airlines.update', $airline));
        $response->assertJsonFragment(['default_language_id' => 1]);

        $updateData = ['languages' => [2]];

        $response = $this->putJson(route('airlines.update', $airline), $updateData);
        $response->assertOk()
            ->assertJson([
                'success' => true,
                'message' => 'Airline updated successfully',
            ]);

        $response = $this->getJson(route('airlines.update', $airline));
        $response->assertJsonFragment(['default_language_id' => null]);
    }


    public function test_update_does_not_modify_with_invalid_data()
    {
        $this->actingAs($this->user);

        $airline = Airline::factory()->create();
        $updateData = ['name' => ''];

        $response = $this->putJson("/airlines/{$airline->id}", $updateData);

        $response->assertUnprocessable();
        $this->assertDatabaseMissing('airlines', $updateData);
    }

    public function test_destroy_deletes_airline()
    {
        $this->actingAs($this->user);

        $airline = Airline::factory()->create();

        $response = $this->deleteJson("/airlines/{$airline->id}");

        $response->assertNoContent();

    }

    public function test_destroy_cannot_delete_airline_for_unprivileged_user()
    {
        $this->actingAs($this->cspEditorUser);

        $airline = Airline::factory()->create();

        $response = $this->deleteJson("/airlines/{$airline->id}");

        $response->assertForbidden();
    }

    public function test_cannot_delete_airline_unauthorized()
    {
        $airline = Airline::factory()->create();

        $response = $this->deleteJson("/airlines/{$airline->id}");

        $response->assertUnauthorized();

    }
}
