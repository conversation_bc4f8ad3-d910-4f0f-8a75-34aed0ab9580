<?php

namespace Tests\Feature;

use App\Models\Enums\FieldEnum;
use Tests\TestCase;

class ValidatorFieldTypeTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createValidator();
    }

    public function test_can_read_available_validators_for_field_types(): void
    {
        $response = $this->actingAs($this->user)->getJson("/validator-field-types");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_available_validators_for_field_types(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson("/validator-field-types");
        $response->assertForbidden();
    }

    public function test_can_create_validator_for_field_type_entry(): void
    {
        $createData = [
            'validator_id' => $this->validator->id,
            'field_type' => FieldEnum::String->value,
            'enabled' => 'true'
        ];

        $response = $this->actingAs($this->user)->postJson("/validator-field-types", $createData);
        $response->assertCreated();
    }

    public function test_cannot_create_validator_for_field_type_entry_by_unprivileged_user(): void
    {
        $createData = [
            'validator_id' => $this->validator->id,
            'field_type' => FieldEnum::String->value,
            'enabled' => 'true'
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson("/validator-field-types", $createData);
        $response->assertForbidden();
    }
}
