<?php

namespace Tests\Feature;

use app\Models\User;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\DataProvider;

class UserTest extends TestCase
{
    public function test_user_can_be_created()
    {
        $response = $this->actingAs($this->user)->postJson('/register', [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'organization_id' => $this->organization->id,
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_cannot_be_created_by_unprivileged_user()
    {
        $response = $this->actingAs($this->cspEditorUser)->postJson('/register', [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'organization_id' => $this->organization->id,
        ]);

        $response->assertForbidden();
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_can_be_updated()
    {
        $user = $this->user;

        $response = $this->actingAs($this->user)->put("/users/{$user->id}", [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_cannot_be_updated_by_unprivileged_user()
    {
        $user = $this->user;

        $response = $this->actingAs($this->cspEditorUser)->putJson("/users/{$user->id}", [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
        ]);

        $response->assertForbidden();
        $this->assertDatabaseMissing('users', [
            'id' => $user->id,
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_unprivileged_user_can_update_its_own_details()
    {
        $user = $this->cspEditorUser;

        $new_password = 'new password';

        $response = $this->actingAs($user)->putJson("/users/{$user->id}", [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'password' => $new_password,
            'password_confirmation' => $new_password,
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
        ]);

        $updated_user = User::find($user->id);
        $this->assertTrue(password_verify($new_password, $updated_user->password));
    }

    public function test_unprivileged_user_cannot_update_its_permissions()
    {
        $user = $this->cspEditorUser;

        $response = $this->actingAs($user)->putJson("/users/{$user->id}", [
            'role_id' => 1,
        ]);

        $response->assertForbidden();

        $response = $this->actingAs($user)->putJson("/users/{$user->id}", [
            'organization_id' => 1,
        ]);

        $response->assertForbidden();
    }

    public function test_user_can_be_deleted()
    {
        $user = $this->user;

        $response = $this->actingAs($this->user)->delete("/users/{$user->id}");

        $response->assertStatus(204);
        $this->assertDatabaseMissing('users', [
            'id' => $user->id,
            'deleted_at' => null
        ]);
    }

    public function test_user_cannot_be_deleted_by_unprivileged_user()
    {
        $user = $this->user;

        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/users/{$user->id}");

        $response->assertForbidden();
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'deleted_at' => null
        ]);
    }

    public static function user_type_user_provider()
    {
        return [
            ['admin', 3], // 3 users created by default
            ['cspEditor', 1],
        ];
    }

    #[DataProvider('user_type_user_provider')]
    public function test_user_can_search_all_users(string $type, int $num_results)
    {
        $user = $type === 'admin' ? $this->user : $this->cspEditorUser;
        $other_user = $type === 'admin' ? $this->cspEditorUser : $this->user;

        $this->actingAs($user);

        // test the index endpoint
        $response = $this->getJson('/users');
        $response->assertJsonCount($num_results, 'data');

        // test the id endpoint for same user
        $response = $this->getJson("/users/{$user->id}");
        $response->assertOk();

        // test the id endpoint for another user
        $response = $this->getJson("/users/{$other_user->id}");

        if ($type === 'admin') {
            $response->assertOk();
        } else {
            $response->assertForbidden();
        }
    }
}
