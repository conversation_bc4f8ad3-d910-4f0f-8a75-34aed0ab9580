<?php

namespace Tests\Feature;

use App\Models\Asset;
use App\Models\Enums\AssetStatus;
use App\Models\Enums\FieldEnum;
use Carbon\Carbon;
use Tests\TestCase;

class AssetVersioningEventsTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createStringField();
        $this->createStringFieldValue();
        // New assets are not duplicating
        $this->cycle->assets()->detach($this->asset->id);
        $this->cycle->assets()->attach($this->asset->id, ['status' => AssetStatus::Expiring->value]);
    }

    /**
     * Asset is duplicated if a field value is changed
     */
    public function test_asset_is_duplicated_if_a_field_value_is_changed(): void
    {
        $data = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringField->id,
            'value' => 'New Field Value',
            'language_id' => null
        ];
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", $data);
        $response->assertAccepted();

        // check if asset was duplicated
        $this->assertDatabaseHas('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2,
            'origin_cycle_id' => $this->cycle->id
        ]);

        // check if original asset have field value
        $this->assertDatabaseHas('field_values', [
            'asset_id' => $this->asset->id,
            'field_id' => $this->stringField->id
        ]);

        // check if duplicate asset have field value
        $duplicate = Asset::where('origin_asset_id', $this->asset->id)->first();
        $this->assertDatabaseHas('field_values', [
            'asset_id' => $duplicate->id,
            'field_id' => $this->stringField->id
        ]);
    }

    /**
     * Asset is duplicated if a field is removed via schema change
     */
    public function test_asset_is_duplicated_if_a_field_is_removed_via_schema_change(): void
    {
        // new field
        $this->createStringField();
        $this->createStringFieldValue();
        // asset must have field value to trigger duplication when field is removed
        $response = $this->actingAs($this->user)->deleteJson("/fields/{$this->stringField->id}");
        $response->assertNoContent();

        // check if asset was duplicated
        $this->assertDatabaseHas('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2,
            'origin_cycle_id' => $this->cycle->id
        ]);

        $duplicate = Asset::where('origin_asset_id', $this->asset->id)->first();

        // check if duplicate asset doesn't have old field value
        $this->assertDatabaseMissing('field_values', [
            'asset_id' => $duplicate->id,
            'field_id' => $this->stringField->id
        ]);
    }

    /**
     * Asset is not duplicated if a field and field value is added via schema change
     * 
     */
    public function test_asset_is_not_duplicated_if_a_field_and_field_value_is_added_via_schema_change(): void
    {
        $data = [
            'field_type' => FieldEnum::String->value,
            'name' => 'New Field',
            'schema_id' => $this->cycle->schema->id,
            'asset_types' => $this->getAllAssetTypes()
        ];
        // field is added via schema change
        $response = $this->actingAs($this->user)->postJson("/fields", $data);
        $response->assertCreated();

        $data = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringField->id,
            'value' => 'New Value',
            'language_id' => null
        ];
        // field value is added after schema change
        $response = $this->actingAs($this->user)->postJson("/field-values", $data);
        $response->assertCreated();

        // TODO: fix behaviour. During Test All items have same timestamp
        // $this->assertDatabaseMissing('assets', [
        //     'origin_asset_id' => $this->asset->id,
        //     'version_number' => 2
        // ]);
    }

    /**
     * Asset is only duplicated a single time within a given cycle
     */
    public function test_asset_is_only_duplicated_a_single_time_within_a_given_cycle(): void
    {
        $this->createStringField();
        $this->createStringFieldValue(); // belongs to the current cycle

        $data = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringField->id,
            'value' => 'New Field Value',
            'language_id' => null
        ];
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", $data);
        $response->assertAccepted();

        // duplicate once - pass 
        $this->assertDatabaseHas('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2
        ]);

        // original asset have field value
        $this->assertDatabaseHas('field_values', [
            'asset_id' => $this->asset->id,
            'field_id' => $this->stringField->id
        ]);

        // check if duplicate asset have field value
        $duplicate = Asset::where('origin_asset_id', $this->asset->id)->first();
        $this->assertDatabaseHas('field_values', [
            'asset_id' => $duplicate->id,
            'field_id' => $this->stringField->id
        ]);

        // duplicate second time - fail
        $data['value'] = 'New Field Value 2';
        $response = $this->actingAs($this->user)->putJson("/field-values/{$this->stringFieldValue->id}", $data);
        $response->assertAccepted();

        $this->assertDatabaseMissing('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 3
        ]);
    }

    /**
     * Asset is duplicated if start or end date is edited
     */
    public function test_asset_is_duplicated_if_start_is_edited(): void
    {
        // no duplication when asset title is changed
        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/assets/{$this->asset->id}", ['title' => 'New Title']);
        $response->assertAccepted();

        $this->assertDatabaseMissing('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2
        ]);

        // duplicate when start date is changed 
        $data = ['start_date' => Carbon::now()->format('Y-m-d H:i:s')];
        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/assets/{$this->asset->id}", $data);
        $response->assertAccepted();
        $duplicate = $response->json('asset');
        // check if asset is duplicated
        $this->assertDatabaseHas('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2
        ]);

        // not duplicate when end date is changed because already duplicated
        $data2 = ['end_date' => Carbon::now()->addMonth(2)->format('Y-m-d H:i:s')];
        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/assets/{$duplicate['id']}", $data2);
        $response->assertAccepted();
        // check if asset is duplicated
        $this->assertDatabaseMissing('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 3
        ]);
    }

    /**
     * Asset is duplicated if end date is edited
     */
    public function test_asset_is_duplicated_if_end_date_is_edited(): void
    {
        // no duplication when asset title is changed
        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/assets/{$this->asset->id}", ['title' => 'New Title']);
        $response->assertAccepted();

        $this->assertDatabaseMissing('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2
        ]);

        // duplicate when end date is changed  
        $data2 = ['end_date' => Carbon::now()->addMonth(3)->format('Y-m-d H:i:s')];
        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/assets/{$this->asset->id}", $data2);
        $response->assertAccepted();
        $duplicate = $response->json('asset');
        // check if asset is duplicated
        $this->assertDatabaseHas('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2
        ]);

        // no duplicate when start date is changed because already duplicated
        $data = ['start_date' => Carbon::now()->format('Y-m-d H:i:s')];
        $response = $this->actingAs($this->user)->putJson("/cycles/{$this->cycle->id}/assets/{$duplicate['id']}", $data);
        $response->assertAccepted();
        // check if asset is duplicated
        $this->assertDatabaseMissing('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 3
        ]);
    }

    /**  
     * Asset is duplicated if a collection item has been removed for a list-backed collection and a past field value references that item.
     */
    public function test_asset_is_duplicated_if_a_collection_item_has_been_removed(): void
    {
        $this->createCollection();
        $item1 = $this->createCollectionItem('One');
        $item2 = $this->createCollectionItem('Two');
        $item3 = $this->createCollectionItem('Three');
        $this->createDropdownField();

        $data = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->dropdownField->id,
            'value' => json_encode([$item1->id, $item3->id]),
            'language_id' => null
        ];
        $result = $this->actingAs($this->user)->postJson("/field-values", $data);
        $result->assertCreated();

        $fieldValueID = $result->json('field.id');

        $this->assertDatabaseMissing('assets', [
            'origin_asset_id' => $this->asset->id,
            'version_number' => 2
        ]);

        // asset is duplicated 
        $data = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->dropdownField->id,
            'value' => json_encode([$item1->id]), // item3 has been removed
            'language_id' => null
        ];
        $result = $this->actingAs($this->user)->putJson("/field-values/{$fieldValueID}", $data);
        $result->assertAccepted();

        $this->assertNotNull($result->json('field.asset_id'));
        $this->assertNotEquals($result->json('field.asset_id'), $this->asset->id);
    }
}
