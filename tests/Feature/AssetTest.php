<?php

namespace Tests\Feature;

use App\Models\Enums\AssetStatus;
use Tests\TestCase;
use App\Models\Enums\AssetTypeEnum;
use PHPUnit\Framework\Attributes\DataProvider;

class AssetTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        // validation rules
        $this->createStringField();
        $this->createValidator();
        $this->createValidatorFieldType();
        $this->createValidatorField();
    }


    public function test_asset_can_be_created(): void
    {
        $createData = [
            'cycle_id' => $this->cycle->id,
            'title' => 'Test Asset',
            'asset_type' => AssetTypeEnum::Movie->value,
        ];

        $response = $this->actingAs($this->user)->postJson("cycles/{$this->cycle->id}/assets", $createData);
        $response->assertCreated();
    }


    public function test_asset_can_be_create_with_child_assets(): void
    {
        // parent asset TV series
        $createData = [
            'cycle_id' => $this->cycle->id,
            'title' => 'Test TV Series Asset',
            'asset_type' => AssetTypeEnum::Series->value,
        ];
        $response = $this->actingAs($this->user)->postJson("cycles/{$this->cycle->id}/assets", $createData);
        $response->assertCreated();

        $tvSeriesAsset = $response->json('asset');

        $createData = [
            'cycle_id' => $this->cycle->id,
            'title' => 'Test Episode Asset',
            'asset_type' => AssetTypeEnum::Episode->value,
            'parent_asset_id' => $tvSeriesAsset['id'],
        ];

        $response = $this->actingAs($this->user)->postJson("cycles/{$this->cycle->id}/assets", $createData);
        $response->assertCreated();

        $this->assertDatabaseHas('assets', [
            'id' => $response->json('asset')['id'],
            'parent_asset_id' => $tvSeriesAsset['id'],
        ]);
    }

    public function test_asset_child_and_parent_attr(): void
    {
        $parentAsset = $this->createAsset(assetType: AssetTypeEnum::Series->value);
        $childAsset = $this->createAsset(assetType: AssetTypeEnum::Episode->value);

        $this->cycle->assets()->attach($parentAsset->id, ['status' => 'active']);
        $this->cycle->assets()->attach($childAsset->id, ['status' => 'active']);

        $response = $this->actingAs($this->user)->post("cycles/{$this->cycle->id}/assets/{$parentAsset->id}/attach-assets", [
            'ids' => [$childAsset->id],
            'asset_type' => AssetTypeEnum::Episode->value,
        ]);
        $response->assertOk();

        $this->assertDatabaseHas('assets', [
            'id' => $childAsset->id,
            'parent_asset_id' => $parentAsset->id,
        ]);
        $response = $this->actingAs($this->user)->getJson("cycles/{$this->cycle->id}/assets/{$childAsset->id}");

        $response->assertOk();

        $response->assertJsonFragment(['parent_asset_id' => $parentAsset->id]);
        $response->assertJsonFragments([
            'parent_asset' => [
                'id' => $parentAsset->id,
                'title' => $parentAsset->title,
            ],
        ]);
        // check the child asset
        $response = $this->actingAs($this->user)->getJson("cycles/{$this->cycle->id}/assets/{$parentAsset->id}");

        $response->assertOk();

        $response->assertJsonFragments([
            'child_assets' => [
                'id' => $childAsset->id,
                'title' => $childAsset->title,
            ],
        ]);
    }

    public static function privileged_user_to_field_provider()
    {
        return [
            ['admin', 'title'],
            ['admin', 'start_date'],
            ['cspEditor', 'start_date'],
        ];
    }

    #[DataProvider('privileged_user_to_field_provider')]
    public function test_asset_can_be_updated(string $userType, string $fieldName): void
    {
        $user = $userType === 'admin' ? $this->user : $this->cspEditorUser;

        $asset = $this->createAsset();

        $assetUpdatePayload = [];

        if ($fieldName === 'title') {
            $assetUpdatePayload['title'] = 'Updated Asset';
        } else if ($fieldName === 'start_date') {
            $assetUpdatePayload['start_date'] = '2025-06-18';
        }

        $response = $this->actingAs($user)->putJson("cycles/{$this->cycle->id}/assets/{$asset->id}", $assetUpdatePayload);

        $response->assertAccepted();
    }

    public static function edit_start_date_provider()
    {
        $data = [];

        foreach (['admin', 'cspEditor'] as $role) {
            foreach (AssetStatus::cases() as $status) {
                $isAllowed = $role === 'cspEditor' && in_array($status, [AssetStatus::Holdover, AssetStatus::Expiring]) ? false : true;
                $data[] = [$role, $status, $isAllowed];
            }
        }

        return $data;
    }

    #[DataProvider('edit_start_date_provider')]
    public function test_asset_start_date_can_be_updated(string $userType, AssetStatus $status, bool $isAllowed): void
    {
        $user = $userType === 'admin' ? $this->user : $this->cspEditorUser;

        $this->createCycle('2025-06-18');
        $asset = $this->createAsset();
        $this->cycle->assets()->updateExistingPivot($asset, [
            'status' => $status,
        ]);

        $assetUpdatePayload = ['start_date' => '2025-06-18'];

        $response = $this->actingAs($user)->putJson("cycles/{$this->cycle->id}/assets/{$asset->id}", $assetUpdatePayload);

        if ($isAllowed) {
            $response->assertAccepted();
        } else {
            $response->assertForbidden();
        }
    }

    public function test_asset_title_cannot_be_updated_by_unprivileged_user(): void
    {
        $asset = $this->createAsset();

        $assetUpdatePayload = [
            'title' => 'Updated Asset',
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("cycles/{$this->cycle->id}/assets/{$asset->id}", $assetUpdatePayload);

        $response->assertForbidden();
    }

    public function test_it_lists_assets_with_localized_titles(): void
    {
        // Given
        $this->asset->title = 'My Asset Title Identifier';
        $this->asset->save();

        $this->createStringFieldValue();
        $this->stringFieldValue->value = 'My Localized Title';
        $this->stringFieldValue->save();

        // When
        $response = $this->actingAs($this->user)->get("cycles/{$this->cycle->id}/assets");

        // Then
        $response->assertOk();
        $response->assertJson([
            'data' => [[
                'default_title' => 'My Localized Title',
                'title' => 'My Asset Title Identifier'
            ]]
        ]);
    }

    public static function asset_status_change_provider()
    {
        return [
            [AssetStatus::New, '2025-06-01', '2026-01-01', '2026-02-01', AssetStatus::New],
            [AssetStatus::Recalled, '2025-06-01', '2026-01-01', '2026-02-01', AssetStatus::Recalled],
            [AssetStatus::Holdover, '2024-11-01', '2027-01-01', '2025-06-01', AssetStatus::Expiring], // holdover end date reduces and enters the cycle
            [AssetStatus::Holdover, '2024-11-01', '2027-01-01', '2026-06-01', AssetStatus::Holdover], // holdover end date reduces but still outside cycle
            [AssetStatus::Holdover, '2024-11-01', '2027-01-01', '2027-01-01', AssetStatus::Holdover], // holdover end date increases and still outside cycle
            [AssetStatus::Expiring, '2024-11-01', '2025-11-01', '2026-02-01', AssetStatus::Holdover], // expiring end date increases to outside cycle
            [AssetStatus::Expiring, '2024-11-01', '2025-11-01', '2025-12-01', AssetStatus::Expiring], // expiring end date increases but still inside cycle
            [AssetStatus::Expiring, '2024-11-01', '2025-11-01', '2025-10-01', AssetStatus::Expiring], // expiring end date reduces and still inside cycle
        ];
    }

    #[DataProvider('asset_status_change_provider')]
    public function test_asset_status_changes_with_end_date(AssetStatus $initialStatus, $startDate, $initialEndDate, $newEndDate, AssetStatus $expectedStatus): void
    {
        $this->createCycle('2025-01-01', '2026-01-01');
        $this->createAsset($startDate, $initialEndDate, AssetTypeEnum::Movie, $initialStatus);

        $assetUpdatePayload = [
            'end_date' => $newEndDate,
        ];

        $response = $this->actingAs($this->user)->putJson("cycles/{$this->cycle->id}/assets/{$this->asset->id}", $assetUpdatePayload);

        $response->assertSuccessful();
        $this->assertDatabaseHas('asset_cycle', [
            'asset_id' => $response->json('asset')['id'],
            'cycle_id' => $response->json('asset')['cycle']['id'],
            'status' => $expectedStatus,
        ]);
    }

    public static function asset_start_edit_provider()
    {
        return [
            ['2025-01-01', '2026-01-01', '2025-02-01', AssetStatus::Recalled, true], // inside cycle dates
            ['2025-01-01', '2026-01-01', '2025-12-31', AssetStatus::Recalled, true], // inside cycle dates up to last day of cycle
            ['2025-01-01', '2026-01-01', '2026-01-01', AssetStatus::Recalled, true], // on last day of cycle (should this be false? are cycle dates inclusive or exclusive?)
            ['2025-01-01', '2026-01-01', '2024-12-05', AssetStatus::Recalled, true], // up to 4 weeks before start date
            ['2025-01-01', '2026-01-01', '2024-12-04', AssetStatus::Recalled, false], // 1 day more than 4 weeks before
            ['2025-01-01', '2026-01-01', '2024-10-20', AssetStatus::New, true], // 1 day more than 4 weeks before
            // No tests for Holdover or Expiring status because CSP edit is not allowed at all in those statuses, which is captured in other tests.
        ];
    }

    #[DataProvider('asset_start_edit_provider')]
    public function test_asset_start_edit_by_csp($cycleStartDate, $cycleEndDate, $newAssetStartDate, AssetStatus $status, bool $isAllowed): void
    {
        $this->createCycle($cycleStartDate, $cycleEndDate);
        $this->createAsset($cycleStartDate, $cycleEndDate, AssetTypeEnum::Movie, $status);

        $assetUpdatePayload = [
            'start_date' => $newAssetStartDate,
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("cycles/{$this->cycle->id}/assets/{$this->asset->id}", $assetUpdatePayload);

        if ($isAllowed) {
            $response->assertSuccessful();
        } else {
            $response->assertForbidden();
        }
    }
}
