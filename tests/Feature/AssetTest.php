<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Enums\AssetTypeEnum;
use PHPUnit\Framework\Attributes\DataProvider;

class AssetTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        // validation rules
        $this->createStringField();
        $this->createValidator();
        $this->createValidatorFieldType();
        $this->createValidatorField();
    }


    public function test_asset_can_be_created(): void
    {
        $createData = [
            'cycle_id' => $this->cycle->id,
            'title' => 'Test Asset',
            'asset_type' => AssetTypeEnum::Movie->value,
        ];

        $response = $this->actingAs($this->user)->postJson("cycles/{$this->cycle->id}/assets", $createData);
        $response->assertCreated();
    }


    public function test_asset_can_be_create_with_child_assets(): void
    {
        // parent asset TV series
        $createData = [
            'cycle_id' => $this->cycle->id,
            'title' => 'Test TV Series Asset',
            'asset_type' => AssetTypeEnum::Series->value,
        ];
        $response = $this->actingAs($this->user)->postJson("cycles/{$this->cycle->id}/assets", $createData);
        $response->assertCreated();

        $tvSeriesAsset = $response->json('asset');

        $createData = [
            'cycle_id' => $this->cycle->id,
            'title' => 'Test Episode Asset',
            'asset_type' => AssetTypeEnum::Episode->value,
            'parent_asset_id' => $tvSeriesAsset['id'],
        ];

        $response = $this->actingAs($this->user)->postJson("cycles/{$this->cycle->id}/assets", $createData);
        $response->assertCreated();

        $this->assertDatabaseHas('assets', [
            'id' => $response->json('asset')['id'],
            'parent_asset_id' => $tvSeriesAsset['id'],
        ]);
    }

    public function test_asset_child_and_parent_attr(): void
    {
        $parentAsset = $this->createAsset(assetType: AssetTypeEnum::Series->value);
        $childAsset = $this->createAsset(assetType: AssetTypeEnum::Episode->value);

        $this->cycle->assets()->attach($parentAsset->id, ['status' => 'active']);
        $this->cycle->assets()->attach($childAsset->id, ['status' => 'active']);

        $response = $this->actingAs($this->user)->post("cycles/{$this->cycle->id}/assets/{$parentAsset->id}/attach-assets", [
            'ids' => [$childAsset->id],
            'asset_type' => AssetTypeEnum::Episode->value,
        ]);
        $response->assertOk();

        $this->assertDatabaseHas('assets', [
            'id' => $childAsset->id,
            'parent_asset_id' => $parentAsset->id,
        ]);
        $response = $this->actingAs($this->user)->getJson("cycles/{$this->cycle->id}/assets/{$childAsset->id}");

        $response->assertOk();

        $response->assertJsonFragment(['parent_asset_id' => $parentAsset->id]);
        $response->assertJsonFragments([
            'parent_asset' => [
                'id' => $parentAsset->id,
                'title' => $parentAsset->title,
            ],
        ]);
        // check the child asset
        $response = $this->actingAs($this->user)->getJson("cycles/{$this->cycle->id}/assets/{$parentAsset->id}");

        $response->assertOk();

        $response->assertJsonFragments([
            'child_assets' => [
                'id' => $childAsset->id,
                'title' => $childAsset->title,
            ],
        ]);
    }

    public static function privileged_user_to_field_provider()
    {
        return [
            ['admin', 'title'],
            ['admin', 'start_date'],
            ['cspEditor', 'start_date'],
        ];
    }

    #[DataProvider('privileged_user_to_field_provider')]
    public function test_asset_can_be_updated(string $userType, string $fieldName): void
    {
        $user = $userType === 'admin' ? $this->user : $this->cspEditorUser;

        $asset = $this->createAsset();

        $assetUpdatePayload = [];

        if ($fieldName === 'title') {
            $assetUpdatePayload['title'] = 'Updated Asset';
        } else if ($fieldName === 'start_date') {
            $assetUpdatePayload['start_date'] = '2025-06-18';
        }

        $response = $this->actingAs($user)->putJson("cycles/{$this->cycle->id}/assets/{$asset->id}", $assetUpdatePayload);

        $response->assertAccepted();
    }

    public function test_asset_title_cannot_be_updated_by_unprivileged_user(): void
    {
        $asset = $this->createAsset();

        $assetUpdatePayload = [
            'title' => 'Updated Asset',
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("cycles/{$this->cycle->id}/assets/{$asset->id}", $assetUpdatePayload);

        $response->assertForbidden();
    }

    public function test_it_lists_assets_with_localized_titles(): void
    {
        // Given
        $this->asset->title = 'My Asset Title Identifier';
        $this->asset->save();

        $this->createStringFieldValue();
        $this->stringFieldValue->value = 'My Localized Title';
        $this->stringFieldValue->save();

        // When
        $response = $this->actingAs($this->user)->get("cycles/{$this->cycle->id}/assets");

        // Then
        $response->assertOk();
        $response->assertJson([
            'data' => [[
                'default_title' => 'My Localized Title',
                'title' => 'My Asset Title Identifier'
            ]]
        ]);
    }
}
