<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\{Asset, Import, Field, FieldValue};
use App\Jobs\ProcessImport;
use App\Models\Enums\AssetTypeEnum;
use Illuminate\Support\Facades\Storage;

class ImporterTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
    }

    public function test_it_imports_series_with_episodes()
    {
        $seriesTitleField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Title',
            'field_type' => 'string',
            'asset_types' => ['series']
        ]);

        $seasonField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Season',
            'field_type' => 'string',
            'asset_types' => ['episode']
        ]);

        $content = "Asset Identifier,SERIES:Asset Identifier,Start Date,End Date,SERIES:Start Date,SERIES:End Date,SERIES:Title,Season\n";
        $content .= "Episode 1,Test Series," . now()->toDateString() . "," . now()->addMonths(3)->toDateString() . "," . now()->subDays(7)->toDateString() . "," . now()->addYear()->toDateString() . ",Test Series Title,1\n";
        $content .= "Episode 2,Test Series," . now()->addDays(14)->toDateString() . "," . now()->addMonths(4)->toDateString() . "," . now()->subDays(7)->toDateString() . "," . now()->addYear()->toDateString() . ",Test Series Title,1\n";
        $content .= "Episode 3,Test Series," . now()->addDays(28)->toDateString() . "," . now()->addMonths(5)->toDateString() . "," . now()->subDays(7)->toDateString() . "," . now()->addYear()->toDateString() . ",Test Series Title,2\n";

        $importPath = $this->createCsvFile($content);
        $import = $this->createImport($importPath);

        $this->runImport($import, 'series');

        $import->refresh();

        $this->assertEquals('good', $import->status);

        $series = Asset::where('title', 'Test Series')
            ->where('asset_type', 'series')
            ->first();
        $this->assertNotNull($series);

        $this->assertTrue($this->cycle->assets->contains($series->id));

        $episodes = Asset::where('asset_type', 'episode')
            ->where('parent_asset_id', $series->id)
            ->get();
        $this->assertCount(3, $episodes);

        $episodeTitles = $episodes->pluck('title')->toArray();
        $this->assertContains('Episode 1', $episodeTitles);
        $this->assertContains('Episode 2', $episodeTitles);
        $this->assertContains('Episode 3', $episodeTitles);

        $seriesTitleValue = FieldValue::where('asset_id', $series->id)
            ->where('field_id', $seriesTitleField->id)
            ->first();
        $this->assertNotNull($seriesTitleValue);
        $this->assertEquals('Test Series Title', $seriesTitleValue->getValue());

        $episode3 = $episodes->firstWhere('title', 'Episode 3');
        $seasonValue = FieldValue::where('asset_id', $episode3->id)
            ->where('field_id', $seasonField->id)
            ->first();
        $this->assertNotNull($seasonValue);
        $this->assertEquals('2', $seasonValue->getValue());
    }

    public function test_it_imports_movies()
    {
        $directorField = Field::create([
            'schema_id' => $this->schema->id,
            'name' => 'Director',
            'field_type' => 'string',
            'asset_types' => ['movie']
        ]);

        $content = "Asset Identifier,Start Date,End Date,Director\n";
        $content .= "Movie 1," . now()->toDateString() . "," . now()->addWeek()->toDateString() . ",Director 1\n";
        $content .= "Movie 2," . now()->toDateString() . "," . now()->addWeek()->toDateString() . ",Director 2\n";

        $importPath = $this->createCsvFile($content);
        $import = $this->createImport($importPath);

        $this->runImport($import, 'movie');

        $import->refresh();
        $this->assertEquals('good', $import->status);

        $movies = Asset::where('asset_type', 'movie')->get();
        $this->assertCount(2, $movies);

        $movie1 = $movies->firstWhere('title', 'Movie 1');
        $this->assertNotNull($movie1);

        $directorValue = FieldValue::where('asset_id', $movie1->id)
            ->where('field_id', $directorField->id)
            ->first();

        $this->assertNotNull($directorValue);
        $this->assertEquals('Director 1', $directorValue->getValue());

        $this->assertTrue($this->cycle->assets->contains($movie1->id));

        $movie2 = $movies->firstWhere('title', 'Movie 2');
        $this->assertNotNull($movie2);
        $this->assertTrue($this->cycle->assets->contains($movie2->id));
    }

    public function test_it_handles_errors_in_csv_format()
    {
        $content = "Asset Identifier\nMovie 1\n";
        $importPath = $this->createCsvFile($content);

        $import = $this->createImport($importPath);

        $this->runImport($import, 'series');

        $import->refresh();
        $this->assertEquals('errors', $import->status);
        $this->assertNotEmpty($import->errors);
    }

    public function test_it_handles_missing_asset_identifier()
    {
        $content = "Asset Identifier,SERIES:Asset Identifier,SERIES:Title\n";
        $content .= ",Test Series,Test Series Title\n";

        $importPath = $this->createCsvFile($content);
        $import = $this->createImport($importPath);

        $this->runImport($import, 'series');

        $import->refresh();
        $this->assertEquals('error', $import->status);
        $this->assertNotEmpty($import->errors);
    }

    public function test_it_handles_bad_dates()
    {
        $content = "Asset Identifier,SERIES:Asset Identifier,Start Date,End Date,SERIES:Start Date,SERIES:End Date,SERIES:Title\n";
        $content .= "Episode 1,Test Series,,,2023-01-05,2023-01-15,Test Series Title\n";

        $importPath = $this->createCsvFile($content);
        $import = $this->createImport($importPath);

        $this->runImport($import, 'series');

        $import->refresh();
        $this->assertEquals('error', $import->status);
        $this->assertNotEmpty($import->errors);
    }

    public function test_csv_imports_localizable_field_to_default_language(): void
    {
        $this->createStringField(true);
        $csv = <<<CSV
        Asset Identifier,Start Date,End Date,Title
        movie-id,2025-01-01,2025-01-21,"Movie Title"
        CSV;

        $importPath = $this->createCsvFile($csv);
        $import = $this->createImport($importPath);

        $this->runImport($import, 'movie');

        $import->refresh();

        $this->assertEquals('good', $import->status);

        $movie = Asset::where('title', 'movie-id')
            ->where('asset_type', AssetTypeEnum::Movie)
            ->first();

        $localizedValue = FieldValue::where('value', 'Movie Title')
            ->where('field_id', $this->stringField->id)
            ->where('language_id', $movie->getLatestCycle()->airline->getDefaultOrSystemLanguageId())
            ->first();

        $this->assertNotNull($localizedValue);
    }


    private function getFieldValue($asset, $fieldName)
    {
        $field = Field::where('name', $fieldName)->first();
        if (!$field) {
            return null;
        }

        $fieldValue =  FieldValue::where('asset_id', $asset->id)
            ->where('field_id', $field->id)
            ->first();

        return $fieldValue ? $fieldValue->getValue() : null;
    }


    private function createCsvFile($content)
    {
        $filename = 'test_import_' . uniqid() . '.csv';
        $path = 'imports/CSV/' . $filename;
        Storage::put($path, $content);
        return $path;
    }


    private function createImport($path)
    {
        return Import::create([
            'file_path' => $path,
            'file_name' => basename($path),
            'status' => 'pending',
            'errors' => [],
        ]);
    }

    private function runImport($import, $assetType)
    {
        ProcessImport::dispatchSync($import, $this->cycle->id, $assetType);
    }
}
