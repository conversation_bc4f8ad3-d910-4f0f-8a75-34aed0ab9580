<?php

namespace Tests\Feature;

use App\Models\Enums\IssueEnum;
use Tests\TestCase;

class IssueTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createStringField();
        $this->createStringFieldValue();
        $this->createValidator();
        $this->createValidatorFieldType();
        $this->createValidatorField();
    }


    public function test_issue_can_be_create(): void
    {
        $createData = [
            'description' => 'Test Deescription',
            'asset_id' => $this->asset->id,
            'field_value_id' => $this->stringFieldValue->id,
            'validator_field_id' => $this->validatorField->id,
            'type' => IssueEnum::Metadata->value,
            'is_custom' => 1,
            'cycle_id' => $this->cycle->id
        ];

        $response = $this->actingAs($this->user)->post('/issues', $createData);

        $response->assertCreated();
    }


    public function test_issue_description_can_not_have_tags(): void
    {
        $createData = [
            'description' => 'Test ?> Deescription wit</div> tags',
            'asset_id' => $this->asset->id,
            'field_value_id' => $this->stringFieldValue->id,
            'validator_field_id' => $this->validatorField->id,
            'type' => IssueEnum::Metadata->value,
            'cycle_id' => $this->cycle->id
        ];

        $response = $this->actingAs($this->user)->post('/issues', $createData);

        $response->assertSessionHasErrors(['description']);
    }


    public function test_issue_can_be_updated(): void
    {
        $issue = $this->createIssue();

        $updateData = [
            'description' => 'Updated Description'
        ]; 

        $response = $this->actingAs($this->user)->put("/issues/{$issue->uuid}", $updateData);   

        $response->assertAccepted();
    }


    public function test_issue_can_be_trashed(): void
    {
        $issue = $this->createIssue();

        $response = $this->actingAs($this->user)->delete("/issues/{$issue->uuid}");

        $response->assertOk();
        $this->assertTrue($issue->fresh()->trashed());
    }


    public function test_issue_can_be_viewed(): void
    {
        $issue = $this->createIssue();

        $response = $this->actingAs($this->user)->getJson("/issues/{$issue->uuid}");

        $response
            ->assertOk()
            ->assertJsonFragment(['uuid' => $issue->uuid]);
    }


    public function test_issue_can_be_listed(): void
    {
        $this->createIssue(); // using issue type Metadata
        $this->createIssue();
        $type = IssueEnum::Metadata->value;

        $response = $this->actingAs($this->user)->getJson("/issues?type={$type}&cycleID={$this->cycle->id}");

        $response
            ->assertOk()
            ->assertJsonFragment(['type' => $type])
            ->assertJsonCount(2, 'data');
    }
}
