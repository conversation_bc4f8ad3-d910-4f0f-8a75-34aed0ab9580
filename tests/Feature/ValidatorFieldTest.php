<?php

namespace Tests\Feature;

use App\Models\Asset;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Enums\ValidatorRulesEnum;
use App\Models\Enums\ValidatorSeverityEnum;
use App\Models\Enums\AssetStatus;
use App\Models\FieldValue;
use App\Services\ValidationAssetService;
use App\Services\ValidationFieldService;
use Tests\TestCase;
use App\Models\Issue;
use App\Models\Enums\IssueEnum;
use App\Rules\{MaxYearRule, MediaFileExistsRule};
use Illuminate\Support\Facades\Validator;

class ValidatorFieldTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createStringField();
        $this->createValidator();
        $this->createValidatorFieldType();
        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createDateField();
        $this->createDateFieldValue();
        $this->createStringField();
        $this->createStringFieldValue();
        $this->createCollection();
    }

    public function test_get_available_validators(): void
    {
        $type = 'string';
        $response = $this->actingAs($this->user)->getJson("/validator-fields?type=$type");

        $response->assertOk();
    }

    public function test_limited_user_cannot_get_available_validators(): void
    {
        $type = 'string';
        $response = $this->actingAs($this->cspEditorUser)->getJson("/validator-fields?type=$type");

        $response->assertForbidden();
    }

    public function test_can_create_validator_field_relation(): void
    {
        $createData = [
            'field_id' => $this->stringField->id,
            'validator_id' => $this->validator->id,
            'severity' => ValidatorSeverityEnum::Warning->value,
            'enabled' => true,
        ];

        $response = $this->actingAs($this->user)->post('/validator-fields', $createData);

        $response->assertCreated();
    }

    public function test_cannot_upsert_validator_field_relation_by_unprivileged_user(): void
    {
        $createData = [
            'field_id' => $this->stringField->id,
            'validator_id' => $this->validator->id,
            'severity' => ValidatorSeverityEnum::Warning->value,
            'enabled' => true,
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/validator-fields', $createData);

        $response->assertForbidden();
    }

    public function test_can_create_validator_field_relation_with_parameters(): void
    {
        $createData = [
            'field_id' => $this->stringField->id,
            'validator_id' => $this->validator->id,
            'parameters' => ['min' => 3],
            'severity' => ValidatorSeverityEnum::Warning->value,
            'enabled' => true,
        ];

        $response = $this->actingAs($this->user)->post('/validator-fields', $createData);

        $response->assertCreated();
    }

    public function test_can_validate_case_style()
    {
        $this->createValidatorField();
        $this->validator->update([
            'name' => ValidatorRulesEnum::CaseStyle->value,
        ]);
        $validationService = app(ValidationFieldService::class);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
            'parameters' => json_encode([
                'value' => 'title'
            ])
        ]);

        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);

        $this->assertNull($error);
        $this->stringFieldValue->update([
            'value' => 'Not title case',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
    }

    public function test_can_validate_formatting()
    {
        $this->createValidatorField();
        $this->validator->update([
            'name' => ValidatorRulesEnum::Formatting->value,
        ]);
        $validationService = app(ValidationFieldService::class);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);
        $this->stringFieldValue->update([
            'value' => 'Properly formatted synopsis for a fake movie. It has multiple sentences. This test should pass because it is well formatted.',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNull($error);
        $this->stringFieldValue->update([
            'value' => 'Double  space.',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
        $this->stringFieldValue->update([
            'value' => '  Leading space.',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
        $this->stringFieldValue->update([
            'value' => 'Trailing space.  ',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
        $this->stringFieldValue->update([
            'value' => 'not capitalized first letter.',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);

        $this->stringFieldValue->update([
            'value' => 'This synopsis starts well. But it starts to have issues. this sentence is not capitalized and has no proper ending',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
    }

    public function test_can_validate_asset_date_order()
    {
        $this->asset;
        $this->asset->start_date = '2024-10-01';
        $this->asset->end_date = '2023-10-01';
        $this->asset->save();

        // run asset validation service
        $validationService = app(ValidationAssetService::class);
        $error = $validationService->validateAssetFields($this->asset, $this->cycle, true);

        // check if content issue was created
        $contentIssues = Issue::where('type', IssueEnum::Metadata->value)->count();
        $this->assertEquals(1, $contentIssues);
    }

    public function test_validate_string_length()
    {
        $this->createValidatorField(json_encode([
            'value' => 3,
        ]));
        $this->validator->update([
            'name' => ValidatorRulesEnum::MaxStringLength->value,
        ]);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);
        $this->stringFieldValue->update([
            'value' => '1234',
        ]);
        $validationService = app(ValidationFieldService::class);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
        $this->stringFieldValue->update([
            'value' => '12',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNull($error);
    }

    public function test_vaidate_value_in_enum()
    {
        $this->createValidatorField(json_encode([
            'enum' => ['value1', 'value2', 'value3'],
        ]));
        $this->validator->update([
            'name' => ValidatorRulesEnum::Enumeration->value,
        ]);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);
        $this->stringFieldValue->update([
            'value' => 'value1',
        ]);
        $validationService = app(ValidationFieldService::class);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNull($error);
        $this->stringFieldValue->update([
            'value' => 'value4',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
    }

    public function test_validate_file_name_pattern()
    {
        $this->createValidatorField(json_encode([
            'type' => 'movie',
        ]));
        $this->validator->update([
            'name' => ValidatorRulesEnum::FileNamePattern->value,
        ]);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);
        $this->stringFieldValue->update([
            'value' => 'ABC1234_Example_Feature_Example.mp4',
        ]);
        $validationService = app(ValidationFieldService::class);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNull($error);
        $this->stringFieldValue->update([
            'value' => 'InvalidFileName.mp4',
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
    }

    public function test_check_max_count()
    {
        $this->createValidatorField(json_encode([
            'value' => 3,
        ]));
        $this->validator->update([
            'name' => ValidatorRulesEnum::MaxCount->value,
        ]);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);
        $this->stringFieldValue->update([
            'value' => ['value1', 'value2', 'value3'],
        ]);
        $validationService = app(ValidationFieldService::class);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNull($error);
        $this->stringFieldValue->update([
            'value' => ['value1', 'value2', 'value3', 'value4'],
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
    }

    public function test_cannot_create_movies_with_same_title_in_cycle()
    {
        $this->validator->update([
            'name' => ValidatorRulesEnum::UniqueInCycle->value,
        ]);
        $this->createValidatorField();
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);

        // movie assets in the same cycle
        $asset1 = Asset::create([
            'cycle_id' => $this->cycle->id,
            'start_date' => $startDate ?? now(),
            'end_date' => $endDate ?? now()->addMonth(),
            'asset_type' => AssetTypeEnum::Movie->value,
            'title' => 'Movie Asset 1',
        ]);

        $this->cycle->assets()->attach($asset1->id, [
            'status' => AssetStatus::New
        ]);

        $asset2 = Asset::create([
            'cycle_id' => $this->cycle->id,
            'start_date' => $startDate ?? now(),
            'end_date' => $endDate ?? now()->addMonth(),
            'asset_type' => AssetTypeEnum::Movie->value,
            'title' => 'Movie Asset 2',
        ]);

        $this->cycle->assets()->attach($asset2->id, [
            'status' => AssetStatus::New
        ]);

        // field values with validator attached to the field
        FieldValue::create([
            'field_id' => $this->stringField->id,
            'asset_id' => $asset1->id,
            'value' => 'Unique Movie Title',
        ]);

        $fieldValue = FieldValue::create([
            'field_id' => $this->stringField->id,
            'asset_id' => $asset2->id,
            'value' => 'Another field value'
        ]);

        // setting same title in a value triggers validation error
        $data = [
            'cycle_id' => $this->cycle->id,
            'value' => 'Unique Movie Title',
            'field_id' => $this->stringField->id
        ];

        $response = $this->actingAs($this->user)->putJson("/field-values/{$fieldValue->id}", $data);

        $response->assertUnprocessable()
            ->assertJsonFragment(['success' => false]);
    }

    public function test_cannot_create_episode_with_same_title_in_tv_series()
    {
        $tvSeries = Asset::create([
            'cycle_id' => $this->cycle->id,
            'asset_type' => AssetTypeEnum::Series->value,
            'title' => 'Amazing TV Series',
            'start_date' => now(),
            'end_date' => now()->addMonth(),
        ]);

        $this->validator->update([
            'name' => ValidatorRulesEnum::UniqueInTVSeries->value,
        ]);
        $this->createValidatorField();
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);

        // episodes in the same tv series
        $episode1 = Asset::create([
            'cycle_id' => $this->cycle->id,
            'start_date' => $startDate ?? now(),
            'end_date' => $endDate ?? now()->addMonth(),
            'asset_type' => AssetTypeEnum::Episode->value,
            'title' => 'TV Series Asset 1',
            'parent_asset_id' => $tvSeries->id
        ]);

        $this->cycle->assets()->attach($episode1->id, [
            'status' => AssetStatus::New
        ]);

        $episode2 = Asset::create([
            'cycle_id' => $this->cycle->id,
            'start_date' => $startDate ?? now(),
            'end_date' => $endDate ?? now()->addMonth(),
            'asset_type' => AssetTypeEnum::Episode->value,
            'title' => 'TV Series Asset 2',
            'parent_asset_id' => $tvSeries->id
        ]);

        $this->cycle->assets()->attach($episode2->id, [
            'status' => AssetStatus::New
        ]);

        // field values with validator attached to the field
        $title1 = FieldValue::create([
            'field_id' => $this->stringField->id,
            'asset_id' => $episode1->id,
            'value' => 'Unique TV Series Title',
        ]);

        $fieldValue = FieldValue::create([
            'field_id' => $this->stringField->id,
            'asset_id' => $episode2->id,
            'value' => 'Another TV Series title value'
        ]);

        // setting different title in a value does Not trigger a validation error
        $data = [
            'cycle_id' => $this->cycle->id,
            'value' => 'Another TV Series with differenttitle value',
            'field_id' => $this->stringField->id
        ];
        $response = $this->actingAs($this->user)->putJson("/field-values/{$fieldValue->id}", $data);
        $response->assertAccepted();

        // setting same title in a value triggers validation error
        $data = [
            'cycle_id' => $this->cycle->id,
            'value' => 'Unique TV Series Title',
            'field_id' => $this->stringField->id
        ];

        $response = $this->actingAs($this->user)->putJson("/field-values/{$fieldValue->id}", $data);

        $response->assertUnprocessable()
            ->assertJsonFragment(['success' => false]);
    }

    public function test_value_is_positive_number()
    {
        $this->createValidatorField();
        $this->validator->update([
            'name' => ValidatorRulesEnum::PositiveNumber->value,
        ]);
        $this->validatorField->update([
            'field_id' => $this->stringField->id,
            'enabled' => true,
        ]);
        $this->stringFieldValue->update([
            'value' => 1234,
        ]);
        $validationService = app(ValidationFieldService::class);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNull($error);

        $this->stringFieldValue->update([
            'value' => 0,
        ]);
        $validationService = app(ValidationFieldService::class);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);

        $this->stringFieldValue->update([
            'value' => -1234,
        ]);
        $error = $validationService->runValidation($this->stringFieldValue, $this->cycle->id, true);
        $this->assertNotNull($error);
    }

    public function test_validates_max_year()
    {
        $params = (object) [
            'years' => 1
        ];

        $rule = new MaxYearRule($params);

        $validator = Validator::make(['value' => now()], ['value' => $rule]);
        $this->assertTrue($validator->passes());
        $validator = Validator::make(['value' => now()->addYears(2)], ['value' => $rule]);
        $this->assertFalse($validator->passes());

        $validator = Validator::make(['value' => 'invalid date'], ['value' => $rule]);
        $this->assertFalse($validator->passes());
    }

    public function test_media_file_exists_rule()
    {
        $params = (object) [
            'cycle' => $this->cycle
        ];
        $rule = new MediaFileExistsRule($params);
        $validator = Validator::make(['value' => 'TEST'], ['value' => $rule]);
        $this->assertTrue($validator->passes());
    }
}
