<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Language;

class FieldLabelTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createStringField();
        $this->createFieldLabel();
    }

    public function test_can_read_field_labels(): void
    {
        $response = $this->actingAs($this->user)->getJson('/field-labels');
        $response->assertOk();

        $response->assertJsonCount(1);

        $response = $this->actingAs($this->user)->getJson("/field-labels/{$this->fieldLabel->id}");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_field_labels(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson('/field-labels');
        $response->assertForbidden();

        $response = $this->actingAs($this->cspEditorUser)->get<PERSON>son("/field-labels/{$this->fieldLabel->id}");
        $response->assertForbidden();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_field_label(): void
    {
        $createData = [
            "field_id" => $this->stringField->id,
            "language_id" => Language::getDefaultLanguageId(),
            "value" => "new label",
        ];

        $response = $this->actingAs($this->user)->postJson("/field-labels", $createData);

        $response->assertCreated();
    }

    public function test_cannot_create_field_label_by_unprivileged_user(): void
    {
        $createData = [
            "field_id" => $this->stringField->id,
            "language_id" => Language::getDefaultLanguageId(),
            "value" => "new label",
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson("/field-labels", $createData);

        $response->assertForbidden();
    }

    public function test_can_update_field_label(): void
    {
        $updateData = [
            'value' => 'update label',
        ];

        $response = $this->actingAs($this->user)->putJson("/field-labels/{$this->fieldLabel->id}", $updateData);

        $response->assertOk();
    }

    public function test_cannot_update_field_label_by_unprivileged_user(): void
    {
        $updateData = [
            'value' => 'update label',
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("/field-labels/{$this->fieldLabel->id}", $updateData);

        $response->assertForbidden();
    }
}
