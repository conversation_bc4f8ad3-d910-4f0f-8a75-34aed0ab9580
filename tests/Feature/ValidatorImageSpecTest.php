<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\Validator;
use Tests\TestCase;
use App\Rules\ImageSpecRule;

class ValidatorImageSpecTest extends TestCase
{
    private ImageSpecRule $rule;
    private object $defaultParams;

    public function setUp(): void
    {
        parent::setUp();

        $this->defaultParams = (object) [
            'width' => 200,
            'height' => 300,
        ];
        $this->rule = new ImageSpecRule($this->defaultParams);
    }

    private function validateAndAssert($path, $shouldPass, $expectedMsg = null)
    {
        $validator = Validator::make(['value' => $path], ['value' => $this->rule]);
        if ($shouldPass) {
            $this->assertFalse($validator->fails());
        } else {
            $this->assertTrue($validator->fails());
            if ($expectedMsg) {
                $this->assertStringContainsString(
                    $expectedMsg,
                    implode(' ', $validator->errors()->all())
                );
            }
        }
    }

    public function test_it_validates_dimensions()
    {
        $badPath = resource_path('images/202x302.jpg');
        $this->validateAndAssert(
            $badPath,
            false,
            "dimensions must be {$this->defaultParams->width}x{$this->defaultParams->height}"
        );

        $goodPath = resource_path('images/200x300.jpg');
        $this->validateAndAssert($goodPath, true);
    }

    public function test_it_validates_type()
    {
        $goodPath = resource_path('images/200x300.jpg');
        $this->validateAndAssert($goodPath, true);

        $goodPath = resource_path('images/200x300.png');
        $this->validateAndAssert($goodPath, false, 'must be a JPG file');

    }

    public function test_validates_embedded_profiles()
    {
        $withProfilePath = resource_path('images/200x300_profile.jpg');
        $this->validateAndAssert($withProfilePath, false, 'must not have an embedded color profile');

        $noProfilePath = resource_path('images/200x300.jpg');
        $this->validateAndAssert($noProfilePath, true);
    }

    public function test_validates_colorspace()
    {
        $goodPath = resource_path('images/200x300.jpg');
        $this->validateAndAssert($goodPath, true);

        $badPath = resource_path('images/200x300_cmyk.jpg');
        $this->validateAndAssert($badPath, false, 'The image must be in sRGB or RGB color space.');

    }

    public function test_validates_mozjpeg_compression_quality()
    {
        $goodPath = resource_path('images/200x300_mozjpeg.jpg');
        $this->validateAndAssert($goodPath, true);

        $mozJpegLowQPath = resource_path('images/200x300_mozjpeg_bad.jpg');
        $this->validateAndAssert($mozJpegLowQPath, true);
    }
}
