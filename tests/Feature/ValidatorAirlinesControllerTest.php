<?php

namespace Tests\Feature;

use Tests\TestCase;

class ValidatorAirlinesControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createValidator();
        $this->createValidatorAirlineParam();
    }

    public function test_can_read_available_validators_for_airline_config(): void
    {
        $response = $this->actingAs($this->user)->get<PERSON>son('/validator-airline-config');
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_available_validators_for_airline_config(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson('/validator-airline-config');
        $response->assertForbidden();
    }

    public function test_can_create_validator_for_airline_config(): void
    {
        $createData = [
            'validator_id' => $this->validator->id,
            'airline_id' => $this->airline->id,
            'parameters' => ['value' => 1],
        ];

        $response = $this->actingAs($this->user)->postJson('/validator-airline-config', $createData);
        $response->assertCreated();
    }

    public function test_cannot_create_validator_for_airline_config_by_unprivileged_user(): void
    {
        $createData = [
            'validator_id' => $this->validator->id,
            'airline_id' => $this->airline->id,
            'parameters' => ['value' => 1],
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/validator-airline-config', $createData);
        $response->assertForbidden();
    }
}
