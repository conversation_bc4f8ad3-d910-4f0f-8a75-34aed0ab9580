<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Cycle;
use Tests\TestCase;

class CycleMigrationTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
    }

    public function test_empty_cycle_can_be_created(): void
    {
        $response = $this->actingAs($this->user)->postJson('/cycles', ['airline_id' => $this->airline->id]);

        $response->assertCreated();
        $response->assertJson([
            'success' => true,
            'message' => 'Cycle created successfully',
        ]);
    }

    public function test_new_cycle_with_categories_can_be_created(): void
    {
        $this->createCycle();
        $this->createRootCategory(); // 1
        $this->createCategory(); // 2

        $this->assertEquals($this->cycle->categories()->count(), 2); // original cycle has 2 categpories

        $response = $this->actingAs($this->user)->postJson('/cycles', ['airline_id' => $this->airline->id]);
        $response->assertCreated();

        $migratedCycle = Cycle::where('id', $response->json('cycle.id'))->first();
        $this->assertEquals($migratedCycle->categories()->count(), 2);  // migrated cycle has 2 categories 
    }

    public function test_cycle_with_category_and_assets_can_be_created(): void
    {
        $this->createCycle();
        $this->createRootCategory(); // root category 1
        $this->createCategory(); // asset category 1
        $this->rootCategory->addChildCategories([$this->category->id]); // category closure 1
        $this->createCategory(); // asset category 2
        $this->rootCategory->addChildCategories([$this->category->id]); // category closure 2

        $this->createAsset();
        $this->category->addAssets([$this->asset->id]);

        $this->assertEquals($this->cycle->categories()->count(), 3);
        $this->assertEquals($this->rootCategory->childCategories()->count(), 2);

        // migrate cycle
        $response = $this->actingAs($this->user)->postJson('/cycles', ['airline_id' => $this->airline->id]);
        $response->assertCreated();

        $migratedCycle = Cycle::where('id', $response->json('cycle.id'))->first();
        $this->assertEquals($migratedCycle->categories()->count(), 3);
        $this->assertEquals($migratedCycle->categories()->where('is_root', true)->count(), 1); // one root category

        $migratedAssetCategories = Category::whereHas('parentCategories')->where('cycle_id', $migratedCycle->id)->count();
        $this->assertEquals($migratedAssetCategories, 2); // 2 asset categories migrated 
    }
}
