<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Airline;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;

class OrganizationTest extends TestCase
{
    use RefreshDatabase;

    protected $organization;

    public function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
    }

    public function test_org_can_be_created()
    {
        $response = $this->actingAs($this->user)->post('/organizations', [
            'name' => 'Test Organization',
            'owner_id' => 1
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('organizations', [
            'name' => 'Test Organization',
        ]);
    }

    public function test_org_cannot_be_created_by_unprivileged_user()
    {
        $response = $this->actingAs($this->cspEditorUser)->postJson('/organizations', [
            'name' => 'Test Organization',
            'owner_id' => 1
        ]);

        $response->assertForbidden();
        $this->assertDatabaseMissing('organizations', [
            'name' => 'Test Organization',
        ]);
    }

    public function test_org_can_be_updated()
    {
        $response = $this->actingAs($this->user)->put("/organizations/{$this->organization->id}", [
            'name' => 'Updated Organization',
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('organizations', [
            'id' => $this->organization->id,
            'name' => 'Updated Organization',
        ]);
    }

    public function test_org_cannot_be_updated_by_unprivileged_user()
    {
        $response = $this->actingAs($this->cspEditorUser)->putJson("/organizations/{$this->organization->id}", [
            'name' => 'Updated Organization',
        ]);

        $response->assertForbidden();
        $this->assertDatabaseMissing('organizations', [
            'id' => $this->organization->id,
            'name' => 'Updated Organization',
        ]);
    }

    public function test_org_can_be_deleted()
    {
        $response = $this->actingAs($this->user)->delete("/organizations/{$this->organization->id}");

        $response->assertStatus(204);
        $this->assertDatabaseMissing('organizations', [
            'id' => $this->organization->id,
            'deleted_at' => null
        ]);
    }

    public function test_org_cannot_be_deleted_by_unprivileged_user()
    {
        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/organizations/{$this->organization->id}");

        $response->assertForbidden();
        $this->assertDatabaseHas('organizations', [
            'id' => $this->organization->id,
            'deleted_at' => null
        ]);
    }

    public function test_org_can_have_users()
    {
        $users = User::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);

        $this->assertEquals(3, $this->organization->users()->count());

        $response = $this->actingAs($this->user)->get("/organizations/{$this->organization->id}");

        $response->assertStatus(200);
        $response->assertJsonCount(3, 'users');
        $response->assertJsonFragment([
            'id' => $users[0]->id,
            'name' => $users[0]->name
        ]);
    }

    public function test_org_can_have_airlines()
    {
        $airlines = Airline::factory()->count(2)->create();

        $this->organization->airlines()->attach($airlines->pluck('id'));

        $this->assertEquals(2, $this->organization->airlines()->count());

        $response = $this->actingAs($this->user)->get("/organizations/{$this->organization->id}");

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'airlines');
        $response->assertJsonFragment([
            'id' => $airlines[0]->id,
            'name' => $airlines[0]->name
        ]);

        $this->assertEquals($airlines[0]->organizations[0]->id, $this->organization->id);
        $this->assertEquals($airlines[1]->organizations[0]->id, $this->organization->id);
    }

    public function test_org_can_add_airlines_during_update()
    {
        $airlines = Airline::factory()->count(3)->create();

        $response = $this->actingAs($this->user)->put("/organizations/{$this->organization->id}", [
            'name' => 'Updated Organization',
            'airlines' => $airlines->pluck('id')->toArray()
        ]);

        $response->assertStatus(200);

        $this->assertEquals(3, $this->organization->airlines()->count());

        foreach ($airlines as $airline) {
            $this->assertDatabaseHas('airline_organization', [
                'airline_id' => $airline->id,
                'organization_id' => $this->organization->id
            ]);
        }
    }

    public function test_org_can_remove_airlines_during_update()
    {
        $airlines = Airline::factory()->count(3)->create();
        $this->organization->airlines()->attach($airlines->pluck('id'));

        $this->assertEquals(3, $this->organization->airlines()->count());

        $response = $this->actingAs($this->user)->put("/organizations/{$this->organization->id}", [
            'name' => 'Updated Organization',
            'airlines' => [$airlines[0]->id]
        ]);

        $response->assertStatus(200);
        $this->organization->refresh();
        $this->assertEquals(1, $this->organization->airlines()->count());

        $this->assertDatabaseHas('airline_organization', [
            'airline_id' => $airlines[0]->id,
            'organization_id' => $this->organization->id
        ]);

        $this->assertDatabaseMissing('airline_organization', [
            'airline_id' => $airlines[1]->id,
            'organization_id' => $this->organization->id
        ]);
        $this->assertDatabaseMissing('airline_organization', [
            'airline_id' => $airlines[2]->id,
            'organization_id' => $this->organization->id
        ]);
    }

    public static function user_type_org_provider()
    {
        return [
            ['admin', 3], // 3 = TestCase org + org created in setUp + organization2 inside test
            ['cspEditor', 1],
        ];
    }

    #[DataProvider('user_type_org_provider')]
    public function test_user_can_search_cycles_for_all_airlines(string $type, int $num_results)
    {
        $user = $type === 'admin' ? $this->user : $this->cspEditorUser;

        $this->createOrganization2();
        $this->actingAs($user);

        // test the index endpoint
        $response = $this->getJson('/organizations');

        $response->assertJsonCount($num_results);

        // test the id endpoint for the user's org
        $response = $this->getJson("/organizations/{$user->organization_id}");
        $response->assertOk();

        // test the id endpoint for a different org
        $response = $this->getJson("/organizations/{$this->organization2->id}");

        if ($type === 'admin') {
            $response->assertOk();
        } else {
            $response->assertForbidden();
        }
    }
}
