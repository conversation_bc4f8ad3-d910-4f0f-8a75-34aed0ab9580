<?php

namespace Tests\Feature;

use App\Models\CategoryValue;
use App\Models\Language;
use Tests\TestCase;

class AtLeastoneCategoryRuleTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
        $this->createRootCategory();
        $this->createCategory();
        $this->rootCategory->addChildCategories([$this->category->id]);
    }


    public function test_validation_passes_when_category_assigned(): void
    {
        $this->createAsset();
        $data = [
            'ids' => [$this->asset->id],
            'type' => 'assets'
        ];

        CategoryValue::create([
            'category_id' => $this->category->id,
            'language_id' => Language::getDefaultLanguageId(),
            'value' => 'Category Title'
        ]);

        $response = $this->actingAs($this->user)->postJson("/categories/{$this->category->id}/add-items", $data);

        $response->assertOk();
    }


    public function test_issue_created_when_validation_failed(): void
    {
        $data = [
            'cycleID' => $this->cycle->id,
            'ids' => [$this->asset->id],
            'type' => 'assets'
        ];
        $response = $this->actingAs($this->user)->delete("/categories/{$this->category->id}/remove-items", $data);
        $response->assertOk();

        $this->assertDatabaseHas('issues', ['validator_group' => 'category']);
    }
}
