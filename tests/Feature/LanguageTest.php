<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Language;

use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LanguageTest extends TestCase
{

    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();

    }

    public function test_languages_are_seeded_correctly(): void
    {

        // Run the database seeds
        $this->artisan('db:seed', ['--class' => 'LanguageTableSeeder']);

        // Check that languages are seeded
        $languageCount = Language::count();
        $this->assertGreaterThan(0, $languageCount, 'No languages were seeded');

        // Verify some specific languages are present
        $expectedLanguages = [
            'ENG' => [
                'bcp_47_code' => 'en',
                'iso_639_2_t_code' => 'eng',
                'eng_description' => 'English',
                'local_description' => 'English'
            ],
            'FRE' => [
                'bcp_47_code' => 'fr',
                'iso_639_2_t_code' => 'fra',
                'eng_description' => 'French',
                'local_description' => 'Français'
            ]
        ];

        foreach ($expectedLanguages as $ifeCode => $details) {
            $language = Language::where('ife_code', $ifeCode)->first();

            $this->assertNotNull($language, "Language with IFE code $ifeCode not found");

            $this->assertEquals(
                $details['bcp_47_code'],
                $language->bcp_47_code,
                "BCP-47 code mismatch for $ifeCode"
            );
            $this->assertEquals(
                $details['iso_639_2_t_code'],
                $language->iso_639_2_t_code,
                "ISO 639-2/T code mismatch for $ifeCode"
            );
            $this->assertEquals(
                $details['eng_description'],
                $language->eng_description,
                "English description mismatch for $ifeCode"
            );
            $this->assertEquals(
                $details['local_description'],
                $language->local_description,
                "Local description mismatch for $ifeCode"
            );
        }

        // Verify unique constraints
        $uniqueIfeCodeCount = Language::select('ife_code')->distinct()->count();
        $uniqueBcp47CodeCount = Language::select('bcp_47_code')->distinct()->count();

        $this->assertEquals(
            $languageCount,
            $uniqueIfeCodeCount,
            'Duplicate IFE codes found'
        );
        $this->assertEquals(
            $languageCount,
            $uniqueBcp47CodeCount,
            'Duplicate BCP-47 codes found'
        );
    }
    
    public function test_it_can_create_a_language()
    {
        $createData = [
            'ife_code' => 'ENG',
            'bcp_47_code' => 'en',
            'iso_639_2_t_code' => 'eng',
            'eng_description' => 'English',
            'local_description' => 'English'
        ];
        $response = $this->actingAs($this->user)->post('/languages', $createData);

        $response->assertCreated();
    }

    public function test_it_prevents_duplicate_ife_codes()
    {
        // Create first language
        $createData = [
            'ife_code' => 'ENG',
            'bcp_47_code' => 'en',
            'iso_639_2_t_code' => 'eng',
            'eng_description' => 'English',
            'local_description' => 'English'
        ];
        $response = $this->actingAs($this->user)->post('/languages', $createData);

        $response->assertCreated();

        // Try to create another with same IFE code
        $createData = [
            'ife_code' => 'ENG',
            'bcp_47_code' => 'en',
            'iso_639_2_t_code' => 'eng',
            'eng_description' => 'English',
            'local_description' => 'English'
        ];
        $response = $this->actingAs($this->user)->post('/languages', $createData);

        $response->assertFound();
    }

    public function test_it_can_get_default_language_id()
    {
        // First call should create the default language
        $defaultLanguageId = Language::getDefaultLanguageId();

        $this->assertIsInt($defaultLanguageId);

        // Verify the language exists in the database
        $this->assertDatabaseHas('languages', [
            'id' => $defaultLanguageId,
            'ife_code' => 'ENG'
        ]);

        // Second call should return the same ID
        $secondCallId = Language::getDefaultLanguageId();
        $this->assertEquals($defaultLanguageId, $secondCallId);
    }

    public function test_it_does_not_create_duplicate_default_language()
    {
        // First call creates the language
        $firstId = Language::getDefaultLanguageId();

        // Simulate multiple calls
        $secondId = Language::getDefaultLanguageId();
        $thirdId = Language::getDefaultLanguageId();

        // All calls should return the same ID
        $this->assertEquals($firstId, $secondId);
        $this->assertEquals($firstId, $thirdId);

        // Verify only one record exists
        $this->assertDatabaseCount('languages', 1);
    }

    public function test_it_limits_field_lengths()
    {
        $createData = [
            'ife_code' => 'TOOLONGIFECODE', // Exceeds max length
            'bcp_47_code' => 'en',
            'iso_639_2_t_code' => 'eng',
            'eng_description' => 'English',
            'local_description' => 'English'
        ];

        $response = $this->actingAs($this->user)->post('/languages', $createData);

        $response->assertSessionHasErrors(['ife_code']);
    }
}
