<?php

namespace Tests\Feature;

use Tests\TestCase;

class AssetFieldsTest extends TestCase
{
    public function setUp() : void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
    }

    public function test_it_gets_fields_for_this_asset_type(): void
    {
        // Given
        $this->createDateField();
        $this->createStringField();
        
        // When
        $response = $this->actingAs($this->user)->get('/field-values?assetID=' . $this->asset->id . '&cycleID=' . $this->cycle->id);

        // Then
        $response->assertJsonFragment([
            'id' => $this->dateField->id
        ], [
            'id' => $this->stringField->id
        ]);
    }

    public function test_excludes_fields_for_this_asset_type(): void
    {
        // Given
        $this->createSeriesField();
        
        // When
        $response = $this->actingAs($this->user)->get('/field-values?assetID=' . $this->asset->id . '&cycleID=' . $this->cycle->id);

        // Then
        $response->assertJsonMissing([
            'id' => $this->seriesSynopsisField->id
        ]);
    }
}
