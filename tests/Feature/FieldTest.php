<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Enums\FieldEnum;

class FieldTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createStringField();
    }

    public function test_can_read_fields(): void
    {
        $response = $this->actingAs($this->user)->getJson('/fields');
        $response->assertOk();

        $response->assertJsonCount(1);

        $response = $this->actingAs($this->user)->getJson("/fields/{$this->stringField->id}");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_fields(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson('/fields');
        $response->assertForbidden();

        $response = $this->actingAs($this->cspEditorUser)->getJson("/fields/{$this->stringField->id}");
        $response->assertForbidden();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_field(): void
    {
        $createData = [
            'name' => 'TESTING',
            'field_type' => 'string',
            'schema_id' => $this->schema->id
        ];

        $response = $this->actingAs($this->user)->post('/fields', $createData);

        $response->assertCreated();
    }

    public function test_cannot_create_field_by_unprivileged_user(): void
    {
        $createData = [
            'name' => 'TESTING',
            'field_type' => 'string',
            'schema_id' => $this->schema->id
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/fields', $createData);

        $response->assertForbidden();
    }

    public function test_can_create_dropdown_field(): void
    {
        $createData = [
            'name' => 'Genre',
            'field_type' =>  FieldEnum::Dropdown->value,
            'schema_id' => $this->schema->id
        ];

        $response = $this->actingAs($this->user)->post('/fields', $createData);

        $response->assertCreated();
    }

    public function test_can_update_field(): void
    {
        $updateData = [
            'schema_id' => $this->schema->id,
            'field_id' => $this->stringField->id,
            'field_type' => 'string',
            'name' => 'new-name',
        ];

        $response = $this->actingAs($this->user)->postJson('/fields', $updateData);

        $response->assertCreated();
    }

    public function test_cannot_update_field_by_unprivileged_user(): void
    {
        $updateData = [
            'schema_id' => $this->schema->id,
            'field_id' => $this->stringField->id,
            'field_type' => 'string',
            'name' => 'new-name',
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/fields', $updateData);

        $response->assertForbidden();
    }

    public function test_can_delete_field(): void
    {
        $response = $this->actingAs($this->user)->deleteJson("/fields/{$this->stringField->id}");

        $response->assertNoContent();
    }

    public function test_cannot_delete_field_by_unprivileged_user(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/fields/{$this->stringField->id}");

        $response->assertForbidden();
    }
}
