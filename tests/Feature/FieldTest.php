<?php

namespace Tests\Feature;

use App\Models\Enums\AssetTypeEnum;
use Tests\TestCase;
use App\Models\Enums\FieldEnum;
use App\Models\Field;
use App\Models\Schema;
use PHPUnit\Framework\Attributes\DataProvider;

class FieldTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createStringField();
    }

    public function test_can_read_fields(): void
    {
        $response = $this->actingAs($this->user)->getJson('/fields');
        $response->assertOk();

        $response->assertJsonCount(1);

        $response = $this->actingAs($this->user)->getJson("/fields/{$this->stringField->id}");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_fields(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson('/fields');
        $response->assertForbidden();

        $response = $this->actingAs($this->cspEditorUser)->getJson("/fields/{$this->stringField->id}");
        $response->assertForbidden();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_field(): void
    {
        $createData = [
            'name' => 'TESTING',
            'field_type' => 'string',
            'schema_id' => $this->schema->id
        ];

        $response = $this->actingAs($this->user)->post('/fields', $createData);

        $response->assertCreated();
    }

    public function test_cannot_create_field_by_unprivileged_user(): void
    {
        $createData = [
            'name' => 'TESTING',
            'field_type' => 'string',
            'schema_id' => $this->schema->id
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/fields', $createData);

        $response->assertForbidden();
    }

    public function test_can_create_dropdown_field(): void
    {
        $createData = [
            'name' => 'Genre',
            'field_type' =>  FieldEnum::Dropdown->value,
            'schema_id' => $this->schema->id
        ];

        $response = $this->actingAs($this->user)->post('/fields', $createData);

        $response->assertCreated();
    }

    public function test_can_update_field(): void
    {
        $updateData = [
            'schema_id' => $this->schema->id,
            'field_id' => $this->stringField->id,
            'field_type' => 'string',
            'name' => 'new-name',
        ];

        $response = $this->actingAs($this->user)->postJson('/fields', $updateData);

        $response->assertCreated();
    }

    public function test_cannot_update_field_by_unprivileged_user(): void
    {
        $updateData = [
            'schema_id' => $this->schema->id,
            'field_id' => $this->stringField->id,
            'field_type' => 'string',
            'name' => 'new-name',
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/fields', $updateData);

        $response->assertForbidden();
    }

    public function test_can_delete_field(): void
    {
        $response = $this->actingAs($this->user)->deleteJson("/fields/{$this->stringField->id}");

        $response->assertNoContent();
    }

    public function test_cannot_delete_field_by_unprivileged_user(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/fields/{$this->stringField->id}");

        $response->assertForbidden();
    }

    public static function api_key_field_provider()
    {
        return [
            ['same-schema', AssetTypeEnum::Movie->value, AssetTypeEnum::Movie->value, false],
            ['different-schema', AssetTypeEnum::Movie->value, AssetTypeEnum::Movie->value, true],
            ['same-schema', AssetTypeEnum::Movie->value, [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value], false],
            ['different-schema', AssetTypeEnum::Movie->value, [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value], true],
            ['same-schema', [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value], AssetTypeEnum::Movie->value, false],
            ['different-schema', [AssetTypeEnum::Movie->value, AssetTypeEnum::Series->value], AssetTypeEnum::Movie->value, true],
            ['same-schema', AssetTypeEnum::Movie->value, null, false],
            ['same-schema', AssetTypeEnum::Movie->value, [], false],
            ['different-schema', AssetTypeEnum::Movie->value, null, true],
            ['different-schema', AssetTypeEnum::Movie->value, [], true],
            ['same-schema', null, AssetTypeEnum::Movie->value, false],
            ['same-schema', [], AssetTypeEnum::Movie->value, false],
            ['different-schema', null, AssetTypeEnum::Movie->value, true],
            ['different-schema', [], AssetTypeEnum::Movie->value, true],
            ['same-schema', AssetTypeEnum::Series->value, AssetTypeEnum::Movie->value, true],
            ['different-schema', AssetTypeEnum::Series->value, AssetTypeEnum::Movie->value, true],
            ['same-schema', AssetTypeEnum::Series->value, [AssetTypeEnum::Movie->value, AssetTypeEnum::Music], true],
            ['different-schema', AssetTypeEnum::Series->value, [AssetTypeEnum::Movie->value, AssetTypeEnum::Music], true],
            ['same-schema', [AssetTypeEnum::Movie->value, AssetTypeEnum::Music], AssetTypeEnum::Series->value, true],
            ['different-schema', [AssetTypeEnum::Movie->value, AssetTypeEnum::Music], AssetTypeEnum::Series->value, true],
        ];
    }

    #[DataProvider('api_key_field_provider')]
    public function test_set_is_api_key_field_for_asset_that_already_has_a_key_field(string $schemaType, array|string|null $existingKeyFieldAssetTypes, array|string|null $assetTypes, bool $isAllowed)
    {
        if (!is_array($existingKeyFieldAssetTypes) && $existingKeyFieldAssetTypes !== null) {
            $existingKeyFieldAssetTypes = [$existingKeyFieldAssetTypes];
        }

        if (!is_array($assetTypes)) {
            $assetTypes = [$assetTypes];
        }

        $schemaIdForNewField = $schemaType === 'same-schema'
            ? $this->schema->id
            : (Schema::create(['is_root' => true, 'name' => 'Other Schema', 'version_number' => 1]))->id;

        $this->createField();

        // create the per-existing key field
        Field::create([
            'name' => 'Field_Api_Key_Test',
            'field_type' => FieldEnum::String,
            'schema_id' => $schemaIdForNewField,
            'is_external_api_key_field' => true,
            'asset_types' => $existingKeyFieldAssetTypes,
        ]);

        $this->field->asset_types = $assetTypes;
        $this->field->save();

        $updateData = [
            'schema_id' => $this->schema->id,
            'field_id' => $this->field->id,
            'name' => $this->field->name,
            'field_type' => $this->field->field_type,
            'is_external_api_key_field' => true
        ];
        $response = $this->actingAs($this->user)->putJson("/fields/{$this->field->id}", $updateData);

        if ($isAllowed) {
            $response->assertOk();
        } else {
            $response->assertBadRequest();
        }
    }
}
