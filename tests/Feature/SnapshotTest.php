<?php

use App\Jobs\GenerateSnapshot;
use Tests\TestCase;
use Illuminate\Support\Facades\Queue;

class SnapshotTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();

    }

    public function test_can_create_snapshot()
    {
        Queue::fake();

        $response = $this->actingAs($this->user)->postJson(
            '/snapshots',
            ['cycle_id' => $this->cycle->id]
        );

        Queue::assertPushed(GenerateSnapshot::class);
        $response->assertSuccessful();
    }

    public function test_cannot_create_snapshot()
    {
        $response = $this->actingAs($this->user)->postJson('/snapshots', ['cycle_id' => 'ABCDEFG']);
        $response->assertNotFound();
    }

    public function test_cannot_create_snapshot_unauthorized()
    {
        $response = $this->postJson('/snapshots', ['cycle_id' => $this->cycle->id]);
        $response->assertUnauthorized();
    }

    public function test_can_list_snapshots()
    {
        $response = $this->actingAs($this->user)->getJson('/snapshots');
        $response->assertSuccessful();
    }

    public function test_cannot_list_snapshots()
    {

        $response = $this->getJson('/snapshots');
        $response->assertUnauthorized();
    }

    public function test_snapshot_command()
    {

        $this->artisan('app:get-snapshot', ['cycle_id' => $this->cycle->id])
            ->expectsOutputToContain('Snapshot has been saved')
            ->assertExitCode(0);
    }

    public function test_snapshot_empty_command()
    {

        $this->artisan('app:get-snapshot')
            ->expectsOutput('Nothing to Process')
            ->assertExitCode(2);
    }

}
