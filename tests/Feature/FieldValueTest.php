<?php

namespace Tests\Feature;

use App\Models\Collection;
use App\Models\CollectionValue;
use App\Models\Enums\AssetStatus;
use App\Models\Enums\AssetTypeEnum;
use App\Models\Field;
use Tests\TestCase;
use App\Models\Enums\FieldEnum;
use App\Models\FieldValue;
use App\Models\Language;
use App\Services\ValidationFieldService;
use App\Utilities\CycleAssetManager;
use PHPUnit\Framework\Attributes\DataProvider;

class FieldValueTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createStringField();
        $this->createDateField();
        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
    }


    /**
     * A basic feature test example.
     */
    public function test_can_create_asset_field_value_for_title(): void
    {
        $createData = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringField->id,
            'value' => 'Anora'
        ];

        $response = $this->actingAs($this->user)->post('/field-values', $createData);

        $response->assertCreated();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_asset_field_value_for_release_date(): void
    {
        $createData = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->dateField->id,
            'value' => now()->addMonth()
        ];

        $response = $this->actingAs($this->user)->post('/field-values', $createData);

        $response->assertCreated();
    }

    public function test_can_add_dropdown_field_collection_item_ids_to_a_field_value(): void
    {

        // create a dropdown field
        $field = Field::create([
            'name' => 'Genre',
            'field_type' =>  FieldEnum::Dropdown,
            'schema_id' => $this->schema->id
        ]);

        // create an asset
        $asset = $this->createAsset();

        // create a collection
        $collection = Collection::create([
            'name' => 'Genre'
        ]);

        // create collection values
        $collectionValues = [
            CollectionValue::create([
                'collection_id' => $collection->id,
                'label' => 'Horror'
            ]),
            CollectionValue::create([
                'collection_id' => $collection->id,
                'label' => 'Thriller'
            ])
        ];

        $collectionValueIDs = json_encode(array_column($collectionValues, 'id'));

        $createData = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $field->id,
            'value' => $collectionValueIDs
        ];

        $response = $this->actingAs($this->user)->post('/field-values', $createData);

        $fieldValue = FieldValue::find($response->json('field.id'));

        $this->assertEquals($collectionValueIDs, $fieldValue->getValue());
    }


    public static function create_or_update_duplication_provider()
    {
        return [
            [false, false],
            [true, true],
        ];
    }


    #[DataProvider('create_or_update_duplication_provider')]
    public function test_create_or_update_duplicates_for_non_new_asset(bool $isValueUpdated, bool $isAssetDuplicated): void
    {
        // note: new assets never duplicate, so we need to test with a Holdover
        $this->createAsset(null, null, AssetTypeEnum::Movie->value, AssetStatus::Holdover);
        $this->createStringField();
        $this->createStringFieldValue();

        $validationService = app(ValidationFieldService::class);
        $cycleAssetManager = app(CycleAssetManager::class);

        [$fieldValue, $error] = FieldValue::createOrUpdate(
            $this->stringField->id,
            $this->asset->id,
            null,
            $this->cycle->id,
            $isValueUpdated ? 'definitely a new value' : $this->stringFieldValue->getValue(),
            $validationService,
            $cycleAssetManager
        );

        if ($isAssetDuplicated) {
            $this->assertNotEquals($this->stringFieldValue->asset_id, $fieldValue->asset_id);
        } else {
            $this->assertEquals($this->stringFieldValue->asset_id, $fieldValue->asset_id);
        }

    }

    public function test_localizable_field_creates_and_updates_correct_language(): void
    {
        $this->createStringField(true);

        // test against multiple languages
        $languages = [
            ['ife_code' => 'FRE', 'bcp_47_code' => 'fr', 'iso_639_2_t_code' => 'fra', 'eng_description' => 'French', 'local_description' => 'Français'],
            ['ife_code' => 'ITA', 'bcp_47_code' => 'it-IT', 'iso_639_2_t_code' => 'ita', 'eng_description' => 'Italian', 'local_description' => 'Italiano'],
        ];
        foreach ($languages as $language) {
            Language::updateOrCreate(
                ['ife_code' => $language['ife_code']],
                $language
            );
        }

        $languageIds = Language::all()->pluck('id')->toArray();
        $localizedFieldValueIds = [];

        foreach ($languageIds as $languageId) {
            $this->createStringFieldValue($languageId);
            $localizedFieldValueIds[] = $this->stringFieldValue->id;

            $createData = [
                'asset_id' => $this->asset->id,
                'cycle_id' => $this->cycle->id,
                'field_id' => $this->stringField->id,
                'language_id' => $languageId,
                'value' => 'new',
            ];

            $response = $this->actingAs($this->user)->post('/field-values', $createData);

            $response->assertCreated();

            $fieldValueId = $response->json('field.id'); // note: a `FieldValue` is returned in `field` for this endpoint

            $this->assertDatabaseHas('field_values', ['id' => $fieldValueId, 'language_id' => $languageId, 'value' => 'new']);

            $updateData = [
                'cycle_id' => $this->cycle->id,
                'language_id' => $languageId,
                'value' => 'updated',
            ];

            $response = $this->actingAs($this->user)->put("/field-values/{$fieldValueId}", $updateData);

            $response->assertAccepted();

            $this->assertDatabaseHas('field_values', ['id' => $fieldValueId, 'language_id' => $languageId, 'value' => 'updated']);
        }

        $this->assertDatabaseCount('field_values', count($languageIds));
    }

}
