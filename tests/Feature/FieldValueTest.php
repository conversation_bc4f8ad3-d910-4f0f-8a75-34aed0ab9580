<?php

namespace Tests\Feature;

use App\Models\Collection;
use App\Models\CollectionValue;
use App\Models\Field;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Enums\FieldEnum;
use App\Models\FieldValue;

class FieldValueTest extends TestCase
{
    public function setUp() : void
    {
        parent::setUp();
        $this->createStringField();
        $this->createDateField();
        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
    }


    /**
     * A basic feature test example.
     */
    public function test_can_create_asset_field_value_for_title(): void
    {
        $createData = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->stringField->id,
            'value' => 'Anora'
        ];

        $response = $this->actingAs($this->user)->post('/field-values', $createData); 

        $response->assertCreated();
    }

    /**
     * A basic feature test example.
     */
    public function test_can_create_asset_field_value_for_release_date(): void
    {
        $createData = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $this->dateField->id,
            'value' => now()->addMonth()
        ];

        $response = $this->actingAs($this->user)->post('/field-values', $createData); 

        $response->assertCreated();
    }

    public function test_can_add_dropdown_field_collection_item_ids_to_a_field_value(): void 
    {

        // create a dropdown field
        $field = Field::create([
            'name' => 'Genre',
            'field_type' =>  FieldEnum::Dropdown,
            'schema_id' => $this->schema->id
        ]);

        // create an asset
        $asset = $this->createAsset();

        // create a collection
        $collection = Collection::create([
            'name' => 'Genre'
        ]);

        // create collection values
        $collectionValues = [
            CollectionValue::create([
                'collection_id' => $collection->id,
                'label' => 'Horror'
            ]),
            CollectionValue::create([
                'collection_id' => $collection->id,
                'label' => 'Thriller'
            ])
        ];

        $collectionValueIDs = json_encode(array_column($collectionValues, 'id'));

        $createData = [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'field_id' => $field->id,
            'value' => $collectionValueIDs
        ];

        $response = $this->actingAs($this->user)->post('/field-values', $createData); 

        $fieldValue = FieldValue::find($response->json('field.id'));

        $this->assertEquals($collectionValueIDs, $fieldValue->getValue());
    }

}
