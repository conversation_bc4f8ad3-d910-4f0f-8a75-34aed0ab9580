<?php

namespace Tests\Feature;

use App\Models\Airline;
use App\Models\Cycle;
use Mockery\MockInterface;
use Tests\TestCase;

class MatchImageToFieldValueTest extends TestCase
{
    protected MockInterface $assetImageMock;

    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
        $this->createAsset();
    }

    public function test_it_matches_an_uploaded_image_to_a_field_value(): void
    {
        // Given
        $this->createImageField();
        $this->createImageFieldValue();

        // When: when the asset image is created and then marked as uploaded...
        $this->createAssetImage($this->imageFieldValue->value, 'uploading');
        $this->assetImage->status = "uploaded";
        $this->assetImage->save();

        // Then: the asset image should be matched to the field value and the asset ID.
        $this->assetImage->refresh();
        $this->assertEquals($this->assetImage->field_id, $this->imageField->id);
        $this->assertEquals($this->assetImage->asset_id, $this->asset->id);
        $this->assertEquals("matched", $this->assetImage->status);
    }

    public function test_it_matches_a_field_value_file_name_to_an_unmatched_image(): void
    {
        // Given
        $this->createAssetImage();

        // When: when the asset image is created and then marked as uploaded...
        $this->createImageField();
        $this->createImageFieldValue($this->assetImage->file_name);

        // Then: the asset image should be matched to the field value and the asset ID.
        $this->assetImage->refresh();
        $this->assertEquals($this->assetImage->field_id, $this->imageField->id);
        $this->assertEquals($this->assetImage->asset_id, $this->asset->id);
        $this->assertEquals("matched", $this->assetImage->status);
    }

    public function test_it_does_not_match_an_uploaded_image_to_a_field_value_in_another_airline(): void
    {
        // Given
        $this->createImageField();
        $this->createImageFieldValue();

        // When: when the asset image is created and then marked as uploaded...
        $this->createAssetImage($this->imageFieldValue->value);
        $this->assetImage->airline_id = Airline::factory()->create()->id;
        $this->organization->airlines()->attach($this->assetImage->airline_id);
        $this->assetImage->status = "uploaded";
        $this->assetImage->save();

        // Then: the asset image should still be unmatched.
        $this->assetImage->refresh();
        $this->assertEquals(null, $this->assetImage->field_id);
        $this->assertEquals(null, $this->assetImage->asset_id);
        $this->assertEquals("uploaded", $this->assetImage->status);
    }

    public function test_it_does_not_match_a_field_value_to_an_image_in_another_cycle(): void
    {
        // Given
        $this->createAssetImage();

        // When: when the asset image is created and then marked as uploaded...
        $this->asset->cycles()->detach($this->cycle->id);
        $newCycle = Cycle::factory()->create(['airline_id' => $this->airline->id, 'schema_id' => $this->cycle->schema_id])->id;
        $this->asset->cycles()->attach($newCycle, ['status' => 'new']);
        $this->createImageField();
        $this->createImageFieldValue($this->assetImage->file_name);


        // Then: field value should not be matched to the asset image.
        $this->assetImage->refresh();
        $this->assertEquals(null, $this->assetImage->field_id);
        $this->assertEquals(null, $this->assetImage->asset_id);
        $this->assertEquals("pending", $this->assetImage->status);
    }

}
