<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Enums\AssetStatus;
use App\Models\Airline;
use App\Models\Cycle;
use PHPUnit\Framework\Attributes\DataProvider;

class CycleTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
    }

    public function test_cycle_can_be_created()
    {
        $createData = [
            'start_date' => now(),
            'end_date' => now()->addMonths(2),
            'airline_id' => $this->airline->id
        ];
        $response = $this->actingAs($this->user)->postJson('/cycles', $createData);

        $response->assertStatus(201);
        $this->assertDatabaseHas('cycles', $createData);
    }

    public function test_cycle_cannot_be_created_by_unprivileged_user()
    {
        $createData = [
            'start_date' => now(),
            'end_date' => now()->addMonths(2),
            'airline_id' => $this->airline->id
        ];
        $response = $this->actingAs($this->cspEditorUser)->postJson('/cycles', $createData);

        $response->assertForbidden();
        $this->assertDatabaseMissing('cycles', $createData);
    }

    public function test_cycle_can_be_updated()
    {
        $cycle = $this->cycle;
        $newEndDate = now()->addYear();

        $response = $this->actingAs($this->user)->putJson("/cycles/{$cycle->id}", [
            'end_date' => $newEndDate
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('cycles', [
            'id' => $cycle->id,
            'end_date' => $newEndDate->toDateTimeString()
        ]);
    }

    public function test_cycle_cannot_be_updated_by_unprivileged_user()
    {
        $cycle = $this->cycle;
        $newEndDate = now()->addYear();

        $response = $this->actingAs($this->cspEditorUser)->putJson("/cycles/{$cycle->id}", [
            'end_date' => $newEndDate
        ]);

        $response->assertForbidden();
        $this->assertDatabaseMissing('cycles', [
            'id' => $cycle->id,
            'end_date' => $newEndDate->toDateTimeString()
        ]);
    }

    public function test_cycle_can_be_deleted()
    {
        $cycle = $this->cycle;

        $response = $this->actingAs($this->user)->deleteJson("/cycles/{$cycle->id}");

        $response->assertStatus(204);
        $this->assertDatabaseMissing('cycles', [
            'id' => $cycle->id,
            'deleted_at' => null
        ]);
    }

    public function test_cycle_cannot_be_deleted_by_unprivileged_user()
    {
        $cycle = $this->cycle;

        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/cycles/{$cycle->id}");

        $response->assertForbidden();
        $this->assertDatabaseHas('cycles', [
            'id' => $cycle->id,
            'deleted_at' => null
        ]);
    }

    public function test_cycle_handles_holdover_status()
    {
        $this->attachAssetToCycle(now(), now()->addYears(3), AssetStatus::New);

        $newCycle = $this->createNewCycleForAirline();

        $this->assertDatabaseHas('asset_cycle', [
            'asset_id' => $this->asset->id,
            'cycle_id' => $newCycle['id'],
            'status' => AssetStatus::Holdover->value
        ]);
    }

    public function test_cycle_handles_expiring_status()
    {
        $this->attachAssetToCycle(now(), now()->addDays(40), AssetStatus::New);

        $newCycle = $this->createNewCycleForAirline();

        $this->assertDatabaseHas('asset_cycle', [
            'asset_id' => $this->asset->id,
            'cycle_id' => $newCycle['id'],
            'status' => AssetStatus::Expiring->value
        ]);
    }

    public function test_cycle_does_not_move_expired_asset()
    {
        $this->attachAssetToCycle(now(), now()->addDays(10), AssetStatus::New);

        $newCycle = $this->createNewCycleForAirline();

        $this->assertDatabaseMissing('asset_cycle', [
            'asset_id' => $this->asset->id,
            'cycle_id' => $newCycle['id'],
        ]);
    }

    public function test_cycle_autofill_succeeds()
    {
        $cycle = $this->cycle;

        $response = $this->actingAs($this->user)->putJson("/cycles/{$cycle->id}/auto-fill");

        $response->assertStatus(200);
    }

    public function test_cycle_cannot_autofill_for_unprivileged_user()
    {
        $cycle = $this->cycle;

        $response = $this->actingAs($this->readOnlyUser)->putJson("/cycles/{$cycle->id}/auto-fill");

        $response->assertForbidden();
    }

    private function attachAssetToCycle($start, $end, $status)
    {
        $this->createAsset($start, $end);
        $this->cycle->assets()->attach($this->asset->id, ['status' => $status->value]);

        $this->assertDatabaseHas('asset_cycle', [
            'asset_id' => $this->asset->id,
            'cycle_id' => $this->cycle->id,
            'status' => $status->value
        ]);
    }

    private function createNewCycleForAirline()
    {
        $response = $this->actingAs($this->user)->postJson("/cycles", [
            'airline_id' => $this->airline->id,
        ]);

        $response->assertCreated();

        return $response->json('cycle');
    }

    public static function user_type_cycle_provider()
    {
        return [
            ['admin', 2],
            ['cspEditor', 1],
        ];
    }

    #[DataProvider('user_type_cycle_provider')]
    public function test_user_can_search_cycles_for_all_airlines(string $type, int $num_results)
    {
        $this->createOrganization2();
        $this->actingAs($type === 'admin' ? $this->user : $this->cspEditorUser);

        $airline2 = Airline::factory()->create(['name' => 'Another Org\'s Airline']);
        $this->organization2->airlines()->attach($airline2);

        Cycle::factory()->create([
            'airline_id' => $airline2->id,
            'schema_id' => $this->schema->id
        ]);

        // test the index endpoint
        $response = $this->getJson('/cycles');

        $response->assertJsonCount($num_results);

        // test the id endpoint for airline in same org
        $response = $this->getJson("/cycles/{$this->airline->id}");
        $response->assertOk();

        // test the id endpoint for airline in different org
        $response = $this->getJson("/cycles/{$airline2->id}");

        if ($type === 'admin') {
            $response->assertOk();
        } else {
            $response->assertForbidden();
        }
    }
}
