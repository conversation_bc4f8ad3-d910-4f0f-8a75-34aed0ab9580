<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Airline;
use App\Models\Cycle;
use App\Models\Schema;

class SchemaControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->createAirline();
        $this->createCycle();
    }

    public function test_can_read_schemas(): void
    {
        $response = $this->actingAs($this->user)->getJson('/schemas');
        $response->assertOk();

        $response->assertJsonCount(1);

        $response = $this->actingAs($this->user)->getJson("/schemas/{$this->schema->id}");
        $response->assertOk();
    }

    public function test_limited_user_can_read_schemas_for_accessible_airlines(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->getJson("/schemas/{$this->schema->id}");
        $response->assertOk();
    }

    public function test_limited_user_cannot_read_schemas_for_inaccessible_airlines(): void
    {
        $this->createOrganization2();

        $airline2 = Airline::factory()->create(['name' => 'Another Org\'s Airline']);
        $this->organization2->airlines()->attach($airline2);

        $schema2 = Schema::factory()->create([
            'is_root' => true,
            'name' => 'Some Other Schema',
            'version_number' => 1,
        ]);

        Cycle::factory()->create([
            'airline_id' => $airline2->id,
            'schema_id' => $schema2->id
        ]);

        // ensure only the default schema is returned
        $response = $this->actingAs($this->cspEditorUser)->getJson('/schemas');
        $response->assertOk();
        $response->assertJsonCount(1);
        $response->assertJsonFragment(['name' => $this->schema->name]);

        // ensure "other" schema cannot be requested
        $response = $this->actingAs($this->cspEditorUser)->getJson("/schemas/{$schema2->id}");
        $response->assertForbidden();
    }

    public function test_can_create_schema(): void
    {
        $createData = [
            'schemaID' => $this->schema->id,
        ];

        $response = $this->actingAs($this->user)->postJson('/schemas', $createData);
        $response->assertOk();
    }

    public function test_cannot_create_schema_by_unprivileged_user(): void
    {
        $createData = [
            'schemaID' => $this->schema->id,
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/schemas', $createData);
        $response->assertForbidden();
    }
}
