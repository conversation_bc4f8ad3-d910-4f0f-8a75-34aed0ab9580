<?php

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;
use App\Models\Airline;
use App\Models\Collection;
use App\Models\CollectionValue;
use App\Models\Cycle;
use App\Models\Schema;

class CollectionValueTest extends TestCase
{
    protected $org2Collection;
    protected $org2CSPEditableCollection;

    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCycle();
        $this->createCollection();
        $this->createCollectionItem();
        $this->createCSPEditableCollection();
        $this->createCSPEditableCollectionItem();

        $this->createOrganization2();

        $org2_airline = Airline::factory()->create(['name' => 'Another Org\'s Airline']);
        $this->organization2->airlines()->attach($org2_airline);


        $schema2 = Schema::factory()->create([
            'is_root' => true,
            'name' => 'Some Other Schema',
            'version_number' => 1,
        ]);

        Cycle::factory()->create([
            'airline_id' => $org2_airline->id,
            'schema_id' => $schema2->id
        ]);

        $this->org2Collection = Collection::create([
            'name' => 'Test Genres',
            'airline_id' => $org2_airline->id,
            'organization_id' => $this->organization2->id,
            'schema_id' => $schema2->id,
            'viasat_only' => false,
        ]);

        CollectionValue::create([
            'collection_id' => $this->org2Collection->id,
            'label' => 'some label',
        ]);

        $this->org2CSPEditableCollection = Collection::create([
            'name' => 'Test Genres',
            'airline_id' => $org2_airline->id,
            'organization_id' => $this->organization2->id,
            'schema_id' => $schema2->id,
            'viasat_only' => false,
        ]);

        CollectionValue::create([
            'collection_id' => $this->org2CSPEditableCollection->id,
            'label' => 'some label',
        ]);
    }

    public static function collection_type_provider()
    {
        return [
            ['admin', 'viasat_only', 'same_org', true],
            ['admin', 'editable', 'same_org', true],
            ['csp', 'viasat_only', 'same_org', false],
            ['csp', 'editable', 'same_org', true],
            ['admin', 'viasat_only', 'diff_org', true],
            ['admin', 'editable', 'diff_org', true],
            ['csp', 'viasat_only', 'diff_org', false],
            ['csp', 'editable', 'diff_org', false],
        ];
    }

    private function getCollectionIdForProviderValues(string $collection_type, string $org_type)
    {
        return match ($org_type) {
            'same_org' => $collection_type === 'viasat_only' ? $this->collection->id : $this->cspEditableCollection->id,
            default => $collection_type === 'viasat_only' ? $this->org2Collection->id : $this->org2CSPEditableCollection->id,
        };
    }

    #[DataProvider('collection_type_provider')]
    public function test_create_collection_value_by_user(string $user_type, string $collection_type, string $org_type, bool $is_allowed): void
    {
        $user = $user_type === 'admin' ? $this->user : $this->cspEditorUser;
        $collection_id = $this->getCollectionIdForProviderValues($collection_type, $org_type);

        $createData = [
            'collection_id' => $collection_id,
        ];

        $response = $this->actingAs($user)->postJson('/collection-values', $createData);

        if ($is_allowed) {
            $response->assertCreated();
        } else {
            $response->assertForbidden();
        }
    }

    #[DataProvider('collection_type_provider')]
    public function test_update_collection_value(string $user_type, string $collection_type, string $org_type, bool $is_allowed): void
    {
        $user = $user_type === 'admin' ? $this->user : $this->cspEditorUser;
        $collection_id = $this->getCollectionIdForProviderValues($collection_type, $org_type);

        $updateData = [
            'label' => 'new label',
        ];

        $response = $this->actingAs($user)->putJson("/collection-values/{$collection_id}", $updateData);

        if ($is_allowed) {
            $response->assertOk();
        } else {
            $response->assertForbidden();
        }
    }

    #[DataProvider('collection_type_provider')]
    public function test_delete_collection_value(string $user_type, string $collection_type, string $org_type, bool $is_allowed): void
    {
        $user = $user_type === 'admin' ? $this->user : $this->cspEditorUser;
        $collection_id = $this->getCollectionIdForProviderValues($collection_type, $org_type);

        $response = $this->actingAs($user)->deleteJson("/collection-values/{$collection_id}");

        if ($is_allowed) {
            $response->assertOk();
        } else {
            $response->assertForbidden();
        }
    }
}
