<?php

use Tests\TestCase;

class CollectionValueTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->createAirline();
        $this->createCollection();
        $this->createCollectionItem();
    }

    public function test_can_create_collection_value(): void
    {
        $createData = [
            'collection_id' => $this->collection->id,
        ];

        $response = $this->actingAs($this->user)->postJson('/collection-values', $createData);

        $response->assertCreated();
    }

    public function test_cannot_create_collection_value_by_unprivileged_user(): void
    {
        $createData = [
            'collection_id' => $this->collection->id,
        ];

        $response = $this->actingAs($this->cspEditorUser)->postJson('/collection-values', $createData);

        $response->assertForbidden();
    }

    public function test_can_update_collection_value(): void
    {
        $updateData = [
            'label' => 'new label',
        ];

        $response = $this->actingAs($this->user)->putJson("/collection-values/{$this->collection->id}", $updateData);

        $response->assertOk();
    }

    public function test_cannot_update_collection_value_by_unprivileged_user(): void
    {
        $updateData = [
            'label' => 'new label',
        ];

        $response = $this->actingAs($this->cspEditorUser)->putJson("/collection-values/{$this->collection->id}", $updateData);

        $response->assertForbidden();
    }

    public function test_can_delete_collection_value(): void
    {
        $response = $this->actingAs($this->user)->deleteJson("/collection-values/{$this->collection->id}");

        $response->assertOk();
    }

    public function test_cannot_delete_collection_value_by_unprivileged_user(): void
    {
        $response = $this->actingAs($this->cspEditorUser)->deleteJson("/collection-values/{$this->collection->id}");

        $response->assertForbidden();
    }
}
